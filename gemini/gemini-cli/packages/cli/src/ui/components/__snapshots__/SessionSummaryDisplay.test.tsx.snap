// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`<SessionSummaryDisplay /> > correctly sums and displays stats from multiple models 1`] = `
"╭─────────────────────────────────────╮
│                                     │
│  Agent powering down. Goodbye!      │
│                                     │
│                                     │
│  Cumulative Stats (15 API calls)    │
│                                     │
│  Input Tokens                1,500  │
│  Output Tokens               3,000  │
│  Tool Use Tokens               220  │
│  Thoughts Tokens               350  │
│  Cached Tokens         600 (12.0%)  │
│  ─────────────────────────────────  │
│  Total Tokens                5,000  │
│                                     │
│  Total duration (API)        1m 2s  │
│  Total duration (Tools)         0s  │
│  Total duration (wall)  1h 23m 45s  │
│                                     │
╰─────────────────────────────────────╯"
`;

exports[`<SessionSummaryDisplay /> > renders zero state correctly 1`] = `
"╭─────────────────────────────────────╮
│                                     │
│  Agent powering down. Goodbye!      │
│                                     │
│                                     │
│  Cumulative Stats (0 API calls)     │
│                                     │
│  Input Tokens                    0  │
│  Output Tokens                   0  │
│  Thoughts Tokens                 0  │
│  ─────────────────────────────────  │
│  Total Tokens                    0  │
│                                     │
│  Total duration (API)           0s  │
│  Total duration (Tools)         0s  │
│  Total duration (wall)  1h 23m 45s  │
│                                     │
╰─────────────────────────────────────╯"
`;
