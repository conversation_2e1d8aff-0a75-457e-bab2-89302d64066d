{"info": {"name": "ONDC Seller App - Verified API Collection", "description": "Comprehensive API collection for ONDC Seller App with verified endpoints", "version": "2.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:9000", "type": "string"}, {"key": "publishableApiKey", "value": "pk_3d67561dece2d466dc798c18c1f80523f84f3b2f01316e0bf915e51f3a59b98b", "type": "string"}, {"key": "adminToken", "value": "", "type": "string"}, {"key": "tenantId", "value": "tenant-electronics-001", "type": "string"}, {"key": "cartId", "value": "", "type": "string"}], "item": [{"name": "🔐 Authentication", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('adminToken', response.token);", "    pm.test('Admin login successful', () => {", "        pm.expect(response.token).to.exist;", "    });", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"supersecret\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/user/emailpass", "host": ["{{baseUrl}}"], "path": ["auth", "user", "emailpass"]}}}]}, {"name": "🏪 Store APIs", "item": [{"name": "📦 Products", "item": [{"name": "Get All Products", "request": {"method": "GET", "header": [{"key": "x-publishable-api-key", "value": "{{publishable<PERSON>pi<PERSON>ey}}"}, {"key": "x-tenant-id", "value": "{{tenantId}}"}], "url": {"raw": "{{baseUrl}}/store/products?limit=10", "host": ["{{baseUrl}}"], "path": ["store", "products"], "query": [{"key": "limit", "value": "10"}]}}}, {"name": "Get Single Product", "request": {"method": "GET", "header": [{"key": "x-publishable-api-key", "value": "{{publishable<PERSON>pi<PERSON>ey}}"}, {"key": "x-tenant-id", "value": "{{tenantId}}"}], "url": {"raw": "{{baseUrl}}/store/products/prod_01JZ7GFAM80F6NXHJB7EJ698GA", "host": ["{{baseUrl}}"], "path": ["store", "products", "prod_01JZ7GFAM80F6NXHJB7EJ698GA"]}}}]}, {"name": "📂 Categories", "item": [{"name": "Get All Categories", "request": {"method": "GET", "header": [{"key": "x-publishable-api-key", "value": "{{publishable<PERSON>pi<PERSON>ey}}"}, {"key": "x-tenant-id", "value": "{{tenantId}}"}], "url": {"raw": "{{baseUrl}}/store/product-categories", "host": ["{{baseUrl}}"], "path": ["store", "product-categories"]}}}]}, {"name": "🎯 Collections", "item": [{"name": "Get All Collections", "request": {"method": "GET", "header": [{"key": "x-publishable-api-key", "value": "{{publishable<PERSON>pi<PERSON>ey}}"}, {"key": "x-tenant-id", "value": "{{tenantId}}"}], "url": {"raw": "{{baseUrl}}/store/collections", "host": ["{{baseUrl}}"], "path": ["store", "collections"]}}}]}, {"name": "🛒 Cart", "item": [{"name": "Create <PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('cartId', response.cart.id);", "    pm.test('<PERSON><PERSON> created successfully', () => {", "        pm.expect(response.cart.id).to.exist;", "    });", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-publishable-api-key", "value": "{{publishable<PERSON>pi<PERSON>ey}}"}, {"key": "x-tenant-id", "value": "{{tenantId}}"}], "body": {"mode": "raw", "raw": "{\n  \"region_id\": \"reg_01JZ7RPY072WGWKTJ6Q2YE46V7\"\n}"}, "url": {"raw": "{{baseUrl}}/store/carts", "host": ["{{baseUrl}}"], "path": ["store", "carts"]}}}, {"name": "Add Item to Cart", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-publishable-api-key", "value": "{{publishable<PERSON>pi<PERSON>ey}}"}, {"key": "x-tenant-id", "value": "{{tenantId}}"}], "body": {"mode": "raw", "raw": "{\n  \"variant_id\": \"variant_01JZ7GFASGR7D4G8BEYG6CE57X\",\n  \"quantity\": 2\n}"}, "url": {"raw": "{{baseUrl}}/store/carts/{{cartId}}/line-items", "host": ["{{baseUrl}}"], "path": ["store", "carts", "{{cartId}}", "line-items"]}}}]}, {"name": "🌍 Regions", "item": [{"name": "Get All Regions", "request": {"method": "GET", "header": [{"key": "x-publishable-api-key", "value": "{{publishable<PERSON>pi<PERSON>ey}}"}, {"key": "x-tenant-id", "value": "{{tenantId}}"}], "url": {"raw": "{{baseUrl}}/store/regions", "host": ["{{baseUrl}}"], "path": ["store", "regions"]}}}]}]}, {"name": "👨‍💼 Admin APIs", "item": [{"name": "📦 Product Management", "item": [{"name": "Get All Products", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}, {"key": "x-tenant-id", "value": "{{tenantId}}"}], "url": {"raw": "{{baseUrl}}/admin/products?limit=10", "host": ["{{baseUrl}}"], "path": ["admin", "products"], "query": [{"key": "limit", "value": "10"}]}}}, {"name": "Get Product Categories", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}, {"key": "x-tenant-id", "value": "{{tenantId}}"}], "url": {"raw": "{{baseUrl}}/admin/product-categories", "host": ["{{baseUrl}}"], "path": ["admin", "product-categories"]}}}, {"name": "Get Product Types", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}, {"key": "x-tenant-id", "value": "{{tenantId}}"}], "url": {"raw": "{{baseUrl}}/admin/product-types", "host": ["{{baseUrl}}"], "path": ["admin", "product-types"]}}}, {"name": "Get Product Tags", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}, {"key": "x-tenant-id", "value": "{{tenantId}}"}], "url": {"raw": "{{baseUrl}}/admin/product-tags", "host": ["{{baseUrl}}"], "path": ["admin", "product-tags"]}}}]}, {"name": "📋 Order Management", "item": [{"name": "Get All Orders", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}, {"key": "x-tenant-id", "value": "{{tenantId}}"}], "url": {"raw": "{{baseUrl}}/admin/orders?limit=10", "host": ["{{baseUrl}}"], "path": ["admin", "orders"], "query": [{"key": "limit", "value": "10"}]}}}]}, {"name": "👥 Customer Management", "item": [{"name": "Get All Customers", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}, {"key": "x-tenant-id", "value": "{{tenantId}}"}], "url": {"raw": "{{baseUrl}}/admin/customers?limit=10", "host": ["{{baseUrl}}"], "path": ["admin", "customers"], "query": [{"key": "limit", "value": "10"}]}}}]}, {"name": "⚙️ System Configuration", "item": [{"name": "Get API Keys", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}, {"key": "x-tenant-id", "value": "{{tenantId}}"}], "url": {"raw": "{{baseUrl}}/admin/api-keys", "host": ["{{baseUrl}}"], "path": ["admin", "api-keys"]}}}, {"name": "Get Sales Channels", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}, {"key": "x-tenant-id", "value": "{{tenantId}}"}], "url": {"raw": "{{baseUrl}}/admin/sales-channels", "host": ["{{baseUrl}}"], "path": ["admin", "sales-channels"]}}}, {"name": "Get Inventory Items", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}"}, {"key": "x-tenant-id", "value": "{{tenantId}}"}], "url": {"raw": "{{baseUrl}}/admin/inventory-items?limit=10", "host": ["{{baseUrl}}"], "path": ["admin", "inventory-items"], "query": [{"key": "limit", "value": "10"}]}}}]}]}, {"name": "🔧 System", "item": [{"name": "Health Check", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}}}]}]}