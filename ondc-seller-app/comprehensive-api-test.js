#!/usr/bin/env node

/**
 * Comprehensive API Endpoint Testing Script
 * Tests all critical store and admin endpoints for the ONDC Seller App
 */

const axios = require('axios');
const fs = require('fs');

const API_BASE = 'http://localhost:9000';
const TENANT_ID = 'tenant-electronics-001';

// Test configuration
const config = {
  baseURL: API_BASE,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'x-tenant-id': TENANT_ID,
    'Accept': 'application/json'
  }
};

// Test results storage
const testResults = {
  store: {},
  admin: {},
  summary: {
    total: 0,
    passed: 0,
    failed: 0,
    errors: []
  }
};

// Helper function to make API requests
async function testEndpoint(name, method, endpoint, data = null, headers = {}) {
  try {
    const requestConfig = {
      method,
      url: endpoint,
      ...config,
      headers: { ...config.headers, ...headers }
    };
    
    if (data) {
      requestConfig.data = data;
    }
    
    console.log(`\n🧪 Testing ${name}...`);
    console.log(`   ${method.toUpperCase()} ${endpoint}`);
    
    const response = await axios(requestConfig);
    
    const result = {
      name,
      method,
      endpoint,
      status: response.status,
      success: response.status >= 200 && response.status < 300,
      data: response.data,
      headers: response.headers,
      timestamp: new Date().toISOString()
    };
    
    console.log(`   ✅ Status: ${response.status}`);
    console.log(`   📊 Response: ${JSON.stringify(response.data).substring(0, 100)}...`);
    
    testResults.summary.total++;
    testResults.summary.passed++;
    
    return result;
    
  } catch (error) {
    const result = {
      name,
      method,
      endpoint,
      status: error.response?.status || 0,
      success: false,
      error: error.message,
      errorData: error.response?.data,
      timestamp: new Date().toISOString()
    };
    
    console.log(`   ❌ Error: ${error.message}`);
    if (error.response?.data) {
      console.log(`   📋 Error Data: ${JSON.stringify(error.response.data).substring(0, 100)}...`);
    }
    
    testResults.summary.total++;
    testResults.summary.failed++;
    testResults.summary.errors.push(`${name}: ${error.message}`);
    
    return result;
  }
}

// Store API Tests
async function testStoreAPIs() {
  console.log('\n🏪 TESTING STORE APIs');
  console.log('=' .repeat(50));
  
  const storeTests = [];
  
  // Test 1: Health Check
  storeTests.push(await testEndpoint(
    'Health Check',
    'GET',
    '/health'
  ));
  
  // Test 2: Store Info
  storeTests.push(await testEndpoint(
    'Store Info',
    'GET',
    '/store/test-info'
  ));
  
  // Test 3: Products (without API key first)
  storeTests.push(await testEndpoint(
    'Products (No API Key)',
    'GET',
    '/store/products'
  ));
  
  // Test 4: Products with potential API key
  storeTests.push(await testEndpoint(
    'Products (With Test API Key)',
    'GET',
    '/store/products',
    null,
    { 'x-publishable-api-key': 'pk_test_123456789' }
  ));
  
  // Test 5: Categories
  storeTests.push(await testEndpoint(
    'Product Categories',
    'GET',
    '/store/product-categories'
  ));
  
  // Test 6: Collections
  storeTests.push(await testEndpoint(
    'Collections',
    'GET',
    '/store/collections'
  ));
  
  // Test 7: Cart Creation
  storeTests.push(await testEndpoint(
    'Create Cart',
    'POST',
    '/store/carts',
    {
      region_id: 'reg_01',
      currency_code: 'USD'
    }
  ));
  
  testResults.store = storeTests;
}

// Admin API Tests
async function testAdminAPIs() {
  console.log('\n👨‍💼 TESTING ADMIN APIs');
  console.log('=' .repeat(50));
  
  const adminTests = [];
  
  // Test 1: Admin Auth (without credentials)
  adminTests.push(await testEndpoint(
    'Admin Auth (No Credentials)',
    'POST',
    '/auth/user/emailpass',
    {
      email: '<EMAIL>',
      password: 'supersecret'
    }
  ));
  
  // Test 2: Admin Products
  adminTests.push(await testEndpoint(
    'Admin Products',
    'GET',
    '/admin/products'
  ));
  
  // Test 3: Admin Orders
  adminTests.push(await testEndpoint(
    'Admin Orders',
    'GET',
    '/admin/orders'
  ));
  
  // Test 4: Admin Customers
  adminTests.push(await testEndpoint(
    'Admin Customers',
    'GET',
    '/admin/customers'
  ));
  
  // Test 5: Tenant Info
  adminTests.push(await testEndpoint(
    'Tenant Info',
    'GET',
    '/admin/tenant'
  ));
  
  testResults.admin = adminTests;
}

// Custom API Tests (for the old backend)
async function testCustomAPIs() {
  console.log('\n🔧 TESTING CUSTOM APIs');
  console.log('=' .repeat(50));
  
  const customTests = [];
  
  // Test custom endpoints that might be available
  customTests.push(await testEndpoint(
    'Custom Products API',
    'GET',
    '/api/products'
  ));
  
  customTests.push(await testEndpoint(
    'Custom Orders API',
    'GET',
    '/api/orders'
  ));
  
  customTests.push(await testEndpoint(
    'Custom Customers API',
    'GET',
    '/api/customers'
  ));
  
  testResults.custom = customTests;
}

// Generate test report
function generateReport() {
  console.log('\n📊 TEST SUMMARY');
  console.log('=' .repeat(50));
  console.log(`Total Tests: ${testResults.summary.total}`);
  console.log(`Passed: ${testResults.summary.passed}`);
  console.log(`Failed: ${testResults.summary.failed}`);
  console.log(`Success Rate: ${((testResults.summary.passed / testResults.summary.total) * 100).toFixed(1)}%`);
  
  if (testResults.summary.errors.length > 0) {
    console.log('\n❌ ERRORS:');
    testResults.summary.errors.forEach(error => console.log(`   - ${error}`));
  }
  
  // Save detailed results to file
  const reportFile = 'api-test-results.json';
  fs.writeFileSync(reportFile, JSON.stringify(testResults, null, 2));
  console.log(`\n📄 Detailed results saved to: ${reportFile}`);
}

// Main test runner
async function runAllTests() {
  console.log('🚀 COMPREHENSIVE API ENDPOINT TESTING');
  console.log('=' .repeat(50));
  console.log(`Base URL: ${API_BASE}`);
  console.log(`Tenant ID: ${TENANT_ID}`);
  console.log(`Timestamp: ${new Date().toISOString()}`);
  
  try {
    await testStoreAPIs();
    await testAdminAPIs();
    await testCustomAPIs();
    
    generateReport();
    
  } catch (error) {
    console.error('❌ Test execution failed:', error);
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests()
    .then(() => {
      console.log('\n✅ All tests completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Test suite failed:', error);
      process.exit(1);
    });
}

module.exports = { runAllTests, testEndpoint, testResults };
