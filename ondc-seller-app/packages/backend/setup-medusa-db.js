/**
 * <PERSON><PERSON><PERSON> to set up Medusa database schema and create admin user
 */

const { Client } = require('pg');
const bcrypt = require('bcryptjs');

async function setupMedusaDatabase() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    database: 'medusa_store',
    user: 'strapi',
    password: 'strapi_password',
  });

  try {
    await client.connect();
    console.log('✅ Connected to PostgreSQL');

    // Create basic Medusa tables
    console.log('🔧 Creating Medusa database schema...');

    // Create users table for admin authentication
    await client.query(`
      CREATE TABLE IF NOT EXISTS "user" (
        id VARCHAR PRIMARY KEY DEFAULT gen_random_uuid(),
        email VARCHAR UNIQUE NOT NULL,
        password_hash VARCHAR,
        first_name VARCHAR,
        last_name VARCHAR,
        role VARCHAR DEFAULT 'admin',
        api_token VARCHAR,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW(),
        deleted_at TIMESTAMP
      );
    `);

    // Create store table
    await client.query(`
      CREATE TABLE IF NOT EXISTS "store" (
        id VARCHAR PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR DEFAULT 'Medusa Store',
        default_currency_code VARCHAR DEFAULT 'USD',
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      );
    `);

    // Create regions table
    await client.query(`
      CREATE TABLE IF NOT EXISTS "region" (
        id VARCHAR PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR NOT NULL,
        currency_code VARCHAR NOT NULL,
        tax_rate REAL DEFAULT 0,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      );
    `);

    // Create customers table
    await client.query(`
      CREATE TABLE IF NOT EXISTS "customer" (
        id VARCHAR PRIMARY KEY DEFAULT gen_random_uuid(),
        email VARCHAR UNIQUE NOT NULL,
        first_name VARCHAR,
        last_name VARCHAR,
        password_hash VARCHAR,
        phone VARCHAR,
        has_account BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW(),
        deleted_at TIMESTAMP
      );
    `);

    console.log('✅ Database schema created');

    // Create default store if it doesn't exist
    const storeCheck = await client.query('SELECT * FROM "store" LIMIT 1');
    if (storeCheck.rows.length === 0) {
      await client.query(`
        INSERT INTO "store" (name, default_currency_code)
        VALUES ('ONDC Seller Store', 'INR')
      `);
      console.log('✅ Default store created');
    }

    // Create default region if it doesn't exist
    const regionCheck = await client.query('SELECT * FROM "region" LIMIT 1');
    if (regionCheck.rows.length === 0) {
      await client.query(`
        INSERT INTO "region" (name, currency_code, tax_rate)
        VALUES ('India', 'INR', 0.18)
      `);
      console.log('✅ Default region created');
    }

    // Create admin user
    const userCheck = await client.query('SELECT * FROM "user" WHERE email = $1', ['<EMAIL>']);
    
    if (userCheck.rows.length === 0) {
      console.log('👤 Creating admin user...');
      
      // Hash the password properly
      const hashedPassword = await bcrypt.hash('supersecret', 12);
      
      const result = await client.query(`
        INSERT INTO "user" (email, password_hash, first_name, last_name, role)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING *
      `, ['<EMAIL>', hashedPassword, 'Admin', 'User', 'admin']);
      
      console.log('✅ Admin user created:', {
        id: result.rows[0].id,
        email: result.rows[0].email,
        name: `${result.rows[0].first_name} ${result.rows[0].last_name}`,
        role: result.rows[0].role
      });
    } else {
      console.log('ℹ️  Admin user already exists');
    }

    // Create additional demo users
    const demoUsers = [
      { email: '<EMAIL>', password: 'demo', firstName: 'Demo', lastName: 'User' },
      { email: '<EMAIL>', password: '123456', firstName: 'ONDC', lastName: 'Admin' },
      { email: '<EMAIL>', password: '123456', firstName: 'ONDC', lastName: 'Seller' }
    ];

    for (const demoUser of demoUsers) {
      const existingUser = await client.query('SELECT * FROM "user" WHERE email = $1', [demoUser.email]);
      
      if (existingUser.rows.length === 0) {
        const hashedPassword = await bcrypt.hash(demoUser.password, 12);
        
        await client.query(`
          INSERT INTO "user" (email, password_hash, first_name, last_name, role)
          VALUES ($1, $2, $3, $4, $5)
        `, [demoUser.email, hashedPassword, demoUser.firstName, demoUser.lastName, 'admin']);
        
        console.log(`✅ Demo user created: ${demoUser.email}`);
      }
    }

    console.log('\n🎉 Medusa database setup completed successfully!');
    console.log('\n📋 Available login credentials:');
    console.log('   • <EMAIL> / supersecret');
    console.log('   • <EMAIL> / demo');
    console.log('   • <EMAIL> / 123456');
    console.log('   • <EMAIL> / 123456');
    console.log('\n🌐 Admin Panel: http://localhost:9000/app');

  } catch (error) {
    console.error('❌ Error setting up database:', error);
    throw error;
  } finally {
    await client.end();
  }
}

setupMedusaDatabase().catch(console.error);
