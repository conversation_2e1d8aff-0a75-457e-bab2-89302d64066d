/**
 * <PERSON><PERSON><PERSON> to create an admin user for Medusa
 */

const { Client } = require('pg');

async function createAdminUser() {
  const client = new Client({
    host: 'localhost',
    port: 15432,
    database: 'medusa-store',
    user: 'admin',
    password: 'admin',
  });

  try {
    await client.connect();
    console.log('Connected to PostgreSQL');

    // Check if users table exists
    const tableCheck = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'user'
      );
    `);

    if (!tableCheck.rows[0].exists) {
      console.log('Users table does not exist. Creating basic table...');
      
      // Create a basic users table
      await client.query(`
        CREATE TABLE IF NOT EXISTS "user" (
          id VARCHAR PRIMARY KEY DEFAULT gen_random_uuid(),
          email VARCHAR UNIQUE NOT NULL,
          password_hash VARCHAR,
          first_name <PERSON><PERSON><PERSON><PERSON>,
          last_name <PERSON><PERSON><PERSON><PERSON>,
          role VA<PERSON>HAR DEFAULT 'admin',
          created_at TIMESTAMP DEFAULT NOW(),
          updated_at TIMESTAMP DEFAULT NOW()
        );
      `);
    }

    // Check if admin user exists
    const userCheck = await client.query('SELECT * FROM "user" WHERE email = $1', ['<EMAIL>']);
    
    if (userCheck.rows.length === 0) {
      console.log('Creating admin user...');
      
      // Create admin user with a simple password hash (for development)
      const result = await client.query(`
        INSERT INTO "user" (email, password_hash, first_name, last_name, role)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING *
      `, ['<EMAIL>', 'supersecret', 'Admin', 'User', 'admin']);
      
      console.log('Admin user created:', result.rows[0]);
    } else {
      console.log('Admin user already exists:', userCheck.rows[0]);
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.end();
  }
}

createAdminUser();
