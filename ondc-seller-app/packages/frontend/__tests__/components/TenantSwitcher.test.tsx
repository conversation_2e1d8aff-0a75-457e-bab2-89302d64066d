import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { useRouter } from 'next/navigation';
import TenantSwitcher from '../../components/tenant/TenantSwitcher';
// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

// Mock tenant data for testing
const mockTenants = [
  {
    id: 'tenant_1',
    name: 'TechStore Pro',
    handle: 'techstore-pro',
    domain: 'techstore.example.com',
    status: 'active',
    plan: 'pro',
    theme: { primary: '#3B82F6', secondary: '#1E40AF' }
  }
];

const mockPush = jest.fn();
const mockReload = jest.fn();

// Mock window.location.reload
Object.defineProperty(window, 'location', {
  value: {
    reload: mockReload,
  },
  writable: true,
});

describe('TenantSwitcher Component', () => {
  beforeEach(() => {
    (useRouter as jest.Mock).mockReturnValue({
      push: mockPush,
    });
    
    // Mock localStorage
    const localStorageMock = {
      getItem: jest.fn(),
      setItem: jest.fn(),
      removeItem: jest.fn(),
      clear: jest.fn(),
    };
    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders current tenant correctly', () => {
    render(<TenantSwitcher currentTenantId="tenant_1" />);
    
    expect(screen.getByText('TechStore Pro')).toBeInTheDocument();
    expect(screen.getByText('@techstore-pro')).toBeInTheDocument();
  });

  it('opens dropdown when clicked', async () => {
    render(<TenantSwitcher currentTenantId="tenant_1" />);
    
    const trigger = screen.getByRole('button');
    fireEvent.click(trigger);
    
    await waitFor(() => {
      expect(screen.getByPlaceholderText('Search tenants...')).toBeInTheDocument();
      expect(screen.getByText('Fashion Hub')).toBeInTheDocument();
    });
  });

  it('filters tenants based on search query', async () => {
    render(<TenantSwitcher currentTenantId="tenant_1" />);
    
    const trigger = screen.getByRole('button');
    fireEvent.click(trigger);
    
    await waitFor(() => {
      const searchInput = screen.getByPlaceholderText('Search tenants...');
      fireEvent.change(searchInput, { target: { value: 'fashion' } });
    });
    
    await waitFor(() => {
      expect(screen.getByText('Fashion Hub')).toBeInTheDocument();
      expect(screen.queryByText('TechStore Pro')).not.toBeInTheDocument();
    });
  });

  it('selects a different tenant', async () => {
    const mockOnTenantChange = jest.fn();
    render(
      <TenantSwitcher 
        currentTenantId="tenant_1" 
        onTenantChange={mockOnTenantChange}
      />
    );
    
    const trigger = screen.getByRole('button');
    fireEvent.click(trigger);
    
    await waitFor(() => {
      const fashionHubOption = screen.getByText('Fashion Hub');
      fireEvent.click(fashionHubOption);
    });
    
    expect(mockOnTenantChange).toHaveBeenCalledWith(
      expect.objectContaining({
        id: 'tenant_2',
        name: 'Fashion Hub',
      })
    );
    expect(localStorage.setItem).toHaveBeenCalledWith('selectedTenantId', 'tenant_2');
    expect(mockReload).toHaveBeenCalled();
  });

  it('shows create tenant option when enabled', async () => {
    render(<TenantSwitcher currentTenantId="tenant_1" showCreateOption={true} />);
    
    const trigger = screen.getByRole('button');
    fireEvent.click(trigger);
    
    await waitFor(() => {
      expect(screen.getByText('Create new tenant')).toBeInTheDocument();
    });
  });

  it('navigates to create tenant page', async () => {
    render(<TenantSwitcher currentTenantId="tenant_1" showCreateOption={true} />);
    
    const trigger = screen.getByRole('button');
    fireEvent.click(trigger);
    
    await waitFor(() => {
      const createOption = screen.getByText('Create new tenant');
      fireEvent.click(createOption);
    });
    
    expect(mockPush).toHaveBeenCalledWith('/admin/settings/tenant/new');
  });

  it('closes dropdown when clicking outside', async () => {
    render(<TenantSwitcher currentTenantId="tenant_1" />);
    
    const trigger = screen.getByRole('button');
    fireEvent.click(trigger);
    
    await waitFor(() => {
      expect(screen.getByPlaceholderText('Search tenants...')).toBeInTheDocument();
    });
    
    // Click outside
    fireEvent.mouseDown(document.body);
    
    await waitFor(() => {
      expect(screen.queryByPlaceholderText('Search tenants...')).not.toBeInTheDocument();
    });
  });

  it('shows tenant status indicators', async () => {
    render(<TenantSwitcher currentTenantId="tenant_1" />);
    
    const trigger = screen.getByRole('button');
    fireEvent.click(trigger);
    
    await waitFor(() => {
      // Check for status indicators (colored dots)
      const statusIndicators = document.querySelectorAll('.bg-green-400');
      expect(statusIndicators.length).toBeGreaterThan(0);
    });
  });

  it('displays subscription plans', async () => {
    render(<TenantSwitcher currentTenantId="tenant_1" />);
    
    const trigger = screen.getByRole('button');
    fireEvent.click(trigger);
    
    await waitFor(() => {
      expect(screen.getByText('@techstore-pro • pro')).toBeInTheDocument();
      expect(screen.getByText('@fashion-hub • enterprise')).toBeInTheDocument();
    });
  });

  it('shows check mark for current tenant', async () => {
    render(<TenantSwitcher currentTenantId="tenant_1" />);
    
    const trigger = screen.getByRole('button');
    fireEvent.click(trigger);
    
    await waitFor(() => {
      // The current tenant should have a check mark
      const checkIcons = document.querySelectorAll('svg');
      expect(checkIcons.length).toBeGreaterThan(0);
    });
  });

  it('handles empty search results', async () => {
    render(<TenantSwitcher currentTenantId="tenant_1" />);
    
    const trigger = screen.getByRole('button');
    fireEvent.click(trigger);
    
    await waitFor(() => {
      const searchInput = screen.getByPlaceholderText('Search tenants...');
      fireEvent.change(searchInput, { target: { value: 'nonexistent' } });
    });
    
    await waitFor(() => {
      expect(screen.getByText('No tenants found')).toBeInTheDocument();
    });
  });

  it('applies custom className', () => {
    const { container } = render(
      <TenantSwitcher currentTenantId="tenant_1" className="custom-class" />
    );
    
    expect(container.firstChild).toHaveClass('custom-class');
  });

  it('shows loading state when no current tenant', () => {
    render(<TenantSwitcher />);
    
    // Should show loading skeleton
    const loadingElement = document.querySelector('.animate-pulse');
    expect(loadingElement).toBeInTheDocument();
  });
});
