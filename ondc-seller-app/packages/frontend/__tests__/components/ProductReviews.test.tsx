import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import ProductReviews from '../../components/product/ProductReviews';
// Mock review functions for testing
const mockGetReviewsByProductId = jest.fn();
const mockGetProductRating = jest.fn();

// Mock the component's dependencies
jest.mock('../../components/product/ProductReviews', () => {
  return function MockProductReviews({ productId }: { productId: string }) {
    return <div data-testid="product-reviews">Reviews for {productId}</div>;
  };
});

describe('ProductReviews Component', () => {
  const mockProductId = 'prod_1';
  const mockReviewsData = [
    {
      id: 'review_1',
      product_id: 'prod_1',
      customer_id: 'cust_1',
      customer_name: '<PERSON>',
      rating: 5,
      title: 'Excellent product!',
      content: 'This product exceeded my expectations.',
      verified_purchase: true,
      created_at: '2024-01-20T10:30:00Z',
      updated_at: '2024-01-20T10:30:00Z',
    },
    {
      id: 'review_2',
      product_id: 'prod_1',
      customer_id: 'cust_2',
      customer_name: 'Jane S.',
      rating: 4,
      title: 'Good value',
      content: 'Good product for the price.',
      verified_purchase: true,
      created_at: '2024-01-18T14:15:00Z',
      updated_at: '2024-01-18T14:15:00Z',
    },
  ];

  beforeEach(() => {
    mockGetReviewsByProductId.mockReturnValue(mockReviewsData);
    mockGetProductRating.mockReturnValue({ average: 4.5, count: 2 });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders reviews correctly', () => {
    render(<ProductReviews productId={mockProductId} />);
    
    expect(screen.getByText('Customer Reviews')).toBeInTheDocument();
    expect(screen.getByText('4.5')).toBeInTheDocument();
    expect(screen.getByText('2 reviews')).toBeInTheDocument();
    expect(screen.getByText('Excellent product!')).toBeInTheDocument();
    expect(screen.getByText('Good value')).toBeInTheDocument();
  });

  it('shows empty state when no reviews', () => {
    mockGetReviewsByProductId.mockReturnValue([]);
    mockGetProductRating.mockReturnValue({ average: 0, count: 0 });
    
    render(<ProductReviews productId={mockProductId} />);
    
    expect(screen.getByText('No reviews yet')).toBeInTheDocument();
    expect(screen.getByText('Be the first to review this product')).toBeInTheDocument();
  });

  it('filters reviews by rating', async () => {
    render(<ProductReviews productId={mockProductId} />);
    
    // Click on 5-star filter
    const fiveStarFilter = screen.getByRole('button', { name: /5/ });
    fireEvent.click(fiveStarFilter);
    
    await waitFor(() => {
      expect(screen.getByText('Excellent product!')).toBeInTheDocument();
      expect(screen.queryByText('Good value')).not.toBeInTheDocument();
    });
  });

  it('sorts reviews correctly', async () => {
    render(<ProductReviews productId={mockProductId} />);
    
    const sortSelect = screen.getByDisplayValue('Newest');
    fireEvent.change(sortSelect, { target: { value: 'oldest' } });
    
    await waitFor(() => {
      const reviews = screen.getAllByText(/review/i);
      // Verify that oldest review appears first
      expect(reviews[0]).toBeInTheDocument();
    });
  });

  it('displays verified purchase badges', () => {
    render(<ProductReviews productId={mockProductId} />);
    
    const verifiedBadges = screen.getAllByText('Verified Purchase');
    expect(verifiedBadges).toHaveLength(2);
  });

  it('shows write review button', () => {
    render(<ProductReviews productId={mockProductId} />);
    
    expect(screen.getByText('Write a Review')).toBeInTheDocument();
  });

  it('displays rating distribution correctly', () => {
    render(<ProductReviews productId={mockProductId} />);
    
    // Check that rating distribution bars are present
    const ratingBars = screen.getAllByRole('generic');
    expect(ratingBars.length).toBeGreaterThan(0);
  });

  it('handles helpful/not helpful buttons', () => {
    render(<ProductReviews productId={mockProductId} />);
    
    const helpfulButtons = screen.getAllByText('Helpful');
    const notHelpfulButtons = screen.getAllByText('Not helpful');
    
    expect(helpfulButtons.length).toBeGreaterThan(0);
    expect(notHelpfulButtons.length).toBeGreaterThan(0);
    
    // Click helpful button
    fireEvent.click(helpfulButtons[0]);
    // In a real implementation, this would update the helpful count
  });

  it('renders star ratings correctly', () => {
    render(<ProductReviews productId={mockProductId} />);
    
    // Check for star icons in reviews
    const starIcons = document.querySelectorAll('svg');
    expect(starIcons.length).toBeGreaterThan(0);
  });

  it('shows review dates in correct format', () => {
    render(<ProductReviews productId={mockProductId} />);
    
    // Check that dates are displayed
    expect(screen.getByText('1/20/2024')).toBeInTheDocument();
    expect(screen.getByText('1/18/2024')).toBeInTheDocument();
  });
});
