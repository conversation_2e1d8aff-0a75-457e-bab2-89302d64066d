import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface CustomerInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  newsletter: boolean;
}

export interface Address {
  id?: string;
  type: 'home' | 'office' | 'other';
  label: string;
  firstName: string;
  lastName: string;
  company: string;
  address1: string;
  address2: string;
  city: string;
  state: string;
  postalCode: string;
  phone: string;
  isDefault: boolean;
}

export interface CheckoutState {
  // Customer Information
  customerInfo: CustomerInfo;
  
  // Addresses
  savedAddresses: Address[];
  selectedShippingAddressId: string | null;
  selectedBillingAddressId: string | null;
  shippingAddress: Address;
  billingAddress: Address;
  sameAsShipping: boolean;
  
  // Checkout Flow
  currentStep: number;
  completedSteps: number[];
  
  // Actions
  setCustomerInfo: (info: Partial<CustomerInfo>) => void;
  setShippingAddress: (address: Address) => void;
  setBillingAddress: (address: Address) => void;
  setSameAsShipping: (same: boolean) => void;
  setCurrentStep: (step: number) => void;
  markStepCompleted: (step: number) => void;
  
  // Address Management
  addSavedAddress: (address: Address) => void;
  updateSavedAddress: (id: string, address: Address) => void;
  deleteSavedAddress: (id: string) => void;
  selectShippingAddress: (id: string) => void;
  selectBillingAddress: (id: string) => void;
  
  // Auto-populate functionality
  autoPopulateFromCustomerInfo: () => void;
  
  // Reset
  resetCheckout: () => void;
}

const defaultCustomerInfo: CustomerInfo = {
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  newsletter: false,
};

const defaultAddress: Address = {
  type: 'home',
  label: '',
  firstName: '',
  lastName: '',
  company: '',
  address1: '',
  address2: '',
  city: '',
  state: '',
  postalCode: '',
  phone: '',
  isDefault: false,
};

// Mock saved addresses for demonstration
const mockSavedAddresses: Address[] = [
  {
    id: 'addr-1',
    type: 'home',
    label: 'Home Address',
    firstName: 'John',
    lastName: 'Doe',
    company: '',
    address1: '123 Main Street',
    address2: 'Apt 4B',
    city: 'Mumbai',
    state: 'Maharashtra',
    postalCode: '400001',
    phone: '+91 9876543210',
    isDefault: true,
  },
  {
    id: 'addr-2',
    type: 'office',
    label: 'Office Address',
    firstName: 'John',
    lastName: 'Doe',
    company: 'Tech Corp',
    address1: '456 Business Park',
    address2: 'Floor 5',
    city: 'Bangalore',
    state: 'Karnataka',
    postalCode: '560001',
    phone: '+91 9876543210',
    isDefault: false,
  },
];

export const useCheckoutStore = create<CheckoutState>()(
  persist(
    (set, get) => ({
      // Initial State
      customerInfo: defaultCustomerInfo,
      savedAddresses: mockSavedAddresses,
      selectedShippingAddressId: null,
      selectedBillingAddressId: null,
      shippingAddress: defaultAddress,
      billingAddress: defaultAddress,
      sameAsShipping: true,
      currentStep: 1,
      completedSteps: [],

      // Customer Info Actions
      setCustomerInfo: (info) =>
        set((state) => ({
          customerInfo: { ...state.customerInfo, ...info },
        })),

      // Address Actions
      setShippingAddress: (address) => set({ shippingAddress: address }),
      setBillingAddress: (address) => set({ billingAddress: address }),
      setSameAsShipping: (same) => set({ sameAsShipping: same }),

      // Step Management
      setCurrentStep: (step) => set({ currentStep: step }),
      markStepCompleted: (step) =>
        set((state) => ({
          completedSteps: [...new Set([...state.completedSteps, step])],
        })),

      // Address Management
      addSavedAddress: (address) =>
        set((state) => ({
          savedAddresses: [
            ...state.savedAddresses,
            { ...address, id: `addr-${Date.now()}` },
          ],
        })),

      updateSavedAddress: (id, address) =>
        set((state) => ({
          savedAddresses: state.savedAddresses.map((addr) =>
            addr.id === id ? { ...address, id } : addr
          ),
        })),

      deleteSavedAddress: (id) =>
        set((state) => ({
          savedAddresses: state.savedAddresses.filter((addr) => addr.id !== id),
        })),

      selectShippingAddress: (id) => {
        const address = get().savedAddresses.find((addr) => addr.id === id);
        if (address) {
          set({
            selectedShippingAddressId: id,
            shippingAddress: address,
          });
        }
      },

      selectBillingAddress: (id) => {
        const address = get().savedAddresses.find((addr) => addr.id === id);
        if (address) {
          set({
            selectedBillingAddressId: id,
            billingAddress: address,
          });
        }
      },

      // Auto-populate from customer info
      autoPopulateFromCustomerInfo: () => {
        const { customerInfo, shippingAddress, billingAddress } = get();
        
        const updatedShippingAddress = {
          ...shippingAddress,
          firstName: customerInfo.firstName,
          lastName: customerInfo.lastName,
          phone: customerInfo.phone,
        };
        
        const updatedBillingAddress = {
          ...billingAddress,
          firstName: customerInfo.firstName,
          lastName: customerInfo.lastName,
          phone: customerInfo.phone,
        };

        set({
          shippingAddress: updatedShippingAddress,
          billingAddress: updatedBillingAddress,
        });
      },

      // Reset
      resetCheckout: () =>
        set({
          customerInfo: defaultCustomerInfo,
          selectedShippingAddressId: null,
          selectedBillingAddressId: null,
          shippingAddress: defaultAddress,
          billingAddress: defaultAddress,
          sameAsShipping: true,
          currentStep: 1,
          completedSteps: [],
        }),
    }),
    {
      name: 'checkout-storage',
      partialize: (state) => ({
        customerInfo: state.customerInfo,
        savedAddresses: state.savedAddresses,
        currentStep: state.currentStep,
        completedSteps: state.completedSteps,
      }),
    }
  )
);
