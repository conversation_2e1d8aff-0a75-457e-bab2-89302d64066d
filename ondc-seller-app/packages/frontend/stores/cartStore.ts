import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { useEffect, useState } from 'react';

export interface CartItem {
  id: string;
  productId: string;
  name: string;
  price: number;
  quantity: number;
  image: string;
  variant?: {
    id: string;
    name: string;
    sku: string;
  };
  maxQuantity: number;
  sellerId: string;
  sellerName: string;
}

export interface CartSummary {
  subtotal: number;
  tax: number;
  shipping: number;
  discount: number;
  total: number;
  totalItems: number;
  isEmpty: boolean;
}

interface CartStore {
  items: CartItem[];
  isLoading: boolean;
  
  // Actions
  addItem: (item: Omit<CartItem, 'quantity'>, quantity?: number) => void;
  removeItem: (itemId: string) => void;
  updateQuantity: (itemId: string, quantity: number) => void;
  clearCart: () => void;
  getItemQuantity: (productId: string, variant?: any) => number;
  isInCart: (productId: string, variant?: any) => boolean;
  
  // Computed values
  getSummary: () => CartSummary;
}

// Helper function to generate item ID
const generateItemId = (productId: string, variant?: any): string => {
  if (variant && variant.id) {
    return `${productId}-${variant.id}`;
  }
  return productId;
};

// Calculate tax (6% GST)
const calculateTax = (subtotal: number): number => {
  return Math.round(subtotal * 0.06);
};

// Calculate shipping (free for orders above ₹500)
const calculateShipping = (subtotal: number): number => {
  return subtotal >= 500 ? 0 : 50;
};

export const useCartStore = create<CartStore>()(
  persist(
    (set, get) => ({
      items: [],
      isLoading: false,

      addItem: (newItem, quantity = 1) => {
        const itemId = generateItemId(newItem.productId, newItem.variant);
        
        set((state) => {
          const existingItemIndex = state.items.findIndex(item => item.id === itemId);

          if (existingItemIndex >= 0) {
            // Update existing item quantity
            const updatedItems = [...state.items];
            const existingItem = updatedItems[existingItemIndex];
            const newQuantity = existingItem.quantity + quantity;
            const maxQuantity = existingItem.maxQuantity || 999;

            updatedItems[existingItemIndex] = {
              ...existingItem,
              quantity: Math.min(newQuantity, maxQuantity),
            };

            return { items: updatedItems };
          } else {
            // Add new item
            const cartItem: CartItem = {
              ...newItem,
              id: itemId,
              quantity: Math.min(quantity, newItem.maxQuantity || 999),
            };

            return { items: [...state.items, cartItem] };
          }
        });
      },

      removeItem: (itemId) => {
        set((state) => ({
          items: state.items.filter(item => item.id !== itemId)
        }));
      },

      updateQuantity: (itemId, quantity) => {
        if (quantity <= 0) {
          get().removeItem(itemId);
          return;
        }

        set((state) => ({
          items: state.items.map(item => {
            if (item.id === itemId) {
              const maxQuantity = item.maxQuantity || 999;
              return {
                ...item,
                quantity: Math.min(quantity, maxQuantity),
              };
            }
            return item;
          })
        }));
      },

      clearCart: () => {
        set({ items: [] });
      },

      getItemQuantity: (productId, variant) => {
        const itemId = generateItemId(productId, variant);
        const item = get().items.find(item => item.id === itemId);
        return item?.quantity || 0;
      },

      isInCart: (productId, variant) => {
        return get().getItemQuantity(productId, variant) > 0;
      },

      getSummary: () => {
        const items = get().items;
        const subtotal = items.reduce((total, item) => total + (item.price * item.quantity), 0);
        const tax = calculateTax(subtotal);
        const shipping = calculateShipping(subtotal);
        const discount = 0; // Can be implemented later
        const total = subtotal + tax + shipping - discount;
        const totalItems = items.reduce((total, item) => total + item.quantity, 0);
        const isEmpty = items.length === 0;

        return {
          subtotal,
          tax,
          shipping,
          discount,
          total,
          totalItems,
          isEmpty,
        };
      },
    }),
    {
      name: 'ondc-cart-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({ items: state.items }),
    }
  )
);

// Hook for cart summary with hydration safety
export const useCartSummary = () => {
  const [isMounted, setIsMounted] = useState(false);
  const getSummary = useCartStore((state) => state.getSummary);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) {
    return {
      subtotal: 0,
      tax: 0,
      shipping: 0,
      discount: 0,
      total: 0,
      totalItems: 0,
      isEmpty: true,
    };
  }

  return getSummary();
};

// Hook for cart items count with hydration safety
export const useCartItemsCount = () => {
  const [isMounted, setIsMounted] = useState(false);
  const items = useCartStore((state) => state.items);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) {
    return 0;
  }

  return items.reduce((total, item) => total + item.quantity, 0);
};

// Hydration-safe cart store hook
export const useHydratedCartStore = () => {
  const [isMounted, setIsMounted] = useState(false);
  const store = useCartStore();

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) {
    return {
      ...store,
      items: [],
      isInCart: () => false,
      getItemQuantity: () => 0,
    };
  }

  return store;
};
