{"name": "@ondc-seller/frontend", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:unit": "jest --testPathPattern=__tests__/components --watchAll=false", "test:integration": "jest --testPathPattern=__tests__/integration --watchAll=false", "test:watch": "jest --watch", "test:coverage": "jest --coverage --watchAll=false", "test:ci": "jest --ci --coverage --watchAll=false --maxWorkers=1", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "playwright:install": "playwright install", "clean": "rimraf .turbo node_modules .next coverage test-results"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@heroicons/react": "^2.0.18", "@medusajs/medusa-js": "^6.1.10", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.1.1", "@ondc-seller/prisma": "*", "@tailwindcss/line-clamp": "^0.4.4", "@tanstack/react-query": "^5.81.5", "axios": "^1.10.0", "better-sqlite3": "^11.10.0", "date-fns": "^3.6.0", "express": "^5.1.0", "lucide-react": "^0.522.0", "next": "^14.2.29", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-query": "^3.39.3", "recharts": "^2.12.7", "swiper": "^11.2.8", "swr": "^2.2.4", "tailwindcss": "^3.3.6", "zustand": "^5.0.6"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@types/jest": "^29.5.14", "@types/node": "^20.17.48", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "autoprefixer": "^10.4.21", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "msw": "^1.3.5", "postcss": "^8.5.3", "puppeteer": "^24.10.0", "rimraf": "^5.0.5", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "msw": {"workerDirectory": ["public"]}}