'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { Search, X, Clock, TrendingUp } from 'lucide-react';
// TODO: Replace with real API integration
interface MockProduct {
  id: string;
  title: string;
  price: number;
  image: string;
  category: string;
  subcategory: string;
}

interface SearchSuggestion {
  id: string;
  type: 'product' | 'category' | 'recent' | 'trending';
  title: string;
  subtitle?: string;
  image?: string;
  url: string;
}

interface EnhancedSearchProps {
  placeholder?: string;
  className?: string;
  showSuggestions?: boolean;
}

const EnhancedSearch: React.FC<EnhancedSearchProps> = ({
  placeholder = 'Search products...',
  className = '',
  showSuggestions = true,
}) => {
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  // Load recent searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('recentSearches');
    if (saved) {
      setRecentSearches(JSON.parse(saved));
    }
  }, []);

  // Generate suggestions based on query
  useEffect(() => {
    if (!query.trim()) {
      // Show recent searches and trending when no query
      const recentSuggestions: SearchSuggestion[] = recentSearches.slice(0, 3).map((search, index) => ({
        id: `recent-${index}`,
        type: 'recent',
        title: search,
        url: `/products?search=${encodeURIComponent(search)}`,
      }));

      const trendingSuggestions: SearchSuggestion[] = [
        { id: 'trending-1', type: 'trending', title: 'Wireless Headphones', url: '/products?search=headphones' },
        { id: 'trending-2', type: 'trending', title: 'Smart Watch', url: '/products?search=watch' },
        { id: 'trending-3', type: 'trending', title: 'Office Chair', url: '/products?search=chair' },
      ];

      setSuggestions([...recentSuggestions, ...trendingSuggestions]);
      return;
    }

    const searchQuery = query.toLowerCase();
    
    // TODO: Replace with real API call for product search
    const productSuggestions: SearchSuggestion[] = []
      .slice(0, 5)
      .map(product => ({
        id: `product-${product.id}`,
        type: 'product',
        title: product.title,
        subtitle: `₹${product.price.toLocaleString()}`,
        image: product.thumbnail,
        url: `/products/${product.handle}`,
      }));

    // Category suggestions
    const categories = ['electronics', 'fashion', 'home-garden', 'sports-fitness', 'books-media', 'beauty-health'];
    const categorySuggestions: SearchSuggestion[] = categories
      .filter(category => category.toLowerCase().includes(searchQuery))
      .slice(0, 2)
      .map(category => ({
        id: `category-${category}`,
        type: 'category',
        title: category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase()),
        subtitle: 'Category',
        url: `/categories/${category}`,
      }));

    setSuggestions([...productSuggestions, ...categorySuggestions]);
  }, [query, recentSearches]);

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSelectedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(e.target.value);
    setIsOpen(true);
    setSelectedIndex(-1);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => (prev < suggestions.length - 1 ? prev + 1 : prev));
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => (prev > 0 ? prev - 1 : -1));
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && suggestions[selectedIndex]) {
          handleSuggestionClick(suggestions[selectedIndex]);
        } else if (query.trim()) {
          handleSearch(query);
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  const handleSearch = (searchQuery: string) => {
    if (!searchQuery.trim()) return;

    // Save to recent searches
    const updatedRecent = [searchQuery, ...recentSearches.filter(s => s !== searchQuery)].slice(0, 5);
    setRecentSearches(updatedRecent);
    localStorage.setItem('recentSearches', JSON.stringify(updatedRecent));

    // Navigate to search results
    router.push(`/products?search=${encodeURIComponent(searchQuery)}`);
    setIsOpen(false);
    setQuery('');
    inputRef.current?.blur();
  };

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    if (suggestion.type === 'product' || suggestion.type === 'category') {
      router.push(suggestion.url);
    } else {
      handleSearch(suggestion.title);
    }
    setIsOpen(false);
    setQuery('');
  };

  const clearQuery = () => {
    setQuery('');
    setIsOpen(false);
    inputRef.current?.focus();
  };

  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case 'recent':
        return <Clock className="w-4 h-4 text-gray-400" />;
      case 'trending':
        return <TrendingUp className="w-4 h-4 text-gray-400" />;
      default:
        return null;
    }
  };

  return (
    <div ref={searchRef} className={`relative ${className}`}>
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-400" />
        </div>
        
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => setIsOpen(true)}
          placeholder={placeholder}
          className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
        />
        
        {query && (
          <button
            onClick={clearQuery}
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
          >
            <X className="h-5 w-5 text-gray-400 hover:text-gray-600" />
          </button>
        )}
      </div>

      {/* Suggestions Dropdown */}
      {isOpen && showSuggestions && suggestions.length > 0 && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-96 overflow-y-auto">
          {suggestions.map((suggestion, index) => (
            <button
              key={suggestion.id}
              onClick={() => handleSuggestionClick(suggestion)}
              className={`w-full px-4 py-3 text-left hover:bg-gray-50 flex items-center space-x-3 ${
                index === selectedIndex ? 'bg-blue-50' : ''
              }`}
            >
              {suggestion.image ? (
                <img
                  src={suggestion.image}
                  alt={suggestion.title}
                  className="w-8 h-8 object-cover rounded"
                />
              ) : (
                getSuggestionIcon(suggestion.type)
              )}
              
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {suggestion.title}
                </p>
                {suggestion.subtitle && (
                  <p className="text-xs text-gray-500 truncate">
                    {suggestion.subtitle}
                  </p>
                )}
              </div>
              
              {suggestion.type === 'recent' && (
                <span className="text-xs text-gray-400">Recent</span>
              )}
              {suggestion.type === 'trending' && (
                <span className="text-xs text-gray-400">Trending</span>
              )}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default EnhancedSearch;
