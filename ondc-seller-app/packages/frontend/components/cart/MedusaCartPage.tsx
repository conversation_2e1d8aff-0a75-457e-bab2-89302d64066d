'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useMedusaCartContext } from '@/hooks/useMedusaCart';
import { useMedusaCustomer } from '@/hooks/useMedusaCustomer';
import { PlusIcon, MinusIcon, XMarkIcon } from '@heroicons/react/24/outline';

export default function MedusaCartPage() {
  const { 
    cart, 
    items, 
    totalItems, 
    totalPrice, 
    subtotal,
    removeItem, 
    updateQuantity, 
    clearCart,
    isLoading,
    isUpdatingItem,
    error,
    clearError
  } = useMedusaCartContext();

  const { completeCartWithCOD, isCreatingOrder } = useMedusaCustomer();
  const [isCheckingOut, setIsCheckingOut] = useState(false);

  const handleQuantityChange = async (itemId: string, newQuantity: number) => {
    if (newQuantity < 1) {
      await removeItem(itemId);
    } else {
      await updateQuantity(itemId, newQuantity);
    }
  };

  const handleCheckout = async () => {
    if (!cart?.id) {
      alert('No cart found');
      return;
    }

    setIsCheckingOut(true);
    try {
      const order = await completeCartWithCOD(cart.id);
      if (order) {
        // Redirect to order confirmation page
        window.location.href = `/orders/${order.id}`;
      } else {
        alert('Failed to complete order. Please try again.');
      }
    } catch (error) {
      console.error('Checkout error:', error);
      alert('Failed to complete order. Please try again.');
    } finally {
      setIsCheckingOut(false);
    }
  };

  const formatPrice = (amount: number) => {
    return `€${(amount / 100).toFixed(2)}`;
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-300 rounded w-1/4 mb-8"></div>
            <div className="space-y-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="bg-white p-6 rounded-lg shadow">
                  <div className="flex space-x-4">
                    <div className="w-20 h-20 bg-gray-300 rounded"></div>
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                      <div className="h-4 bg-gray-300 rounded w-1/2"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h2 className="text-lg font-semibold text-red-800 mb-2">Cart Error</h2>
            <p className="text-red-600 mb-4">{error}</p>
            <button 
              onClick={clearError}
              className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
            >
              Dismiss
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!items || items.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Your Cart is Empty</h1>
            <p className="text-gray-600 mb-8">Add some products to get started!</p>
            <Link 
              href="/products"
              className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700"
            >
              Browse Products
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Shopping Cart</h1>

        {/* Customer Info Debug Section */}
        {cart && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h3 className="text-sm font-medium text-blue-800 mb-2">Cart Customer Info (Debug)</h3>
            <div className="text-sm text-blue-700">
              <p><strong>Cart ID:</strong> {cart.id}</p>
              <p><strong>Customer ID:</strong> {cart.customer_id || 'Not set'}</p>
              <p><strong>Email:</strong> {cart.email || 'Not set'}</p>
              <p><strong>Region:</strong> {cart.region_id}</p>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2 space-y-4">
            {items.map((item) => (
              <div key={item.id} className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center space-x-4">
                  {/* Product Image */}
                  <div className="w-20 h-20 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
                    {item.thumbnail ? (
                      <img
                        src={item.thumbnail}
                        alt={item.title}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-gray-400 text-xs">
                        No Image
                      </div>
                    )}
                  </div>

                  {/* Product Info */}
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900">{item.title}</h3>
                    {item.variant?.title && (
                      <p className="text-sm text-gray-600">{item.variant.title}</p>
                    )}
                    {item.variant?.sku && (
                      <p className="text-xs text-gray-500">SKU: {item.variant.sku}</p>
                    )}
                    <p className="text-lg font-bold text-blue-600 mt-1">
                      {formatPrice(item.unit_price)}
                    </p>
                  </div>

                  {/* Quantity Controls */}
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                      disabled={isUpdatingItem}
                      className="p-1 rounded-full border border-gray-300 hover:bg-gray-50 disabled:opacity-50"
                    >
                      <MinusIcon className="w-4 h-4" />
                    </button>
                    <span className="w-8 text-center font-medium">{item.quantity}</span>
                    <button
                      onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                      disabled={isUpdatingItem}
                      className="p-1 rounded-full border border-gray-300 hover:bg-gray-50 disabled:opacity-50"
                    >
                      <PlusIcon className="w-4 h-4" />
                    </button>
                  </div>

                  {/* Item Total */}
                  <div className="text-right">
                    <p className="text-lg font-bold text-gray-900">
                      {formatPrice(item.total)}
                    </p>
                  </div>

                  {/* Remove Button */}
                  <button
                    onClick={() => removeItem(item.id)}
                    disabled={isUpdatingItem}
                    className="p-1 text-red-500 hover:text-red-700 disabled:opacity-50"
                  >
                    <XMarkIcon className="w-5 h-5" />
                  </button>
                </div>
              </div>
            ))}

            {/* Clear Cart Button */}
            <div className="text-center pt-4">
              <button
                onClick={clearCart}
                className="text-red-600 hover:text-red-800 font-medium"
              >
                Clear Cart
              </button>
            </div>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow p-6 sticky top-8">
              <h2 className="text-xl font-bold text-gray-900 mb-4">Order Summary</h2>
              
              <div className="space-y-2 mb-4">
                <div className="flex justify-between">
                  <span className="text-gray-600">Items ({totalItems})</span>
                  <span className="font-medium">{formatPrice(subtotal)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Shipping</span>
                  <span className="font-medium">Free</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Tax</span>
                  <span className="font-medium">Included</span>
                </div>
                <div className="border-t pt-2">
                  <div className="flex justify-between">
                    <span className="text-lg font-bold">Total</span>
                    <span className="text-lg font-bold text-blue-600">
                      {formatPrice(totalPrice)}
                    </span>
                  </div>
                </div>
              </div>

              {/* Payment Method Info */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-4">
                <div className="flex items-center">
                  <div className="text-green-600 mr-2">💰</div>
                  <div>
                    <p className="text-sm font-medium text-green-800">Cash on Delivery</p>
                    <p className="text-xs text-green-600">Pay when your order arrives</p>
                  </div>
                </div>
              </div>

              {/* Checkout Button */}
              <button
                onClick={handleCheckout}
                disabled={isCheckingOut || isCreatingOrder}
                className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed transition-colors"
              >
                {isCheckingOut || isCreatingOrder ? 'Processing...' : 'Checkout with Cash on Delivery'}
              </button>

              <p className="text-xs text-gray-500 text-center mt-2">
                By proceeding, you agree to our terms and conditions
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
