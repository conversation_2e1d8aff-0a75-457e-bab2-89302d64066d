import React, { useState } from 'react';
import { Address } from '@/stores/checkoutStore';
import { PlusIcon, HomeIcon, BuildingOfficeIcon, MapPinIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline';

interface AddressSelectorProps {
  addresses: Address[];
  selectedAddressId: string | null;
  onSelectAddress: (id: string) => void;
  onAddNewAddress: () => void;
  onEditAddress: (address: Address) => void;
  onDeleteAddress: (id: string) => void;
  title: string;
}

export default function AddressSelector({
  addresses,
  selectedAddressId,
  onSelectAddress,
  onAddNewAddress,
  onEditAddress,
  onDeleteAddress,
  title,
}: AddressSelectorProps) {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);

  const getAddressIcon = (type: string) => {
    switch (type) {
      case 'home':
        return HomeIcon;
      case 'office':
        return BuildingOfficeIcon;
      default:
        return MapPinIcon;
    }
  };

  const handleDeleteClick = (addressId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setShowDeleteConfirm(addressId);
  };

  const confirmDelete = (addressId: string) => {
    onDeleteAddress(addressId);
    setShowDeleteConfirm(null);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">{title}</h3>
        <button
          type="button"
          onClick={onAddNewAddress}
          className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Add New Address
        </button>
      </div>

      {addresses.length === 0 ? (
        <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
          <MapPinIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No saved addresses</h3>
          <p className="mt-1 text-sm text-gray-500">Get started by adding your first address.</p>
          <div className="mt-6">
            <button
              type="button"
              onClick={onAddNewAddress}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Add Address
            </button>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-4">
          {addresses.map((address) => {
            const IconComponent = getAddressIcon(address.type);
            const isSelected = selectedAddressId === address.id;

            return (
              <div
                key={address.id}
                className={`relative border-2 rounded-lg p-4 cursor-pointer transition-all duration-200 ${
                  isSelected
                    ? 'border-blue-500 bg-blue-50 shadow-md'
                    : 'border-gray-200 hover:border-gray-300 hover:shadow-sm'
                }`}
                onClick={() => onSelectAddress(address.id!)}
              >
                {/* Selection indicator */}
                {isSelected && (
                  <div className="absolute top-2 right-2">
                    <div className="h-6 w-6 rounded-full bg-blue-600 flex items-center justify-center">
                      <svg className="h-4 w-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>
                )}

                <div className="flex items-start space-x-3">
                  {/* Address type icon */}
                  <div className={`flex-shrink-0 p-2 rounded-lg ${
                    isSelected ? 'bg-blue-100' : 'bg-gray-100'
                  }`}>
                    <IconComponent className={`h-5 w-5 ${
                      isSelected ? 'text-blue-600' : 'text-gray-600'
                    }`} />
                  </div>

                  {/* Address details */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h4 className={`text-sm font-medium ${
                        isSelected ? 'text-blue-900' : 'text-gray-900'
                      }`}>
                        {address.label || `${address.type.charAt(0).toUpperCase() + address.type.slice(1)} Address`}
                      </h4>
                      {address.isDefault && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Default
                        </span>
                      )}
                    </div>
                    
                    <p className={`text-sm mt-1 ${
                      isSelected ? 'text-blue-800' : 'text-gray-600'
                    }`}>
                      {address.firstName} {address.lastName}
                    </p>
                    
                    <p className={`text-sm ${
                      isSelected ? 'text-blue-700' : 'text-gray-500'
                    }`}>
                      {address.address1}
                      {address.address2 && `, ${address.address2}`}
                    </p>
                    
                    <p className={`text-sm ${
                      isSelected ? 'text-blue-700' : 'text-gray-500'
                    }`}>
                      {address.city}, {address.state} {address.postalCode}
                    </p>
                    
                    <p className={`text-sm ${
                      isSelected ? 'text-blue-700' : 'text-gray-500'
                    }`}>
                      {address.phone}
                    </p>
                  </div>
                </div>

                {/* Action buttons */}
                <div className="flex items-center justify-end space-x-2 mt-3">
                  <button
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation();
                      onEditAddress(address);
                    }}
                    className="inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <PencilIcon className="h-3 w-3 mr-1" />
                    Edit
                  </button>
                  
                  {!address.isDefault && (
                    <button
                      type="button"
                      onClick={(e) => handleDeleteClick(address.id!, e)}
                      className="inline-flex items-center px-2 py-1 border border-red-300 shadow-sm text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                    >
                      <TrashIcon className="h-3 w-3 mr-1" />
                      Delete
                    </button>
                  )}
                </div>

                {/* Delete confirmation */}
                {showDeleteConfirm === address.id && (
                  <div className="absolute inset-0 bg-white bg-opacity-95 rounded-lg flex items-center justify-center">
                    <div className="text-center">
                      <p className="text-sm text-gray-900 mb-3">Delete this address?</p>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => confirmDelete(address.id!)}
                          className="px-3 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700"
                        >
                          Delete
                        </button>
                        <button
                          onClick={() => setShowDeleteConfirm(null)}
                          className="px-3 py-1 bg-gray-300 text-gray-700 text-xs rounded hover:bg-gray-400"
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}
