'use client';

import React from 'react';
import { useTenantContext } from '../../lib/contexts/TenantContext';
import { TenantSelector } from '../../lib/contexts/TenantContext';
import { 
  BuildingStorefrontIcon, 
  CubeIcon, 
  ExclamationTriangleIcon,
  ArrowPathIcon 
} from '@heroicons/react/24/outline';

// Store Header Component
function StoreHeader() {
  const { state, getTenantDisplayName, getTenantBranding } = useTenantContext();
  const branding = getTenantBranding();

  return (
    <div className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center space-x-4">
            <BuildingStorefrontIcon className="h-8 w-8 text-gray-600" />
            <div>
              <h1 className="text-xl font-semibold text-gray-900">
                {getTenantDisplayName()}
              </h1>
              {state.storeInfo && (
                <p className="text-sm text-gray-500">
                  {state.storeInfo.store.domain}
                </p>
              )}
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <TenantSelector />
            {state.loading && (
              <ArrowPathIcon className="h-5 w-5 text-gray-400 animate-spin" />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

// Store Info Card Component
function StoreInfoCard() {
  const { state } = useTenantContext();
  
  if (!state.storeInfo) return null;

  const { store } = state.storeInfo;

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-lg font-medium text-gray-900 mb-4">Store Information</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <h3 className="text-sm font-medium text-gray-500">Store Details</h3>
          <div className="mt-2 space-y-2">
            <p><span className="font-medium">Name:</span> {store.name}</p>
            <p><span className="font-medium">Domain:</span> {store.domain}</p>
            <p><span className="font-medium">Currency:</span> {store.currency}</p>
            <p><span className="font-medium">Region:</span> {store.region}</p>
          </div>
        </div>
        
        <div>
          <h3 className="text-sm font-medium text-gray-500">Features</h3>
          <div className="mt-2">
            <div className="flex flex-wrap gap-2">
              {store.features.map((feature, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  {feature}
                </span>
              ))}
            </div>
          </div>
        </div>
        
        <div>
          <h3 className="text-sm font-medium text-gray-500">Branding</h3>
          <div className="mt-2 space-y-2">
            <div className="flex items-center space-x-2">
              <div 
                className="w-4 h-4 rounded"
                style={{ backgroundColor: store.branding.primaryColor }}
              />
              <span className="text-sm">{store.branding.primaryColor}</span>
            </div>
          </div>
        </div>
        
        <div>
          <h3 className="text-sm font-medium text-gray-500">ONDC Configuration</h3>
          <div className="mt-2 space-y-1 text-sm">
            <p><span className="font-medium">Participant ID:</span> {store.ondcConfig.participantId}</p>
            <p><span className="font-medium">Domain:</span> {store.ondcConfig.domain}</p>
          </div>
        </div>
      </div>
    </div>
  );
}

// Products Grid Component
function ProductsGrid() {
  const { state } = useTenantContext();
  
  if (!state.products) return null;

  const { products, count, filtered_for_tenant, total_products_in_system } = state.products;

  return (
    <div className="bg-white rounded-lg shadow">
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-medium text-gray-900">Products</h2>
          <div className="text-sm text-gray-500">
            {filtered_for_tenant} of {total_products_in_system} products
          </div>
        </div>
      </div>
      
      <div className="p-6">
        {products.length === 0 ? (
          <div className="text-center py-8">
            <CubeIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No products</h3>
            <p className="mt-1 text-sm text-gray-500">
              No products found for this tenant.
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {products.map((product) => (
              <div key={product.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="aspect-w-1 aspect-h-1 w-full overflow-hidden rounded-lg bg-gray-200 mb-4">
                  {product.thumbnail ? (
                    <img
                      src={product.thumbnail}
                      alt={product.title}
                      className="h-full w-full object-cover object-center"
                    />
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <CubeIcon className="h-12 w-12 text-gray-400" />
                    </div>
                  )}
                </div>
                
                <h3 className="text-sm font-medium text-gray-900 mb-2">
                  {product.title}
                </h3>
                
                {product.description && (
                  <p className="text-sm text-gray-500 mb-2 line-clamp-2">
                    {product.description}
                  </p>
                )}
                
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-500">
                    ID: {product.id}
                  </span>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    product.status === 'published' 
                      ? 'bg-green-100 text-green-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {product.status}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

// Error Display Component
function ErrorDisplay() {
  const { state, refreshData } = useTenantContext();
  
  if (!state.error) return null;

  return (
    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
      <div className="flex">
        <div className="flex-shrink-0">
          <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium text-red-800">
            Error loading store data
          </h3>
          <div className="mt-2 text-sm text-red-700">
            <p>{state.error}</p>
          </div>
          <div className="mt-4">
            <button
              onClick={refreshData}
              className="bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

// Loading Component
function LoadingDisplay() {
  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="h-3 bg-gray-200 rounded"></div>
              <div className="h-3 bg-gray-200 rounded w-3/4"></div>
            </div>
            <div className="space-y-2">
              <div className="h-3 bg-gray-200 rounded"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="space-y-3">
                <div className="h-32 bg-gray-200 rounded"></div>
                <div className="h-3 bg-gray-200 rounded"></div>
                <div className="h-3 bg-gray-200 rounded w-3/4"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

// Main Multi-Tenant Store Interface Component
export default function MultiTenantStoreInterface() {
  const { state } = useTenantContext();

  return (
    <div className="min-h-screen bg-gray-50">
      <StoreHeader />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <ErrorDisplay />
        
        {state.loading ? (
          <LoadingDisplay />
        ) : (
          <div className="space-y-6">
            <StoreInfoCard />
            <ProductsGrid />
          </div>
        )}
      </div>
    </div>
  );
}
