# ProductListingPage Component

A reusable, feature-rich product listing component that provides a consistent UI for displaying products across different pages like top deals, featured products, hot picks, and subcategory pages.

## Features

- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **Multiple View Modes**: Grid and list view options
- **Advanced Filtering**: Price range, brand, rating, and availability filters
- **Flexible Sorting**: Multiple sorting options (featured, price, rating, newest, best sellers)
- **Customizable Configuration**: Different styles and behaviors for different page types
- **Tenant-Aware**: Supports multi-tenant data isolation
- **Loading States**: Built-in loading animations
- **Empty States**: Handles no products found scenarios
- **Breadcrumb Navigation**: Optional breadcrumb support
- **Subcategory Filtering**: Optional subcategory filter tabs

## Usage

### Basic Usage

```tsx
import ProductListingPage, { Product } from '@/components/common/ProductListingPage';
import { PAGE_CONFIGS } from '@/utils/productListingHelpers';

function MyProductPage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  return (
    <ProductListingPage
      products={products}
      config={PAGE_CONFIGS['top-deals']}
      isLoading={isLoading}
    />
  );
}
```

### With Subcategory Filtering

```tsx
import ProductListingPage, { Product, ProductListingConfig } from '@/components/common/ProductListingPage';
import { createBreadcrumbs, createSubcategoryFilters } from '@/utils/productListingHelpers';

function CategoryPage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [activeSubcategory, setActiveSubcategory] = useState('');

  const config: ProductListingConfig = {
    title: 'Electronics',
    subtitle: 'Discover amazing electronics',
    showDiscountBadge: true,
    showSaleBadge: true,
    showPriceComparison: true,
    showSavingsAmount: false,
    buttonText: 'View Product',
    breadcrumbs: createBreadcrumbs([
      { name: 'Categories', href: '/categories' },
      { name: 'Electronics' }
    ]),
    subcategoryFilters: [
      { id: 'smartphones', name: 'Smartphones', count: 25 },
      { id: 'laptops', name: 'Laptops', count: 18 },
      { id: 'headphones', name: 'Headphones', count: 32 }
    ],
    activeSubcategory
  };

  const handleSubcategoryChange = (subcategoryId: string) => {
    setActiveSubcategory(subcategoryId);
    // Filter products based on subcategory
  };

  return (
    <ProductListingPage
      products={products}
      config={config}
      isLoading={false}
      onSubcategoryChange={handleSubcategoryChange}
    />
  );
}
```

## Product Interface

```tsx
interface Product {
  id: number;
  name: string;
  slug: string;
  originalPrice: number;
  salePrice: number;
  discount?: number;
  image: string;
  category: string;
  rating: number;
  reviewCount: number;
  badge?: string;
  brand?: string;
  inStock?: boolean;
}
```

## Configuration Interface

```tsx
interface ProductListingConfig {
  title: string;
  subtitle: string;
  showDiscountBadge?: boolean;
  showSaleBadge?: boolean;
  showPriceComparison?: boolean;
  showSavingsAmount?: boolean;
  buttonText?: string;
  buttonStyle?: string;
  headerImage?: string;
  breadcrumbs?: Array<{ name: string; href?: string }>;
  subcategoryFilters?: Array<{ id: string; name: string; count?: number }>;
  activeSubcategory?: string;
}
```

## Pre-configured Page Types

The component comes with pre-configured settings for common page types:

### Top Deals
```tsx
config={PAGE_CONFIGS['top-deals']}
```
- Shows discount badges and sale badges
- Displays price comparison and savings amount
- "Buy Now" button with blue gradient

### Featured Products
```tsx
config={PAGE_CONFIGS['featured-products']}
```
- Shows sale badges only
- Displays price comparison
- "View Details" button with green gradient

### Hot Picks
```tsx
config={PAGE_CONFIGS['hot-picks']}
```
- Shows discount and sale badges
- Displays price comparison and savings amount
- "Get It Now" button with red gradient

### Subcategory Pages
```tsx
config={PAGE_CONFIGS['subcategory']}
```
- Shows discount and sale badges
- Displays price comparison
- "View Product" button with blue gradient

### Search Results
```tsx
config={PAGE_CONFIGS['search-results']}
```
- Shows discount and sale badges
- Displays price comparison
- "View Product" button with blue gradient

## Helper Functions

### Product Data Conversion
```tsx
import { convertToProduct, getProductImageUrl } from '@/utils/productListingHelpers';

// Convert any data to Product interface
const product = convertToProduct(rawData, id);

// Get product image URL
const imageUrl = getProductImageUrl(productName, productId);
```

### Breadcrumb Creation
```tsx
import { createBreadcrumbs } from '@/utils/productListingHelpers';

const breadcrumbs = createBreadcrumbs([
  { name: 'Categories', href: '/categories' },
  { name: 'Electronics', href: '/category/electronics' },
  { name: 'Smartphones' }
]);
```

### Subcategory Filters
```tsx
import { createSubcategoryFilters } from '@/utils/productListingHelpers';

const filters = createSubcategoryFilters(
  [
    { id: 'smartphones', name: 'Smartphones' },
    { id: 'laptops', name: 'Laptops' }
  ],
  products
);
```

## Styling

The component uses Tailwind CSS classes and is fully responsive. You can customize the button styles by providing a custom `buttonStyle` in the configuration:

```tsx
const config = {
  // ... other config
  buttonStyle: 'bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors'
};
```

## Examples

See the following example implementations:
- `/app/deals/page.tsx` - Top deals page
- `/app/featured/page.tsx` - Featured products page
- `/app/hot-picks/page.tsx` - Hot picks page
- `/app/category/[categoryName]/page.tsx` - Dynamic category/subcategory page

## Multi-tenant Support

The component works seamlessly with the tenant context. Each page can load different products based on the current tenant:

```tsx
function DealsPageContent() {
  const { tenantId } = useTenant();
  const [products, setProducts] = useState<Product[]>([]);

  useEffect(() => {
    const tenantProducts = getProductsForTenant(tenantId);
    setProducts(tenantProducts);
  }, [tenantId]);

  return (
    <ProductListingPage
      products={products}
      config={PAGE_CONFIGS['top-deals']}
      isLoading={false}
    />
  );
}
```

This ensures that each tenant sees their own curated product listings while maintaining the same consistent UI experience.
