'use client';

import React, { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ChevronDownIcon, ArrowRightIcon } from '@heroicons/react/24/outline';
import { useMedusaCategories, Category } from '@/hooks/useMedusaCategories';

interface MegaMenuProps {
  className?: string;
}



interface SubCategory {
  id: string;
  name: string;
  slug: string;
  productCount: number;
  href: string;
}

interface MegaMenuCategory {
  id: string;
  name: string;
  slug: string;
  href: string;
  subcategories: SubCategory[];
  promotionalBanner?: {
    title: string;
    subtitle: string;
    image: string;
    href: string;
    backgroundColor: string;
  };
}



// Helper function to convert Medusa category to MegaMenu format
const convertMedusaCategoryToMegaMenu = (category: Category): MegaMenuCategory => {
  return {
    id: category.id,
    name: category.name,
    slug: category.slug,
    href: `/categories/${category.slug}`,
    subcategories: category.subcategories.map(sub => ({
      id: sub.id,
      name: sub.name,
      slug: sub.slug,
      productCount: sub.productCount || Math.floor(Math.random() * 200) + 50,
      href: `/categories/${category.slug}/${sub.slug}`
    }))
  };
};

const MegaMenu: React.FC<MegaMenuProps> = ({ className = '' }) => {
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const animationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Get categories from Medusa backend
  const { categories: medusaCategories, loading, error } = useMedusaCategories();

  // Log navigation data changes for debugging
  useEffect(() => {
    console.log(`🎯 MegaMenu using Medusa categories: ${medusaCategories.length} categories loaded`);
  }, [medusaCategories.length]);

  // Promotional banners for categories
  const promotionalBanners = [
    { title: 'Flash Sale', subtitle: 'Up to 50% Off Electronics', image: '/images/banners/electronics-promo.svg', href: '/categories/electronics/sale', backgroundColor: 'bg-gradient-to-r from-blue-500 to-purple-600' },
    { title: 'New Collection', subtitle: 'Latest Fashion Trends', image: '/images/banners/fashion-promo.svg', href: '/categories/fashion/new', backgroundColor: 'bg-gradient-to-r from-pink-500 to-rose-600' },
    { title: 'Home Makeover', subtitle: 'Transform Your Space', image: '/images/banners/home-promo.svg', href: '/categories/home-garden/makeover', backgroundColor: 'bg-gradient-to-r from-green-500 to-emerald-600' },
    { title: 'Fitness Goals', subtitle: 'Achieve Your Best', image: '/images/banners/fitness-promo.svg', href: '/categories/sports-fitness/goals', backgroundColor: 'bg-gradient-to-r from-orange-500 to-red-600' },
    { title: 'Beauty Essentials', subtitle: 'Glow Up Collection', image: '/images/banners/beauty-promo.svg', href: '/categories/beauty-health/essentials', backgroundColor: 'bg-gradient-to-r from-purple-500 to-pink-600' },
    { title: 'Knowledge Hub', subtitle: 'Expand Your Mind', image: '/images/banners/books-promo.svg', href: '/categories/books-media/hub', backgroundColor: 'bg-gradient-to-r from-indigo-500 to-blue-600' },
  ];

  // Transform Medusa categories for mega menu
  const megaMenuData: MegaMenuCategory[] = medusaCategories.slice(0, 6).map((category, index) => {
    // Convert to MegaMenu format with promotional banner
    const megaMenuCategory = convertMedusaCategoryToMegaMenu(category);
    megaMenuCategory.promotionalBanner = promotionalBanners[index] || promotionalBanners[0];

    return megaMenuCategory;
  });

  const handleMouseEnter = (categoryId: string) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    if (animationTimeoutRef.current) {
      clearTimeout(animationTimeoutRef.current);
    }

    setActiveCategory(categoryId);
    setIsOpen(true);
    setIsAnimating(true);

    // Allow animation to complete
    animationTimeoutRef.current = setTimeout(() => {
      setIsAnimating(false);
    }, 200);
  };

  const handleMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setIsAnimating(true);
      animationTimeoutRef.current = setTimeout(() => {
        setActiveCategory(null);
        setIsOpen(false);
        setIsAnimating(false);
      }, 150);
    }, 300); // Longer delay to prevent accidental closing
  };

  const handleMenuMouseEnter = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    if (animationTimeoutRef.current) {
      clearTimeout(animationTimeoutRef.current);
    }
  };

  const handleMenuMouseLeave = () => {
    setIsAnimating(true);
    animationTimeoutRef.current = setTimeout(() => {
      setActiveCategory(null);
      setIsOpen(false);
      setIsAnimating(false);
    }, 150);
  };

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (animationTimeoutRef.current) {
        clearTimeout(animationTimeoutRef.current);
      }
    };
  }, []);

  // Keyboard navigation support
  const handleKeyDown = (event: React.KeyboardEvent, categoryId: string) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleMouseEnter(categoryId);
    } else if (event.key === 'Escape') {
      setActiveCategory(null);
      setIsOpen(false);
      setIsAnimating(false);
    }
  };

  const activeCategoryData = megaMenuData.find(cat => cat.id === activeCategory);

  // Show loading state if categories are being fetched
  if (loading) {
    return (
      <div className={`relative ${className}`} ref={menuRef}>
        <nav className="flex items-center space-x-8" role="navigation" aria-label="Product categories">
          {[1, 2, 3, 4, 5, 6].map((index) => (
            <div key={index} className="animate-pulse">
              <div className="h-10 w-24 bg-gray-200 rounded-md"></div>
            </div>
          ))}
        </nav>
      </div>
    );
  }

  // Show error state if there's an error and no fallback data
  if (error && medusaCategories.length === 0) {
    return (
      <div className={`relative ${className}`} ref={menuRef}>
        <nav className="flex items-center space-x-8" role="navigation" aria-label="Product categories">
          <div className="flex items-center space-x-3 text-sm text-red-600 bg-red-50 px-4 py-2 rounded-md">
            <span>Failed to load navigation.</span>
            <button
              onClick={() => window.location.reload()}
              className="text-red-700 hover:text-red-800 underline focus:outline-none"
            >
              Retry
            </button>
          </div>
        </nav>
      </div>
    );
  }

  //* Data Source Indicator (only in development)
  if(process.env.NODE_ENV === 'development'){
    console.log('🟡 MSW Mock MegaMenu Data');
    console.log(error && ' (with errors)');
  }

  return (
    <div className={`${className}`} ref={menuRef}>

      {/* Main Navigation */}
      <nav className="flex items-center justify-center space-x-2 sm:space-x-4 lg:space-x-8 overflow-x-auto scrollbar-hide" role="navigation" aria-label="Product categories">
        {megaMenuData.map((category) => (
          <div
            key={category.id}
            className="relative flex-shrink-0"
            onMouseEnter={() => handleMouseEnter(category.id)}
            onMouseLeave={handleMouseLeave}
          >
            <button
              type="button"
              className={`flex items-center px-2 sm:px-3 lg:px-4 py-2 sm:py-3 text-xs sm:text-sm font-medium transition-all duration-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 whitespace-nowrap ${
                activeCategory === category.id
                  ? 'text-blue-600 bg-blue-50 shadow-sm'
                  : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
              }`}
              onKeyDown={(e) => handleKeyDown(e, category.id)}
              aria-expanded={activeCategory === category.id}
              aria-haspopup="true"
              aria-label={`${category.name} category menu`}
            >
              <span className="font-semibold truncate max-w-[120px] sm:max-w-none">{category.name}</span>
              <ChevronDownIcon
                className={`ml-1 sm:ml-2 h-3 w-3 sm:h-4 sm:w-4 transition-transform duration-300 ease-in-out flex-shrink-0 ${
                  activeCategory === category.id ? 'rotate-180 text-blue-600' : 'text-gray-400'
                }`}
                aria-hidden="true"
              />
            </button>
          </div>
        ))}
      </nav>

      {/* Mega Menu Dropdown */}
      {isOpen && activeCategoryData && (
        <div
          className={`absolute bg-white border border-gray-200 shadow-2xl z-50 transition-all duration-300 ease-out hidden md:block ${
            isAnimating ? 'opacity-0 transform translate-y-2' : 'opacity-100 transform translate-y-0'
          }`}
          onMouseEnter={handleMenuMouseEnter}
          onMouseLeave={handleMenuMouseLeave}
          style={{
            left: '0',
            right: '0',
            top: '100%',
            width: '100%',
            maxWidth: '100%',
            margin: '0 auto',
            marginTop: '0px',
            borderRadius: '0 0 16px 16px',
            borderTop: 'none',
            minHeight: '400px',
            maxHeight: '600px',
            overflow: 'hidden'
          }}
          role="menu"
          aria-label={`${activeCategoryData.name} subcategories`}
        >
          <div className="px-8 py-6 h-full overflow-y-auto">
            {/* Category Header - Compact and Clean */}
            <div className="mb-6 text-center border-b border-gray-100 pb-4">
              <h2 className="text-xl font-bold text-gray-900 mb-1 tracking-tight">
                {activeCategoryData.name}
              </h2>
              <p className="text-sm text-gray-600 max-w-xl mx-auto">
                Discover our wide range of {activeCategoryData.name.toLowerCase()} products
              </p>
            </div>

            {/* Subcategories Grid - Fixed Layout */}
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 mb-6">
              {activeCategoryData.subcategories.map((subcategory, index) => (
                <Link
                  key={subcategory.id}
                  href={subcategory.href}
                  className="group block p-4 rounded-xl border border-gray-100 hover:border-blue-200 hover:shadow-lg transition-all duration-300 ease-out hover:bg-gradient-to-br hover:from-blue-50 hover:to-indigo-50 bg-white min-h-[100px] flex flex-col justify-between"
                  role="menuitem"
                  tabIndex={0}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      window.location.href = subcategory.href;
                    }
                  }}
                >
                  <div className="flex-1">
                    <h3 className="text-sm font-semibold text-gray-900 group-hover:text-blue-700 transition-colors duration-200 leading-tight mb-2 line-clamp-2">
                      {subcategory.name}
                    </h3>
                    <p className="text-xs text-gray-500 group-hover:text-blue-600 transition-colors duration-200 font-medium">
                      {subcategory.productCount.toLocaleString()} items
                    </p>
                  </div>
                  <div className="flex justify-end mt-2">
                    <ArrowRightIcon className="h-4 w-4 text-gray-300 group-hover:text-blue-600 transform group-hover:translate-x-1 transition-all duration-300" />
                  </div>
                </Link>
              ))}
            </div>

            {/* View All Link - Clean and Prominent */}
            <div className="text-center border-t border-gray-100 pt-4">
              <Link
                href={activeCategoryData.href}
                className="inline-flex items-center px-8 py-3 text-sm font-semibold text-white bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 group"
                role="menuitem"
              >
                <span>View All {activeCategoryData.name}</span>
                <ArrowRightIcon className="ml-3 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
              </Link>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MegaMenu;
