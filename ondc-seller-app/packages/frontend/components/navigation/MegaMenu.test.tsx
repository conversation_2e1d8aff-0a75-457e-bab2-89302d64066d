/**
 * MegaMenu Component Test
 * 
 * Tests the updated MegaMenu component with MSW mock data integration
 */

import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { TenantProvider } from '../../contexts/TenantContext';
import MegaMenu from './MegaMenu';
import { mockTenants, mockCategories, getCategoriesByTenant } from '../../data/msw-data';

// Mock the tenant context with test data
const MockTenantProvider = ({ children, tenantId = 'tenant_1' }: { children: React.ReactNode; tenantId?: string }) => {
  const selectedTenant = mockTenants.find(t => t.id === tenantId);
  
  const mockContextValue = {
    selectedTenant: selectedTenant || mockTenants[0],
    tenantId,
    setSelectedTenant: jest.fn(),
    sellers: mockTenants,
    loading: false,
    error: null,
    refreshTenants: jest.fn(),
  };

  return (
    <TenantProvider value={mockContextValue}>
      {children}
    </TenantProvider>
  );
};

describe('MegaMenu Component', () => {
  beforeEach(() => {
    // Clear any previous console logs
    jest.clearAllMocks();
  });

  test('renders loading state initially', () => {
    render(
      <MockTenantProvider>
        <MegaMenu />
      </MockTenantProvider>
    );

    // Should show loading skeleton
    expect(screen.getByRole('navigation')).toBeInTheDocument();
  });

  test('loads TechHub Electronics categories correctly', async () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
    
    render(
      <MockTenantProvider tenantId="tenant_1">
        <MegaMenu />
      </MockTenantProvider>
    );

    await waitFor(() => {
      // Check if categories were loaded
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('MegaMenu loaded')
      );
    });

    // Verify TechHub categories are available
    const techCategories = getCategoriesByTenant('tenant_1');
    expect(techCategories).toHaveLength(5);
    expect(techCategories[0].name).toBe('Electronics');
    expect(techCategories[1].name).toBe('Computers & Laptops');

    consoleSpy.mockRestore();
  });

  test('loads StyleHub Fashion categories correctly', async () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
    
    render(
      <MockTenantProvider tenantId="tenant_2">
        <MegaMenu />
      </MockTenantProvider>
    );

    await waitFor(() => {
      // Check if categories were loaded
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('MegaMenu loaded')
      );
    });

    // Verify StyleHub categories are available
    const fashionCategories = getCategoriesByTenant('tenant_2');
    expect(fashionCategories).toHaveLength(5);
    expect(fashionCategories[0].name).toBe('Clothing');
    expect(fashionCategories[1].name).toBe('Footwear');

    consoleSpy.mockRestore();
  });

  test('loads HomeHub Essentials categories correctly', async () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
    
    render(
      <MockTenantProvider tenantId="tenant_3">
        <MegaMenu />
      </MockTenantProvider>
    );

    await waitFor(() => {
      // Check if categories were loaded
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('MegaMenu loaded')
      );
    });

    // Verify HomeHub categories are available
    const homeCategories = getCategoriesByTenant('tenant_3');
    expect(homeCategories).toHaveLength(5);
    expect(homeCategories[0].name).toBe('Furniture');
    expect(homeCategories[1].name).toBe('Kitchen & Dining');

    consoleSpy.mockRestore();
  });

  test('handles tenant switching correctly', async () => {
    const { rerender } = render(
      <MockTenantProvider tenantId="tenant_1">
        <MegaMenu />
      </MockTenantProvider>
    );

    // Wait for initial load
    await waitFor(() => {
      expect(screen.getByRole('navigation')).toBeInTheDocument();
    });

    // Switch to different tenant
    rerender(
      <MockTenantProvider tenantId="tenant_2">
        <MegaMenu />
      </MockTenantProvider>
    );

    // Should reload with new tenant data
    await waitFor(() => {
      expect(screen.getByRole('navigation')).toBeInTheDocument();
    });
  });

  test('shows error state when tenant data fails to load', async () => {
    // Mock a scenario where getCategoriesByTenant throws an error
    const originalGetCategories = getCategoriesByTenant;
    
    render(
      <MockTenantProvider tenantId="invalid_tenant">
        <MegaMenu />
      </MockTenantProvider>
    );

    await waitFor(() => {
      // Should show navigation even with no categories
      expect(screen.getByRole('navigation')).toBeInTheDocument();
    });
  });

  test('renders with proper accessibility attributes', async () => {
    render(
      <MockTenantProvider tenantId="tenant_1">
        <MegaMenu />
      </MockTenantProvider>
    );

    await waitFor(() => {
      const navigation = screen.getByRole('navigation');
      expect(navigation).toHaveAttribute('aria-label', 'Product categories');
    });
  });
});

// Integration test to verify MSW data structure
describe('MSW Mock Data Integration', () => {
  test('verifies mock data structure is correct', () => {
    // Test that all tenants have categories
    mockTenants.forEach(tenant => {
      const categories = getCategoriesByTenant(tenant.id);
      expect(categories.length).toBeGreaterThan(0);
      
      categories.forEach(category => {
        expect(category).toHaveProperty('id');
        expect(category).toHaveProperty('name');
        expect(category).toHaveProperty('slug');
        expect(category).toHaveProperty('tenant_id', tenant.id);
      });
    });
  });

  test('verifies tenant isolation in categories', () => {
    const tenant1Categories = getCategoriesByTenant('tenant_1');
    const tenant2Categories = getCategoriesByTenant('tenant_2');
    const tenant3Categories = getCategoriesByTenant('tenant_3');

    // Categories should be different for each tenant
    expect(tenant1Categories).not.toEqual(tenant2Categories);
    expect(tenant2Categories).not.toEqual(tenant3Categories);
    expect(tenant1Categories).not.toEqual(tenant3Categories);

    // Each tenant should have their specific categories
    expect(tenant1Categories.some(cat => cat.name === 'Electronics')).toBe(true);
    expect(tenant2Categories.some(cat => cat.name === 'Clothing')).toBe(true);
    expect(tenant3Categories.some(cat => cat.name === 'Furniture')).toBe(true);
  });
});
