'use client';

import React, { useState, useEffect } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import { useTenant } from '@/contexts/TenantContext';
import { multiTenantAPI } from '@/lib/api/multi-tenant';
import { safeExtractText } from '@/lib/utils/richTextParser';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

interface BannerSlide {
  id: number;
  title: string;
  subtitle?: string;
  description?: string;
  image?: string;
  buttonText?: string;
  buttonLink?: string;
  backgroundColor: string;
}

const defaultSlides: BannerSlide[] = [
  {
    id: 1,
    title: 'Welcome to Our Store',
    subtitle: 'Discover Amazing Products',
    description: 'Explore our wide range of quality products at great prices',
    buttonText: 'Shop Now',
    buttonLink: '/products',
    backgroundColor: 'bg-gradient-to-r from-blue-600 to-purple-600',
  },
  {
    id: 2,
    title: 'New Arrivals',
    subtitle: 'Fresh Collection',
    description: 'Check out the latest additions to our catalog',
    buttonText: 'Explore',
    buttonLink: '/categories',
    backgroundColor: 'bg-gradient-to-r from-green-500 to-teal-600',
  },
];

// Helper function to get gradient colors for banner backgrounds
const getGradientByIndex = (index: number): string => {
  const gradients = [
    'from-blue-600 to-purple-600',
    'from-green-500 to-teal-600',
    'from-purple-600 to-pink-600',
    'from-orange-500 to-red-600',
    'from-indigo-600 to-blue-600',
  ];
  return gradients[index % gradients.length];
};

export default function MultiTenantHeroBanner() {
  const { tenantId, selectedTenant } = useTenant();
  const [slides, setSlides] = useState<BannerSlide[]>(defaultSlides);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBanners = async () => {
      console.log('🔍 [Banner] useEffect triggered - tenantId:', tenantId, 'selectedTenant:', selectedTenant);

      // if (!tenantId || !selectedTenant) {
      //   console.log('⚠️ [Banner] Missing tenantId or selectedTenant, using default slides');
      //   setSlides(defaultSlides);
      //   setLoading(false);
      //   return;
      // }

      try {
        setLoading(true);
        setError(null);

        // console.log(`🚀 [Banner] Fetching banners from Strapi for tenant ${tenantId} (${selectedTenant.name})`);

        // Real-time Strapi API call
        // const response = await multiTenantAPI.getBannersByTenant(tenantId);
        const response = await multiTenantAPI.getActiveBanners();

        console.log('✅ [Banner] Strapi API Response:', response);
        console.log('✅ [Banner] Response data:', response.data);
        console.log('✅ [Banner] Response data type:', typeof response.data);
        console.log('✅ [Banner] Response data is array:', Array.isArray(response.data));

        if (response.data && Array.isArray(response.data) && response.data.length > 0) {


          // Transform Strapi banners to slides format
          const transformedSlides: BannerSlide[] = response.data.map((banner: any, index: number) => {

            // Check if data is nested under attributes (Strapi v4) or at root level
            const bannerData = banner.attributes || banner;   

            // Extract image URL from Strapi response
            const imageUrl = bannerData.image?.url;
            const fullImageUrl = imageUrl
              ? (imageUrl.startsWith('http') ? imageUrl : `http://localhost:1337${imageUrl}`)
              : '/images/banners/default-banner.jpg';


            const slide = {
              id: banner.id || index,
              title: String(bannerData.title || 'Welcome'),
              subtitle: String(bannerData.subtitle || ''),
              description: safeExtractText(bannerData.description, ''),
              image: fullImageUrl,
              buttonText: String(bannerData.buttonText || 'Learn More'),
              buttonLink: String(bannerData.buttonLink || bannerData.link || '#'),
              backgroundColor: `bg-gradient-to-r ${getGradientByIndex(index)}`,
            };

            console.log(`🔍 [Banner] Transformed slide ${index + 1}:`, slide);
            return slide;
          });

          console.log('✅ [Banner] All transformed banner slides:', transformedSlides);
          setSlides(transformedSlides);
        } else {
          console.log(`⚠️ [Banner] No banners found - using fallback slides`);    
          setSlides(defaultSlides);
        }


      } catch (err) {
        console.error('❌ Error fetching banners:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch banners');

        // Use tenant-specific default slides as fallback
        if (selectedTenant) {

          setSlides(defaultSlides);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchBanners();
  }, [tenantId, selectedTenant]);

  const getGradientByIndex = (index: number): string => {
    const gradients = [
      'from-blue-600 to-purple-600',
      'from-green-500 to-teal-600',
      'from-orange-500 to-red-600',
      'from-purple-500 to-pink-600',
      'from-indigo-500 to-blue-600',
    ];
    return gradients[index % gradients.length];
  };



  if (loading) {
    return (
      <div className="relative h-96 bg-gray-200 animate-pulse">
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center">
            <div className="h-8 bg-gray-300 rounded w-64 mb-4"></div>
            <div className="h-4 bg-gray-300 rounded w-48 mb-2"></div>
            <div className="h-4 bg-gray-300 rounded w-32"></div>
          </div>
        </div>
      </div>
    );
  }

  // Hide banner section if no slides are available
  if (slides.length === 0) {
    return null;
  }

  return (
    <div className="relative h-96 md:h-[500px] overflow-hidden">
      <Swiper
        modules={[Navigation, Pagination, Autoplay]}
        spaceBetween={0}
        slidesPerView={1}
        navigation={{
          prevEl: '.swiper-button-prev-custom',
          nextEl: '.swiper-button-next-custom',
        }}
        pagination={{
          clickable: true,
          el: '.swiper-pagination-custom',
        }}
        autoplay={{
          delay: 4000,
          disableOnInteraction: false,
        }}
        loop={slides.length > 1}
        className="h-full"
      >
        {slides.map((slide) => (
          <SwiperSlide key={slide.id}>
            <div className="relative h-full flex items-center justify-center">
              {/* Background Image */}
              {slide.image ? (
                <div
                  className="absolute inset-0 bg-cover bg-center bg-no-repeat"
                  style={{ backgroundImage: `url(${slide.image})` }}
                />
              ) : (
                <div className={`absolute inset-0 ${slide.backgroundColor}`} />
              )}

              {/* Overlay for better text readability */}
              <div className="absolute inset-0 bg-black bg-opacity-40"></div>

              {/* Content */}
              <div className="relative z-10 text-center text-white px-4 sm:px-6 lg:px-8 max-w-4xl mx-auto">
                <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-4 drop-shadow-lg">
                  {String(slide.title || '')}
                </h1>
                {slide.subtitle && (
                  <p className="text-lg sm:text-xl md:text-2xl mb-4 text-blue-100 drop-shadow-md">
                    {String(slide.subtitle)}
                  </p>
                )}
                {slide.description && (
                  <p className="text-base sm:text-lg md:text-xl mb-8 text-gray-100 max-w-2xl mx-auto drop-shadow-md">
                    {String(slide.description)}
                  </p>
                )}
                {slide.buttonText && slide.buttonLink && (
                  <a
                    href={String(slide.buttonLink)}
                    className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-gray-50 transition-colors duration-200 shadow-lg"
                  >
                    {String(slide.buttonText)}
                  </a>
                )}
              </div>
            </div>
          </SwiperSlide>
        ))}
      </Swiper>

      {/* Custom Navigation Buttons */}
      {slides.length > 1 && (
        <>
          <button className="swiper-button-prev-custom absolute left-4 top-1/2 transform -translate-y-1/2 z-20 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full p-2 transition-all duration-200">
            <ChevronLeftIcon className="h-6 w-6 text-white" />
          </button>
          <button className="swiper-button-next-custom absolute right-4 top-1/2 transform -translate-y-1/2 z-20 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full p-2 transition-all duration-200">
            <ChevronRightIcon className="h-6 w-6 text-white" />
          </button>
        </>
      )}

      {/* Custom Pagination */}
      {slides.length > 1 && (
        <div className="swiper-pagination-custom absolute bottom-4 left-1/2 transform -translate-x-1/2 z-20 flex space-x-2">
          {/* Pagination dots will be rendered by Swiper */}
        </div>
      )}

      {/* Tenant Info Overlay */}
      {selectedTenant && (
        <div className="absolute top-4 left-4 z-20 bg-white bg-opacity-90 rounded-lg p-3 max-w-xs">
          <div className="flex items-center space-x-2">
            <div className="h-8 w-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-semibold text-sm">
                {selectedTenant.name.charAt(0).toUpperCase()}
              </span>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-900">{selectedTenant.name}</p>
              <p className="text-xs text-gray-600">{selectedTenant.business_type}</p>
            </div>
          </div>
        </div>
      )}

      {error && (
        <div className="absolute bottom-4 right-4 z-20 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <p className="text-sm">Failed to load banners: {error}</p>
        </div>
      )}
    </div>
  );
}
