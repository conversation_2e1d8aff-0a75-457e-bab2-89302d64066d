'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowRightIcon, ChevronDownIcon } from '@heroicons/react/24/outline';
import { getCategories } from '@/lib/strapi-api';
// TODO: Replace with real API integration

interface Category {
  id: number;
  name: string;
  slug: string;
  image: string;
  productCount: number;
  description: string;
}

// Helper function to extract text from rich text content
function extractTextFromRichText(richText: any): string {
  if (!richText) return '';

  if (Array.isArray(richText)) {
    return richText
      .map((block: any) => {
        if (block.children && Array.isArray(block.children)) {
          return block.children.map((child: any) => child.text || '').join('');
        }
        return '';
      })
      .join(' ');
  }

  if (typeof richText === 'string') {
    return richText;
  }

  return '';
}

// Helper function to transform Strapi category data
function transformStrapiCategory(strapiCategory: any): Category {
  const attributes = strapiCategory.attributes || strapiCategory;
  const name = attributes.name || strapiCategory.name || 'Unknown Category';
  const slug = attributes.slug || strapiCategory.slug || name.toLowerCase().replace(/\s+/g, '-');
  const description =
    extractTextFromRichText(attributes.description || strapiCategory.description) ||
    'Explore our products';

  // Generate image URL based on category name
  const imageMap: Record<string, string> = {
    electronics: '/images/categories/electronics.jpg',
    fashion: '/images/categories/fashion.jpg',
    'home-garden': '/images/categories/home-garden.jpg',
    'sports-fitness': '/images/categories/sports.jpg',
    'books-media': '/images/categories/books.jpg',
    'beauty-health': '/images/categories/beauty.jpg',
    automotive: '/images/categories/automotive.jpg',
    'toys-games': '/images/categories/toys.jpg',
    'organic-food': '/images/categories/organic-food.jpg',
    smartphones: '/images/categories/smartphones.jpg',
  };

  const image =
    imageMap[slug] ||
    imageMap[name.toLowerCase().replace(/\s+/g, '-')] ||
    '/images/categories/default.jpg';

  return {
    id: strapiCategory.id,
    name,
    slug,
    image,
    productCount: Math.floor(Math.random() * 2000) + 100, // Random count for now
    description: description.substring(0, 50) + (description.length > 50 ? '...' : ''),
  };
}

// No default categories - fetch exclusively from Strapi CMS

export default function ShopByCategory() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [isExpanded, setIsExpanded] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Progressive disclosure: show first 4 categories by default
  const visibleCategories = isExpanded ? categories : categories.slice(0, 4);
  const hasMoreCategories = categories.length > 4;

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  // Fetch categories from Strapi CMS via Next.js API
  // useEffect(() => {
  //   const fetchCategories = async () => {
  //     try {
  //       console.log('🚀 ShopByCategory: Fetching categories...');
  //       setIsLoading(true);

  //       // Fetch subcategories (isSubcategory = true)
  //       const response = await fetch('/api/product-categories?isSubcategory=true&pageSize=50');

  //       if (!response.ok) {
  //         throw new Error(`HTTP error! status: ${response.status}`);
  //       }

  //       const result = await response.json();
        
  //       if (result.success && Array.isArray(result.data)) {
  //         console.log(`✅ Successfully fetched ${result.data.length} subcategories`);
  //         setCategories(result.data);
  //         setError(null);
  //       } else {
  //         throw new Error('Invalid response format from API');
  //       }
  //     } catch (error) {
  //       console.error('❌ Error fetching categories:', error);
  //       setError('Failed to load categories. Please try again later.');
  //       setCategories([]);
  //     } finally {
  //       setIsLoading(false);
  //     }
  //   };

  //   fetchCategories();
  // }, []);

// In ShopByCategory.tsx
useEffect(() => {
  const fetchCategories = async () => {
    try {
      console.log('🚀 ShopByCategory: Fetching categories...');
      setIsLoading(true);

      const response = await fetch('/api/product-categories?isSubcategory=true&pageSize=50');

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success && Array.isArray(result.data)) {
        console.log(`✅ Successfully fetched ${result.data.length} subcategories`);
        setCategories(result.data);
        setError(null);
      } else {
        throw new Error('Invalid response format from API');
      }
    } catch (error) {
      console.error('❌ Error fetching categories:', error);
      console.log('🔄 Falling back to mock data...');

      // Fallback to mock data
      const mockCategories = getCategoryShowcaseByTenant(defaultTenantId);
      const transformedMockCategories = mockCategories.map(cat => ({
        id: parseInt(cat.id.replace('cat-', '')),
        name: cat.name,
        slug: cat.slug,
        image: cat.image,
        productCount: cat.productCount,
        description: cat.description,
      }));

      setCategories(transformedMockCategories);
      setError(null);
    } finally {
      setIsLoading(false);
    }
  };

  fetchCategories();
}, []);

  console.log(
    'ShopByCategory component rendered with categories:',
    categories.length,
    'loading:',
    isLoading
  );

  return (
    <section className="py-16 bg-white" data-testid="shop-by-category">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Shop by Category</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Discover our wide range of products across different categories
          </p>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {[...Array(8)].map((_, index) => (
              <div key={index} className="aspect-square rounded-lg bg-gray-200 animate-pulse">
                <div className="h-full flex flex-col justify-end p-4">
                  <div className="h-4 bg-gray-300 rounded mb-2"></div>
                  <div className="h-3 bg-gray-300 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-300 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Error State */}
        {!isLoading && error && (
          <div className="text-center py-12">
            <div className="mb-4">
              <svg
                className="mx-auto h-16 w-16 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1}
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Categories Unavailable</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              Retry
            </button>
          </div>
        )}

        {/* Categories Grid */}
        {!isLoading && !error && categories.length > 0 && (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 transition-all duration-500 ease-in-out">
            {visibleCategories.map(category => (
              <Link
                key={category.id}
                href={`/categories/${category.slug}`}
                className="group relative overflow-hidden rounded-lg bg-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
                data-testid="category-link"
              >
                {/* Category Image */}
                <div className="aspect-square relative overflow-hidden bg-gradient-to-br from-blue-400 to-blue-600">
                  <img
                    src={category.image}
                    alt={category.name}
                    className="object-cover transition-transform duration-300 group-hover:scale-110"
                    // sizes="(max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
                    onError={e => {
                      console.error(`Failed to load image: ${category.image}`);
                      // Fallback to a solid color background
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                  {/* Overlay */}
                  <div className="absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-30 transition-all duration-300" />

                  {/* Content */}
                  <div className="absolute inset-0 flex flex-col justify-end p-4">
                    <div className="text-white">
                      <h3 className="font-semibold text-lg mb-1 group-hover:text-yellow-300 transition-colors duration-200">
                        {category.name}
                      </h3>
                      <p className="text-sm text-white/90 mb-2">{category.description}</p>
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-white/80">
                          {(category.productCount || 0).toLocaleString()} items
                        </span>
                        <ArrowRightIcon className="h-4 w-4 text-white/80 group-hover:text-yellow-300 group-hover:translate-x-1 transition-all duration-200" />
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        )}

        {/* Empty State */}
        {!isLoading && !error && categories.length === 0 && (
          <div className="text-center py-12">
            <div className="mb-4">
              <svg
                className="mx-auto h-16 w-16 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1}
                  d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Categories Available</h3>
            <p className="text-gray-600">
              Categories will appear here once they are added to the CMS.
            </p>
          </div>
        )}

        {/* Toggle Button for Progressive Disclosure */}
        {hasMoreCategories && (
          <div className="text-center mt-12">
            <button
              onClick={toggleExpanded}
              className="inline-flex items-center px-8 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-400 transition-colors duration-200"
              // className="inline-flex items-center px-8 py-3 bg-blue-600 text-white text-base font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
              aria-expanded={isExpanded}
              aria-label={isExpanded ? 'Show fewer categories' : 'Show more categories'}
            >
              {isExpanded ? 'View Less' : 'View More'}
              <ChevronDownIcon
                className={`ml-2 h-5 w-5 transition-transform duration-300 ${
                  isExpanded ? 'rotate-180' : ''
                }`}
              />
            </button>
          </div>
        )}
      </div>
    </section>
  );
}
