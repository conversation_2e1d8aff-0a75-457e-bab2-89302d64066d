'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination } from 'swiper/modules';
import { StarIcon, HeartIcon, EyeIcon, ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';
import { useTenant } from '@/contexts/TenantContext';
// TODO: Replace with real API integration
import { safeExtractText } from '@/lib/utils/richTextParser';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

interface Product {
  id: number;
  name: string;
  slug?: string;
  price: number;
  sale_price?: number;
  short_description?: string;
  image: string;
  rating: number;
  reviews: number;
  featured: boolean;
}

// Default products for fallback
const defaultProducts: Product[] = [
  {
    id: 1,
    name: 'Premium Wireless Headphones',
    price: 199.99,
    sale_price: 149.99,
    short_description: 'High-quality wireless headphones with noise cancellation',
    image: 'https://picsum.photos/400/400?random=headphones',
    rating: 4.5,
    reviews: 128,
    featured: true,
  },
  {
    id: 2,
    name: 'Smart Fitness Watch',
    price: 299.99,
    short_description: 'Track your fitness goals with this advanced smartwatch',
    image: 'https://picsum.photos/400/400?random=watch',
    rating: 4.8,
    reviews: 89,
    featured: true,
  },
  {
    id: 3,
    name: 'Portable Bluetooth Speaker',
    price: 79.99,
    sale_price: 59.99,
    short_description: 'Compact speaker with powerful sound and long battery life',
    image: 'https://picsum.photos/400/400?random=speaker',
    rating: 4.3,
    reviews: 156,
    featured: true,
  },
  {
    id: 4,
    name: 'Wireless Charging Pad',
    price: 49.99,
    short_description: 'Fast wireless charging for all compatible devices',
    image: 'https://picsum.photos/400/400?random=charger',
    rating: 4.6,
    reviews: 203,
    featured: true,
  },
];

// Helper function to generate product image URL
function getProductImageUrl(productName: string, id: number): string {
  const slug = productName.toLowerCase().replace(/\s+/g, '-');
  return `https://picsum.photos/400/400?random=${slug}-${id}`;
}

// Helper function to transform Strapi product data
function transformStrapiProduct(strapiProduct: any): Product {
  // Handle both Strapi v5 format (with attributes) and v4 format (direct fields)
  const attributes = strapiProduct.attributes || strapiProduct;

  const name = safeExtractText(attributes.name || strapiProduct.name, 'Unknown Product');
  const price = attributes.price || strapiProduct.price || 0;
  const sale_price = attributes.sale_price || strapiProduct.sale_price || undefined;

  // Safely extract description text
  let short_description = safeExtractText(attributes.short_description || strapiProduct.short_description, '');
  if (!short_description && (attributes.description || strapiProduct.description)) {
    short_description = safeExtractText(attributes.description || strapiProduct.description, '');
  }
  if (!short_description) {
    short_description = `High-quality ${name.toLowerCase()}`;
  }

  return {
    id: strapiProduct.id,
    name,
    slug: attributes.slug || strapiProduct.slug || name.toLowerCase().replace(/\s+/g, '-'),
    price,
    sale_price,
    short_description,
    image: getProductImageUrl(name, strapiProduct.id),
    rating: 4.0 + Math.random() * 1.0, // Random rating between 4.0-5.0
    reviews: Math.floor(Math.random() * 200) + 50, // Random reviews 50-250
    featured: attributes.featured || strapiProduct.featured || false,
  };
}

// Helper function to render star rating
function renderStars(rating: number) {
  const stars = [];
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 !== 0;

  for (let i = 0; i < fullStars; i++) {
    stars.push(
      <StarIconSolid key={i} className="h-4 w-4 text-yellow-400" />
    );
  }

  if (hasHalfStar) {
    stars.push(
      <div key="half" className="relative">
        <StarIcon className="h-4 w-4 text-yellow-400" />
        <StarIconSolid className="h-4 w-4 text-yellow-400 absolute top-0 left-0 w-1/2 overflow-hidden" />
      </div>
    );
  }

  const emptyStars = 5 - Math.ceil(rating);
  for (let i = 0; i < emptyStars; i++) {
    stars.push(
      <StarIcon key={`empty-${i}`} className="h-4 w-4 text-gray-300" />
    );
  }

  return stars;
}

export default function MultiTenantFeaturedProducts() {
  const { tenantId, selectedTenant } = useTenant();

  // Debug logging
  console.log('🔍 [MultiTenantFeaturedProducts] Component rendered with:', {
    tenantId,
    selectedTenant: selectedTenant?.name,
  });

  // Get tenant-specific featured products from mock data
  const products = React.useMemo(() => {
    if (tenantId) {
      const tenantFeaturedProducts = // TODO: Replace with real API call
  const products: any[] = [];
      if (tenantFeaturedProducts && tenantFeaturedProducts.length > 0) {
        return tenantFeaturedProducts.map(product => ({
          id: parseInt(product.id.replace(/\D/g, '')) || Math.random() * 1000,
          name: product.name,
          slug: product.slug,
          price: product.price / 100, // Convert from paise to rupees
          sale_price: product.originalPrice ? product.originalPrice / 100 : undefined,
          short_description: product.description,
          image: product.image || `https://picsum.photos/400/400?random=${product.id}`,
          rating: product.rating,
          reviews: product.reviewCount,
          featured: true,
        }));
      }
    }

    // Final fallback to default products
    return defaultProducts;
  }, [tenantId]);



  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-4 mb-4">
            <h2 className="text-3xl font-bold text-gray-900">
              {selectedTenant ? `Featured Products from ${selectedTenant.name}` : 'Featured Products'}
            </h2>
         
          </div>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {selectedTenant
              ? `Discover the best products handpicked from ${selectedTenant.name}`
              : 'Discover our handpicked selection of premium products'
            }
          </p>
        </div>

        {/* Products Carousel */}
        <div className="relative">
          <Swiper
            modules={[Navigation, Pagination]}
            spaceBetween={24}
            slidesPerView={1}
            navigation={{
              prevEl: '.featured-products-prev',
              nextEl: '.featured-products-next',
            }}
            pagination={{
              clickable: true,
              el: '.featured-products-pagination',
            }}
            breakpoints={{
              640: {
                slidesPerView: 2,
              },
              768: {
                slidesPerView: 3,
              },
              1024: {
                slidesPerView: 4,
              },
            }}
            className="pb-12"
          >
            {products.slice(0, 8).map((product) => (
              <SwiperSlide key={product.id} className="h-auto">
                <div className="group bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden h-full flex flex-col">
                  {/* Product Image */}
                  <div className="aspect-square relative overflow-hidden">
                    <Image
                      src={getProductImageUrl(product.name, product.id)}

                      // src={product.image}
                      alt={product.name}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                      sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 25vw"
                    />
                    {product.sale_price && (
                      <div className="absolute top-3 left-3 bg-red-500 text-white px-2 py-1 rounded-md text-xs font-semibold">
                        Sale
                      </div>
                    )}
                    <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                      <button className="bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full p-2 shadow-md">
                        <HeartIcon className="h-4 w-4 text-gray-600 hover:text-red-500" />
                      </button>
                    </div>
                  </div>

                  {/* Product Info */}
                  <div className="p-4 flex flex-col flex-grow">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2 min-h-[3.5rem] group-hover:text-blue-600 transition-colors">
                      <Link href={`/product/featured-products/general?product-name=${product.slug || product.id}`}>
                        {product.name}
                      </Link>
                    </h3>

                    {product.short_description && (
                      <p className="text-sm text-gray-600 mb-3 line-clamp-2 min-h-[2.5rem]">
                        {product.short_description}
                      </p>
                    )}

                    {/* Rating */}
                    <div className="flex items-center space-x-1 mb-3">
                      <div className="flex items-center">
                        {renderStars(product.rating)}
                      </div>
                      <span className="text-sm text-gray-500">
                        ({product.reviews})
                      </span>
                    </div>

                    {/* Price */}
                    <div className="flex items-center space-x-2 mb-4">
                      {product.sale_price ? (
                        <>
                          <span className="text-xl font-bold text-green-600">
                            ₹{product.sale_price}
                          </span>
                          <span className="text-sm text-gray-500 line-through">
                            ₹{product.price}
                          </span>
                        </>
                      ) : (
                        <span className="text-xl font-bold text-gray-900">
                          ₹{product.price}
                        </span>
                      )}
                    </div>

                    {/* View Product Button */}
                    <Link
                      href={`/product/featured-products/general?product-name=${product.slug || product.id}`}
                      className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2 mt-auto"
                    >
                      <EyeIcon className="h-4 w-4" />
                      <span>View Product</span>
                    </Link>
                  </div>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>

          {/* Custom Navigation Buttons */}
          <button className="featured-products-prev absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 z-10 bg-white shadow-lg hover:shadow-xl rounded-full p-3 transition-all duration-200">
            <ChevronLeftIcon className="h-5 w-5 text-gray-600" />
          </button>
          <button className="featured-products-next absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-4 z-10 bg-white shadow-lg hover:shadow-xl rounded-full p-3 transition-all duration-200">
            <ChevronRightIcon className="h-5 w-5 text-gray-600" />
          </button>

          {/* Custom Pagination */}
          <div className="featured-products-pagination flex justify-center mt-8"></div>
        </div>

        {/* View All Featured Products Button */}
        <div className="text-center mt-12">
          <Link
            href="/deals?type=featured-products"
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-200"
          >
            View All Featured Products
          </Link>
        </div>


      </div>
    </section>
  );
}
