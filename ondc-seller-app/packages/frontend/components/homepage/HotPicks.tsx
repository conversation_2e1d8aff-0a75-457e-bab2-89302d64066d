'use client';

import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import { ChevronLeftIcon, ChevronRightIcon, StarIcon, HeartIcon, EyeIcon, FireIcon } from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';
import Link from 'next/link';
import Image from 'next/image';
import { useTenant } from '@/contexts/TenantContext';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
// TODO: Replace with real API integration
interface HotPickProduct {
  id: number;
  name: string;
  slug: string;
  originalPrice: number;
  salePrice: number;
  discount: number;
  rating: number;
  reviews: number;
  image: string;
  category: string;
  inStock: boolean;
}

interface HotProduct {
  id: number;
  name: string;
  slug: string;
  price: number;
  salePrice?: number;
  image: string;
  rating: number;
  reviews: number;
  badge: string;
  trending: boolean;
  tenantId:string;
}



// Get products for current tenant or fallback to default
function getHotPicksForTenant(tenantId: string ): HotProduct[] {
  return // TODO: Replace with real API call
  const products: any[] = [];
}


// Helper function to get product image URL
function getProductImageUrl(productName: string, productId: number): string {
  const imageMap: { [key: string]: string } = {
    // Premium Electronics & Tech
    'iphone': 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400&h=400&fit=crop',
    'macbook': 'https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=400&h=400&fit=crop',
    'airpods': 'https://images.unsplash.com/photo-1600294037681-c80b4cb5b434?w=400&h=400&fit=crop',
    'samsung': 'https://images.unsplash.com/photo-1610945265064-0e34e5519bbf?w=400&h=400&fit=crop',
    'sony': 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop',
    'ipad': 'https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=400&h=400&fit=crop',
    'nintendo': 'https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=400&fit=crop',
    'tesla': 'https://images.unsplash.com/photo-1560958089-b8a1929cea89?w=400&h=400&fit=crop',

    // Fashion & Lifestyle Trending
    'designer-sneakers': 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=400&fit=crop',
    'luxury-smartwatch': 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop',
    'wireless-earbuds': 'https://images.unsplash.com/photo-1590658268037-6bf12165a8df?w=400&h=400&fit=crop',
    'designer-handbag': 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=400&fit=crop',
    'limited-jacket': 'https://images.unsplash.com/photo-1544966503-7cc5ac882d5f?w=400&h=400&fit=crop',
    'perfume-set': 'https://images.unsplash.com/photo-1541643600914-78b084683601?w=400&h=400&fit=crop',
    'premium-sunglasses': 'https://images.unsplash.com/photo-1572635196237-14b3f281503f?w=400&h=400&fit=crop',
    'designer-watch': 'https://images.unsplash.com/photo-1523170335258-f5c6c6bd6eaf?w=400&h=400&fit=crop',

    // Home & Lifestyle Trending
    'smart-home-hub': 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop',
    'air-purifier': 'https://images.unsplash.com/photo-1585771724684-38269d6639fd?w=400&h=400&fit=crop',
    'robot-vacuum': 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop',
    'smart-thermostat': 'https://images.unsplash.com/photo-1545259741-2ea3ebf61fa0?w=400&h=400&fit=crop',
    'coffee-machine': 'https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=400&h=400&fit=crop',
    'security-camera': 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop',
    'luxury-bedding': 'https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=400&h=400&fit=crop',
    'smart-garden': 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=400&fit=crop'
  };

  const key = productName.toLowerCase().includes('iphone') ? 'iphone' :
              productName.toLowerCase().includes('macbook') ? 'macbook' :
              productName.toLowerCase().includes('airpods') ? 'airpods' :
              productName.toLowerCase().includes('samsung') ? 'samsung' :
              productName.toLowerCase().includes('sony') ? 'sony' :
              productName.toLowerCase().includes('ipad') ? 'ipad' :
              productName.toLowerCase().includes('nintendo') ? 'nintendo' :
              productName.toLowerCase().includes('tesla') ? 'tesla' :
              productName.toLowerCase().includes('designer') && productName.toLowerCase().includes('sneaker') ? 'designer-sneakers' :
              productName.toLowerCase().includes('luxury') && productName.toLowerCase().includes('smartwatch') ? 'luxury-smartwatch' :
              productName.toLowerCase().includes('wireless') && productName.toLowerCase().includes('earbuds') ? 'wireless-earbuds' :
              productName.toLowerCase().includes('designer') && productName.toLowerCase().includes('handbag') ? 'designer-handbag' :
              productName.toLowerCase().includes('limited') && productName.toLowerCase().includes('jacket') ? 'limited-jacket' :
              productName.toLowerCase().includes('perfume') && productName.toLowerCase().includes('set') ? 'perfume-set' :
              productName.toLowerCase().includes('premium') && productName.toLowerCase().includes('sunglasses') ? 'premium-sunglasses' :
              productName.toLowerCase().includes('designer') && productName.toLowerCase().includes('watch') ? 'designer-watch' :
              productName.toLowerCase().includes('smart') && productName.toLowerCase().includes('home') ? 'smart-home-hub' :
              productName.toLowerCase().includes('air') && productName.toLowerCase().includes('purifier') ? 'air-purifier' :
              productName.toLowerCase().includes('robot') && productName.toLowerCase().includes('vacuum') ? 'robot-vacuum' :
              productName.toLowerCase().includes('smart') && productName.toLowerCase().includes('thermostat') ? 'smart-thermostat' :
              productName.toLowerCase().includes('coffee') && productName.toLowerCase().includes('machine') ? 'coffee-machine' :
              productName.toLowerCase().includes('security') && productName.toLowerCase().includes('camera') ? 'security-camera' :
              productName.toLowerCase().includes('luxury') && productName.toLowerCase().includes('bedding') ? 'luxury-bedding' :
              productName.toLowerCase().includes('smart') && productName.toLowerCase().includes('garden') ? 'smart-garden' :
              'iphone';

  return imageMap[key] || imageMap['iphone'];
}

// Helper function to render star rating
function renderStarRating(rating: number) {
  const stars = [];
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 !== 0;

  for (let i = 0; i < fullStars; i++) {
    stars.push(
      <StarIconSolid key={i} className="h-4 w-4 text-yellow-400" />
    );
  }

  if (hasHalfStar) {
    stars.push(
      <div key="half" className="relative">
        <StarIcon className="h-4 w-4 text-yellow-400" />
        <StarIconSolid className="h-4 w-4 text-yellow-400 absolute top-0 left-0 w-1/2 overflow-hidden" />
      </div>
    );
  }

  const remainingStars = 5 - Math.ceil(rating);
  for (let i = 0; i < remainingStars; i++) {
    stars.push(
      <StarIcon key={`empty-${i}`} className="h-4 w-4 text-gray-300" />
    );
  }

  return stars;
}

export default function HotPicks() {
  const { tenantId } = useTenant();

  // Get tenant-specific products
  const products =  getHotPicksForTenant(tenantId as string);

  return (
    <section className="py-16 bg-gradient-to-br from-orange-50 to-yellow-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center px-4 py-2 bg-orange-100 text-orange-800 rounded-full text-sm font-medium mb-4">
            <FireIcon className="h-4 w-4 mr-2" />
            Trending Now
          </div>
          <div className="flex items-center justify-center gap-4 mb-4">
            <h2 className="text-3xl font-bold text-gray-900">Hot Picks</h2>

          </div>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Discover what's trending! These products are flying off our shelves.
          </p>
        </div>

        {/* Products Carousel */}
        <div className="relative">
          <Swiper
            modules={[Navigation, Pagination, Autoplay]}
            spaceBetween={24}
            slidesPerView={1}
            navigation={{
              prevEl: '.hot-picks-prev',
              nextEl: '.hot-picks-next',
            }}
            pagination={{
              clickable: true,
              el: '.hot-picks-pagination',
            }}
            autoplay={{
              delay: 3000,
              disableOnInteraction: false,
            }}
            breakpoints={{
              640: {
                slidesPerView: 2,
              },
              768: {
                slidesPerView: 3,
              },
              1024: {
                slidesPerView: 4,
              },
            }}
            className="pb-12"
          >
            {products.slice(0, 8).map((product) => (
              <SwiperSlide key={product.id} className="h-auto">
                <div className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden group h-full flex flex-col">
                  {/* Product Image */}
                  <div className="relative aspect-square overflow-hidden">
                    <Image
                      src={getProductImageUrl(product.name, product.id)}
                      alt={product.name}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    {/* Badge */}
                    <div className={`absolute top-3 left-3 px-2 py-1 rounded-md text-xs font-semibold ${
                      product.badge === 'Hot'
                        ? 'bg-red-500 text-white'
                        : 'bg-orange-500 text-white'
                    }`}>
                      {product.badge}
                    </div>
                    {/* Trending Icon */}
                    {product.trending && (
                      <div className="absolute top-3 right-3 bg-yellow-400 text-yellow-900 rounded-full p-1">
                        <FireIcon className="h-3 w-3" />
                      </div>
                    )}
                    {/* Wishlist Button */}
                    <button className="absolute top-3 right-12 bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full p-2 transition-all duration-200 opacity-0 group-hover:opacity-100">
                      <HeartIcon className="h-4 w-4 text-gray-600 hover:text-red-500" />
                    </button>
                  </div>

                  {/* Product Info */}
                  <div className="p-4 flex flex-col flex-grow">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2 min-h-[3.5rem]">
                      <Link
                        href={`/product/hot-picks/general?product-name=${product.slug}`}
                        className="hover:text-blue-600 transition-colors"
                      >
                        {product.name}
                      </Link>
                    </h3>

                    {/* Rating */}
                    <div className="flex items-center space-x-1 mb-3">
                      <div className="flex items-center">
                        {renderStarRating(product.rating)}
                      </div>
                      <span className="text-sm text-gray-500">({product.reviews})</span>
                    </div>

                    {/* Price */}
                    <div className="flex items-center space-x-2 mb-4">
                      {product.salePrice ? (
                        <>
                          <span className="text-xl font-bold text-green-600">₹{product.salePrice}</span>
                          <span className="text-sm text-gray-500 line-through">₹{product.price}</span>
                        </>
                      ) : (
                        <span className="text-xl font-bold text-gray-900">₹{product.price}</span>
                      )}
                    </div>

                    {/* View Product Button */}
                    <Link
                      href={`/product/hot-picks/general?product-name=${product.slug}`}
                      className="w-full bg-orange-600 hover:bg-orange-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2 mt-auto"
                    >
                      <EyeIcon className="h-4 w-4" />
                      <span>View Product</span>
                    </Link>
                  </div>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>

          {/* Custom Navigation Buttons */}
          <button className="hot-picks-prev absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 z-10 bg-white shadow-lg hover:shadow-xl rounded-full p-3 transition-all duration-200">
            <ChevronLeftIcon className="h-5 w-5 text-gray-600" />
          </button>
          <button className="hot-picks-next absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-4 z-10 bg-white shadow-lg hover:shadow-xl rounded-full p-3 transition-all duration-200">
            <ChevronRightIcon className="h-5 w-5 text-gray-600" />
          </button>

          {/* Custom Pagination */}
          <div className="hot-picks-pagination flex justify-center mt-8"></div>
        </div>

        {/* View All Button */}
        <div className="text-center mt-8">
          <Link
            href="/deals?type=hot-picks"
            className="inline-flex items-center px-6 py-3 bg-orange-600 hover:bg-orange-700 text-white font-medium rounded-lg transition-colors duration-200"
          >
            View All Hot Picks
            <ChevronRightIcon className="ml-2 h-4 w-4" />
          </Link>
        </div>
      </div>
    </section>
  );
}
