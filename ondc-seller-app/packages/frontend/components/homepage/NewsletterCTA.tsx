'use client';

import React, { useState } from 'react';
import { Mail, Check, Gift, Bell, Star, Percent } from 'lucide-react';
// TODO: Replace with real API integration

const NewsletterCTA: React.FC = () => {
  const [email, setEmail] = useState('');
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  const newsletterData = getNewsletterCTAByTenant(defaultTenantId);

  if (!newsletterData) {
    return null;
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;

    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsSubscribed(true);
      setIsLoading(false);
      setEmail('');
    }, 1500);
  };

  const benefitIcons = [
    { icon: Gift, color: 'text-pink-600', bg: 'bg-pink-100' },
    { icon: Bell, color: 'text-blue-600', bg: 'bg-blue-100' },
    { icon: Star, color: 'text-yellow-600', bg: 'bg-yellow-100' },
    { icon: Percent, color: 'text-green-600', bg: 'bg-green-100' },
  ];

  if (isSubscribed) {
    return (
      <section className="py-16 relative overflow-hidden">
        {/* Background Image */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{ backgroundImage: `url(${newsletterData.backgroundImage})` }}
        />
        
        {/* Overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-green-600/90 to-emerald-600/90" />
        
        {/* Content */}
        <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8">
            <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-6">
              <Check className="w-8 h-8 text-green-600" />
            </div>
            <h2 className="text-3xl font-bold text-white mb-4">
              Welcome to Our Community!
            </h2>
            <p className="text-xl text-white/90 mb-6">
              Thank you for subscribing! Check your email for a special welcome offer.
            </p>
            <div className="flex items-center justify-center space-x-6 text-white/80">
              <div className="flex items-center">
                <Mail className="w-5 h-5 mr-2" />
                <span>Confirmation sent</span>
              </div>
              <div className="flex items-center">
                <Gift className="w-5 h-5 mr-2" />
                <span>Welcome offer included</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 relative overflow-hidden">
      {/* Background Image */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{ backgroundImage: `url(${newsletterData.backgroundImage})` }}
      />
      
      {/* Overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-600/90 to-purple-600/90" />
      
      {/* Decorative Elements */}
      <div className="absolute top-10 left-10 w-20 h-20 bg-white/10 rounded-full blur-xl" />
      <div className="absolute bottom-10 right-10 w-32 h-32 bg-white/10 rounded-full blur-2xl" />
      <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-white/5 rounded-full blur-lg" />
      
      {/* Content */}
      <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full mb-6">
            <Mail className="w-8 h-8 text-white" />
          </div>
          
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            {newsletterData.title}
          </h2>
          <p className="text-xl text-white/90 mb-2">
            {newsletterData.subtitle}
          </p>
          <p className="text-lg text-white/80 max-w-2xl mx-auto">
            {newsletterData.description}
          </p>
        </div>

        {/* Benefits Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {newsletterData.benefits.map((benefit, index) => {
            const IconComponent = benefitIcons[index]?.icon || Gift;
            const iconColor = benefitIcons[index]?.color || 'text-pink-600';
            const iconBg = benefitIcons[index]?.bg || 'bg-pink-100';
            
            return (
              <div
                key={index}
                className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center hover:bg-white/20 transition-all duration-300"
              >
                <div className={`w-12 h-12 ${iconBg} rounded-full flex items-center justify-center mx-auto mb-3`}>
                  <IconComponent className={`w-6 h-6 ${iconColor}`} />
                </div>
                <p className="text-white font-medium">{benefit}</p>
              </div>
            );
          })}
        </div>

        {/* Newsletter Signup Form */}
        <div className="max-w-md mx-auto">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-3">
              <div className="flex-1">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder={newsletterData.placeholderText}
                  className="w-full px-4 py-3 rounded-lg border-0 bg-white/90 backdrop-blur-sm text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-white focus:bg-white transition-all duration-200"
                  required
                />
              </div>
              <button
                type="submit"
                disabled={isLoading}
                className="px-6 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-600 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                    </svg>
                    Subscribing...
                  </div>
                ) : (
                  newsletterData.buttonText
                )}
              </button>
            </div>
          </form>

          {/* Privacy Notice */}
          <p className="text-sm text-white/70 text-center mt-4">
            By subscribing, you agree to our{' '}
            <a href="/privacy-policy" className="underline hover:text-white transition-colors">
              Privacy Policy
            </a>{' '}
            and consent to receive marketing emails. Unsubscribe anytime.
          </p>
        </div>

        {/* Social Proof */}
        <div className="text-center mt-8">
          <p className="text-white/80 text-sm">
            Join 50,000+ subscribers who get exclusive deals first
          </p>
          <div className="flex items-center justify-center mt-2">
            <div className="flex -space-x-2">
              {[1, 2, 3, 4, 5].map((i) => (
                <div
                  key={i}
                  className="w-8 h-8 bg-white/20 rounded-full border-2 border-white/30"
                />
              ))}
            </div>
            <span className="ml-3 text-white/80 text-sm">+50K more</span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default NewsletterCTA;
