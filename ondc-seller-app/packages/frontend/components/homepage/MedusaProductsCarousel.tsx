'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { useMedusaBackendProducts } from '@/hooks/useMedusaBackendProducts';
import { useMedusaCartContext } from '@/hooks/useMedusaCart';
import { MedusaProduct } from '@/lib/medusa-backend-api';

interface MedusaProductsCarouselProps {
  title: string;
  maxProducts?: number;
  showViewAll?: boolean;
  viewAllLink?: string;
  productType?: 'latest' | 'featured' | 'top_deals' | 'hot_picks';
}

export default function MedusaProductsCarousel({
  title,
  maxProducts = 8,
  showViewAll = true,
  viewAllLink = '/products',
  productType = 'latest'
}: MedusaProductsCarouselProps) {
  const { 
    products, 
    loading, 
    error, 
    fetchProducts,
    getFeaturedProducts,
    getTopDeals,
    getHotPicks 
  } = useMedusaBackendProducts();
  
  const { addItem, isAddingItem } = useMedusaCartContext();
  const [addingProductId, setAddingProductId] = useState<string | null>(null);

  useEffect(() => {
    const loadProducts = async () => {
      try {
        switch (productType) {
          case 'featured':
            await getFeaturedProducts(maxProducts);
            break;
          case 'top_deals':
            await getTopDeals(maxProducts);
            break;
          case 'hot_picks':
            await getHotPicks(maxProducts);
            break;
          default:
            await fetchProducts({ limit: maxProducts });
            break;
        }
      } catch (error) {
        console.error(`Error loading ${productType} products:`, error);
      }
    };

    loadProducts();
  }, [productType, maxProducts, fetchProducts, getFeaturedProducts, getTopDeals, getHotPicks]);

  const handleAddToCart = async (product: MedusaProduct) => {
    if (!product.variants || product.variants.length === 0) {
      console.error('Product has no variants');
      return;
    }

    const defaultVariant = product.variants[0];
    setAddingProductId(product.id);

    try {
      await addItem(defaultVariant.id, 1);
      // Show success feedback
      console.log(`Added ${product.title} to cart`);
    } catch (error) {
      console.error('Error adding to cart:', error);
    } finally {
      setAddingProductId(null);
    }
  };

  const formatPrice = (variant: any) => {
    if (variant.prices && variant.prices.length > 0) {
      const price = variant.prices[0];
      return `€${(price.amount / 100).toFixed(2)}`;
    }
    return 'Price not available';
  };

  if (loading) {
    return (
      <section className="py-12 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900">{title}</h2>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {Array.from({ length: maxProducts }).map((_, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md p-4 animate-pulse">
                <div className="w-full h-48 bg-gray-300 rounded-lg mb-4"></div>
                <div className="h-4 bg-gray-300 rounded mb-2"></div>
                <div className="h-4 bg-gray-300 rounded w-2/3 mb-4"></div>
                <div className="h-8 bg-gray-300 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-12 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">{title}</h2>
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-red-600">Error loading products: {error}</p>
              <button 
                onClick={() => window.location.reload()}
                className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      </section>
    );
  }

  if (!products || products.length === 0) {
    return (
      <section className="py-12 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">{title}</h2>
            <p className="text-gray-600">No products available at the moment.</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-12 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-3xl font-bold text-gray-900">{title}</h2>
          {showViewAll && (
            <Link 
              href={viewAllLink}
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              View All →
            </Link>
          )}
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {products.slice(0, maxProducts).map((product) => (
            <div key={product.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
              {/* Product Image */}
              <div className="relative h-48 bg-gray-200">
                {product.thumbnail ? (
                  <img
                    src={product.thumbnail}
                    alt={product.title}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-gray-400">
                    No Image
                  </div>
                )}
              </div>

              {/* Product Info */}
              <div className="p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                  {product.title}
                </h3>
                
                {product.description && (
                  <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                    {product.description}
                  </p>
                )}

                {/* Price */}
                {product.variants && product.variants.length > 0 && (
                  <p className="text-xl font-bold text-blue-600 mb-3">
                    {formatPrice(product.variants[0])}
                  </p>
                )}

                {/* Actions */}
                <div className="flex space-x-2">
                  <Link
                    href={`/products/${product.handle || product.id}`}
                    className="flex-1 bg-gray-100 text-gray-800 px-3 py-2 rounded text-center text-sm font-medium hover:bg-gray-200 transition-colors"
                  >
                    View Product
                  </Link>
                  
                  <button
                    onClick={() => handleAddToCart(product)}
                    disabled={isAddingItem || addingProductId === product.id}
                    className="flex-1 bg-blue-600 text-white px-3 py-2 rounded text-sm font-medium hover:bg-blue-700 disabled:bg-blue-400 transition-colors"
                  >
                    {addingProductId === product.id ? 'Adding...' : 'Add to Cart'}
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Show View All button if there are more products */}
        {showViewAll && products.length >= maxProducts && (
          <div className="text-center mt-8">
            <Link
              href={viewAllLink}
              className="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
            >
              View All {title}
            </Link>
          </div>
        )}
      </div>
    </section>
  );
}
