'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Clock, Tag } from 'lucide-react';
// TODO: Replace with real API integration

const PromotionalBanners: React.FC = () => {
  const banners = getPromotionalBannersByTenant(defaultTenantId);
  const [timeLeft, setTimeLeft] = useState<{ [key: string]: string }>({});

  useEffect(() => {
    const calculateTimeLeft = () => {
      const newTimeLeft: { [key: string]: string } = {};

      banners.forEach((banner) => {
        const difference = new Date(banner.validUntil).getTime() - new Date().getTime();

        if (difference > 0) {
          const days = Math.floor(difference / (1000 * 60 * 60 * 24));
          const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
          const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));

          if (days > 0) {
            newTimeLeft[banner.id] = `${days}d ${hours}h ${minutes}m`;
          } else if (hours > 0) {
            newTimeLeft[banner.id] = `${hours}h ${minutes}m`;
          } else {
            newTimeLeft[banner.id] = `${minutes}m`;
          }
        } else {
          newTimeLeft[banner.id] = 'Expired';
        }
      });

      setTimeLeft(prevTimeLeft => {
        // Only update if the values have actually changed
        const hasChanged = Object.keys(newTimeLeft).some(key =>
          prevTimeLeft[key] !== newTimeLeft[key]
        );
        return hasChanged ? newTimeLeft : prevTimeLeft;
      });
    };

    calculateTimeLeft();
    const timer = setInterval(calculateTimeLeft, 60000); // Update every minute

    return () => clearInterval(timer);
  }, []); // Remove banners dependency to prevent infinite re-renders

  if (banners.length === 0) {
    return null;
  }

  return (
    <section className="py-16 bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Special Offers
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Don't miss out on these limited-time deals and exclusive promotions
          </p>
        </div>

        {/* Promotional Banners Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {banners.map((banner) => (
            <div
              key={banner.id}
              className="relative overflow-hidden rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group"
            >
              {/* Background Image */}
              <div
                className="absolute inset-0 bg-cover bg-center bg-no-repeat transform group-hover:scale-105 transition-transform duration-700"
                style={{ backgroundImage: `url(${banner.image})` }}
              />
              
              {/* Gradient Overlay */}
              <div className={`absolute inset-0 ${banner.backgroundColor} opacity-90`} />
              
              {/* Content */}
              <div className="relative z-10 p-8 h-64 flex flex-col justify-between text-white">
                <div>
                  {/* Discount Badge */}
                  {banner.discountPercentage && (
                    <div className="inline-flex items-center mb-4">
                      <div className="bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 flex items-center">
                        <Tag className="w-4 h-4 mr-2" />
                        <span className="font-bold text-lg">
                          {banner.discountPercentage}% OFF
                        </span>
                      </div>
                    </div>
                  )}

                  {/* Title and Subtitle */}
                  <h3 className="text-2xl md:text-3xl font-bold mb-2">
                    {banner.title}
                  </h3>
                  <p className="text-lg font-medium mb-3 opacity-90">
                    {banner.subtitle}
                  </p>
                  <p className="text-sm opacity-80 mb-4">
                    {banner.description}
                  </p>
                </div>

                <div className="flex items-center justify-between">
                  {/* CTA Button */}
                  <Link
                    href={banner.buttonLink}
                    className="inline-flex items-center px-6 py-3 bg-white text-gray-900 font-semibold rounded-lg hover:bg-gray-100 transition-colors duration-200 shadow-lg"
                  >
                    {banner.buttonText}
                    <svg
                      className="ml-2 w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </Link>

                  {/* Countdown Timer */}
                  {timeLeft[banner.id] && timeLeft[banner.id] !== 'Expired' && (
                    <div className="flex items-center bg-white/20 backdrop-blur-sm rounded-lg px-3 py-2">
                      <Clock className="w-4 h-4 mr-2" />
                      <span className="text-sm font-medium">
                        {timeLeft[banner.id]} left
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Decorative Elements */}
              <div className="absolute top-4 right-4 w-16 h-16 bg-white/10 rounded-full blur-xl" />
              <div className="absolute bottom-4 left-4 w-12 h-12 bg-white/10 rounded-full blur-lg" />
            </div>
          ))}
        </div>

        {/* Additional Promotional Info */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-6 bg-white rounded-lg shadow-sm">
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg className="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-1">Price Match Guarantee</h3>
            <p className="text-sm text-gray-600">We'll match any competitor's price</p>
          </div>
          
          <div className="text-center p-6 bg-white rounded-lg shadow-sm">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg className="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-1">Easy Returns</h3>
            <p className="text-sm text-gray-600">30-day hassle-free returns</p>
          </div>
          
          <div className="text-center p-6 bg-white rounded-lg shadow-sm">
            <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg className="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-1">Secure Payments</h3>
            <p className="text-sm text-gray-600">SSL encrypted transactions</p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PromotionalBanners;
