'use client';

import React, { useState, useEffect } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Autoplay } from 'swiper/modules';
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  StarIcon,
  HeartIcon,
} from '@heroicons/react/24/outline';
import { StarIcon as StarSolidIcon, HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';
import Link from 'next/link';
import Image from 'next/image';
import { getProducts } from '@/lib/strapi-api';
// TODO: Replace with real API integration
const defaultTenantId = '20';
import { useTenant } from '@/contexts/TenantContext';
import WishlistButton from '../wishlist/WishlistButton';
import 'swiper/css';
import 'swiper/css/navigation';

interface Product {
  id: number;
  name: string;
  slug: string;
  price: number;
  originalPrice?: number;
  image: string;
  rating: number;
  reviewCount: number;
  category: string;
  isWishlisted: boolean;
  badge?: string;
}


// Helper function to get product image URL
function getProductImageUrl(productName: string, productId: number|string): string {
  const imageMap: { [key: string]: string } = {
    // Electronics & Tech
    'headphones': 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop',
    'smartwatch': 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop',
    'camera-lens': 'https://images.unsplash.com/photo-1606983340126-99ab4feaa64a?w=400&h=400&fit=crop',
    'smart-tv': 'https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?w=400&h=400&fit=crop',

    // Home & Kitchen
    'coffee-maker': 'https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=400&h=400&fit=crop',
    'office-chair': 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop',
    'dining-table': 'https://images.unsplash.com/photo-1549497538-303791108f95?w=400&h=400&fit=crop',
    'luxury-sofa': 'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=400&h=400&fit=crop',

    // Fashion & Accessories
    'tshirt': 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=400&fit=crop',
    'denim-jacket': 'https://images.unsplash.com/photo-1544966503-7cc5ac882d5f?w=400&h=400&fit=crop',
    'leather-boots': 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400&h=400&fit=crop',
    'evening-dress': 'https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=400&h=400&fit=crop'
  };

  const key = productName.toLowerCase().includes('headphones') ? 'headphones' :
              productName.toLowerCase().includes('watch') && !productName.toLowerCase().includes('smart') ? 'smartwatch' :
              productName.toLowerCase().includes('camera') && productName.toLowerCase().includes('lens') ? 'camera-lens' :
              productName.toLowerCase().includes('smart') && productName.toLowerCase().includes('tv') ? 'smart-tv' :
              productName.toLowerCase().includes('coffee') && productName.toLowerCase().includes('maker') ? 'coffee-maker' :
              productName.toLowerCase().includes('office') && productName.toLowerCase().includes('chair') ? 'office-chair' :
              productName.toLowerCase().includes('dining') && productName.toLowerCase().includes('table') ? 'dining-table' :
              productName.toLowerCase().includes('luxury') && productName.toLowerCase().includes('sofa') ? 'luxury-sofa' :
              productName.toLowerCase().includes('tshirt') || productName.toLowerCase().includes('t-shirt') ? 'tshirt' :
              productName.toLowerCase().includes('denim') && productName.toLowerCase().includes('jacket') ? 'denim-jacket' :
              productName.toLowerCase().includes('leather') && productName.toLowerCase().includes('boots') ? 'leather-boots' :
              productName.toLowerCase().includes('evening') && productName.toLowerCase().includes('dress') ? 'evening-dress' :
              'headphones';

  return imageMap[key] || imageMap['headphones'];
}

// Helper function to extract text from rich text content
function extractTextFromRichText(richText: any): string {
  if (!richText) return '';

  if (Array.isArray(richText)) {
    return richText
      .map((block: any) => {
        if (block.children && Array.isArray(block.children)) {
          return block.children.map((child: any) => child.text || '').join('');
        }
        return '';
      })
      .join(' ');
  }

  if (typeof richText === 'string') {
    return richText;
  }

  return '';
}

// Helper function to transform Strapi product data
function transformStrapiProduct(strapiProduct: any): Product {
  const attributes = strapiProduct.attributes || strapiProduct;
  const name = attributes.name || strapiProduct.name || 'Unknown Product';
  const price = attributes.price || strapiProduct.price || 0;
  const salePrice = attributes.sale_price || strapiProduct.sale_price;

  return {
    id: strapiProduct.id,
    name,
    slug: attributes.slug || name.toLowerCase().replace(/\s+/g, '-'),
    price: salePrice || price,
    originalPrice: salePrice ? price : undefined,
    image: '/images/products/placeholder.svg', // Default image for now
    rating: 4.0 + Math.random() * 1, // Random rating between 4-5
    reviewCount: Math.floor(Math.random() * 300) + 10,
    category: 'Featured',
    isWishlisted: false,
    badge: attributes.featured || strapiProduct.featured ? 'Featured' : undefined,
  };
}

export default function FeaturedProducts() {
  const { tenantId } = useTenant();

  const [isLoading, setIsLoading] = useState<boolean>(true);


  // TODO: Replace with real API call
  const products: any[] = [];

 

  // const toggleWishlist = (productId: number) => {
  //   setProducts(prev =>
  //     prev.map(product =>
  //       product.id === productId ? { ...product, isWishlisted: !product.isWishlisted } : product
  //     )
  //   );
  // };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(<StarSolidIcon key={i} className="h-4 w-4 text-yellow-400" />);
    }

    if (hasHalfStar) {
      stars.push(<StarIcon key="half" className="h-4 w-4 text-yellow-400" />);
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(<StarIcon key={`empty-${i}`} className="h-4 w-4 text-gray-300" />);
    }

    return stars;
  };

  return (
    <section className="py-16 bg-gray-50" data-testid="featured-products">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="flex items-center justify-between mb-12">
          <div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Featured Products</h2>
            <p className="text-lg text-gray-600">Handpicked products just for you</p>
          </div>

          {/* Navigation Arrows */}
          <div className="hidden md:flex space-x-2">
            <button className="featured-prev p-2 rounded-full bg-white shadow-md hover:shadow-lg text-gray-600 hover:text-gray-900 transition-all duration-200">
              <ChevronLeftIcon className="h-5 w-5" />
            </button>
            <button className="featured-next p-2 rounded-full bg-white shadow-md hover:shadow-lg text-gray-600 hover:text-gray-900 transition-all duration-200">
              <ChevronRightIcon className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md overflow-hidden">
                <div className="aspect-square bg-gray-200 animate-pulse"></div>
                <div className="p-4">
                  <div className="h-4 bg-gray-200 rounded mb-2 animate-pulse"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2 animate-pulse"></div>
                  <div className="h-6 bg-gray-200 rounded w-1/2 animate-pulse"></div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Products Carousel */}
        {!isLoading && (
          <Swiper
            modules={[Navigation, Autoplay]}
            spaceBetween={24}
            navigation={{
              prevEl: '.featured-prev',
              nextEl: '.featured-next',
            }}
            autoplay={{
              delay: 5000,
              disableOnInteraction: false,
            }}
            breakpoints={{
              640: {
                slidesPerView: 2,
              },
              768: {
                slidesPerView: 3,
              },
              1024: {
                slidesPerView: 4,
              },
            }}
            slidesPerView={1}
            className="pb-4"
          >
            {products.map(product => (
              <SwiperSlide key={product.id}>
                <div className="bg-white rounded-lg shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden group">
                  {/* Product Image */}
                  <div className="relative aspect-square overflow-hidden">
                    <img
                      src={getProductImageUrl(product.name, product.id)}
                      alt={product.name}
                      // fill
                      className="object-cover transition-transform duration-300 group-hover:scale-105"
                      // sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                      onError={e => {
                        console.error(`Failed to load featured product image: ${product.image}`);
                        e.currentTarget.src = '/images/products/placeholder.svg';
                      }}
                    />

                    {/* Badge */}
                    {product.badge && (
                      <div className="absolute top-3 left-3 bg-blue-600 text-white text-xs font-semibold px-2 py-1 rounded">
                        {product.badge}
                      </div>
                    )}

                    {/* Wishlist Button */}
                    <div className="absolute top-3 right-3">
                      <WishlistButton
                        productId={product.id.toString()}
                        variant="icon"
                        size="sm"
                        className="bg-white/80 hover:bg-white"
                      />
                    </div>
                  </div>

                  {/* Product Info */}
                  <div className="p-4">
                    <div className="text-sm text-gray-500 mb-1">{product.category}</div>
                    <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                      {product.name}
                    </h3>

                    {/* Rating */}
                    <div className="flex items-center mb-3">
                      <div className="flex items-center">{renderStars(product.rating)}</div>
                      <span className="ml-2 text-sm text-gray-500">({product.reviewCount})</span>
                    </div>

                    {/* Price */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg font-bold text-gray-900">₹{product.price.toLocaleString()}</span>
                        {product.originalPrice && (
                          <span className="text-sm text-gray-500 line-through">
                            ₹{product.originalPrice.toLocaleString()}
                          </span>
                        )}
                      </div>
                    </div>

                    {/* View Product Button */}
                    <Link
                      href={`/products/${product.slug}`}
                      className="mt-4 w-full bg-blue-600 text-white text-center py-2 rounded-md hover:bg-blue-700 transition-colors duration-200 block"
                      data-testid="product-link"
                    >
                      View Product
                    </Link>
                  </div>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        )}

        {/* View All Featured Products Button */}
        <div className="text-center mt-12">
          <Link
            href="/deals?type=featured-products"
            className="inline-flex items-center px-8 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-400 transition-colors duration-200"
          >
            View All Featured Products
            <ChevronRightIcon className="ml-2 h-5 w-5" />
          </Link>
        </div>
      </div>
    </section>
  );
}
