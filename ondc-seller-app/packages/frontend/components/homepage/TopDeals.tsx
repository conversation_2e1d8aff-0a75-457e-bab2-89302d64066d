'use client';

import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination } from 'swiper/modules';
import { ChevronLeftIcon, ChevronRightIcon, StarIcon, HeartIcon, EyeIcon } from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';
import Link from 'next/link';
import Image from 'next/image';
import { useTenant } from '@/contexts/TenantContext';
// TODO: Replace with real API integration
interface TopDealProduct {
  id: number;
  name: string;
  slug: string;
  originalPrice: number;
  salePrice: number;
  discount: number;
  rating: number;
  reviews: number;
  image: string;
  category: string;
  inStock: boolean;
}
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

// Helper function to get product image URL
function getProductImageUrl(productName: string, productId: number): string {
  const imageMap: { [key: string]: string } = {
    // Electronics & Tech
    'headphones': 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop',
    'smartwatch': 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop',
    'speaker': 'https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=400&h=400&fit=crop',
    'charger': 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=400&fit=crop',
    'keyboard': 'https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=400&h=400&fit=crop',
    'webcam': 'https://images.unsplash.com/photo-1587825140708-dfaf72ae4b04?w=400&h=400&fit=crop',

    // Home & Furniture
    'chair': 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop',
    'lamp': 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop',
    'mattress': 'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=400&h=400&fit=crop',
    'coffee-table': 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop',

    // Fashion & Accessories
    'handbag': 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=400&fit=crop',
    'running-shoes': 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400&h=400&fit=crop',
    'luxury-watch': 'https://images.unsplash.com/photo-1523170335258-f5c6c6bd6eaf?w=400&h=400&fit=crop',
    'scarf': 'https://images.unsplash.com/photo-1601924994987-69e26d50dc26?w=400&h=400&fit=crop',
    'denim-jacket': 'https://images.unsplash.com/photo-1544966503-7cc5ac882d5f?w=400&h=400&fit=crop',
    'leather-boots': 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400&h=400&fit=crop',
    'evening-dress': 'https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=400&h=400&fit=crop',
    'sunglasses': 'https://images.unsplash.com/photo-1572635196237-14b3f281503f?w=400&h=400&fit=crop',

    // Kitchen & Home Appliances
    'blender': 'https://images.unsplash.com/photo-1570197788417-0e82375c9371?w=400&h=400&fit=crop',
    'cookware-set': 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=400&fit=crop',
    'security-system': 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop',
    'laptop-stand': 'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=400&fit=crop'
  };

  const key = productName.toLowerCase().includes('headphones') ? 'headphones' :
              productName.toLowerCase().includes('watch') && !productName.toLowerCase().includes('luxury') ? 'smartwatch' :
              productName.toLowerCase().includes('luxury') && productName.toLowerCase().includes('watch') ? 'luxury-watch' :
              productName.toLowerCase().includes('speaker') ? 'speaker' :
              productName.toLowerCase().includes('charging') ? 'charger' :
              productName.toLowerCase().includes('keyboard') ? 'keyboard' :
              productName.toLowerCase().includes('webcam') ? 'webcam' :
              productName.toLowerCase().includes('chair') ? 'chair' :
              productName.toLowerCase().includes('lamp') ? 'lamp' :
              productName.toLowerCase().includes('mattress') ? 'mattress' :
              productName.toLowerCase().includes('coffee') && productName.toLowerCase().includes('table') ? 'coffee-table' :
              productName.toLowerCase().includes('handbag') ? 'handbag' :
              productName.toLowerCase().includes('running') && productName.toLowerCase().includes('shoes') ? 'running-shoes' :
              productName.toLowerCase().includes('scarf') ? 'scarf' :
              productName.toLowerCase().includes('denim') && productName.toLowerCase().includes('jacket') ? 'denim-jacket' :
              productName.toLowerCase().includes('leather') && productName.toLowerCase().includes('boots') ? 'leather-boots' :
              productName.toLowerCase().includes('evening') && productName.toLowerCase().includes('dress') ? 'evening-dress' :
              productName.toLowerCase().includes('sunglasses') ? 'sunglasses' :
              productName.toLowerCase().includes('blender') ? 'blender' :
              productName.toLowerCase().includes('cookware') ? 'cookware-set' :
              productName.toLowerCase().includes('security') && productName.toLowerCase().includes('system') ? 'security-system' :
              productName.toLowerCase().includes('laptop') && productName.toLowerCase().includes('stand') ? 'laptop-stand' :
              productName.toLowerCase().includes('smart') && productName.toLowerCase().includes('tv') ? 'smart-tv' :
              'headphones';

  return imageMap[key] || imageMap['headphones'];
}

// Helper function to render star rating
function renderStarRating(rating: number) {
  const stars = [];
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 !== 0;

  for (let i = 0; i < fullStars; i++) {
    stars.push(
      <StarIconSolid key={i} className="h-4 w-4 text-yellow-400" />
    );
  }

  if (hasHalfStar) {
    stars.push(
      <div key="half" className="relative">
        <StarIcon className="h-4 w-4 text-yellow-400" />
        <StarIconSolid className="h-4 w-4 text-yellow-400 absolute top-0 left-0 w-1/2 overflow-hidden" />
      </div>
    );
  }

  const remainingStars = 5 - Math.ceil(rating);
  for (let i = 0; i < remainingStars; i++) {
    stars.push(
      <StarIcon key={`empty-${i}`} className="h-4 w-4 text-gray-300" />
    );
  }

  return stars;
}

export default function TopDeals() {
  const { tenantId } = useTenant();

  // TODO: Replace with real API call
  const products: TopDealProduct[] = [];
  return (
    <section className="py-16 bg-gradient-to-br from-red-50 to-orange-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center px-4 py-2 bg-red-100 text-red-800 rounded-full text-sm font-medium mb-4">
            🔥 Limited Time Offers
          </div>
          <div className="flex items-center justify-center gap-4 mb-4">
            <h2 className="text-3xl font-bold text-gray-900">Top Deals</h2>
            {/* <Link
              href="/deals?type=top-deals"
              className="text-sm font-medium text-red-600 hover:text-red-700 transition-colors border border-red-200 hover:border-red-300 px-4 py-2 rounded-full"
            >
              View All Top Deals →
            </Link> */}
          </div>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Don't miss out on these incredible deals! Limited time offers with massive savings.
          </p>
        </div>

        {/* Products Carousel */}
        <div className="relative">
          <Swiper
            modules={[Navigation, Pagination]}
            spaceBetween={24}
            slidesPerView={1}
            navigation={{
              prevEl: '.top-deals-prev',
              nextEl: '.top-deals-next',
            }}
            pagination={{
              clickable: true,
              el: '.top-deals-pagination',
            }}
            breakpoints={{
              640: {
                slidesPerView: 2,
              },
              768: {
                slidesPerView: 3,
              },
              1024: {
                slidesPerView: 4,
              },
            }}
            className="pb-12"
          >
            {products.slice(0, 8).map((product) => (
              <SwiperSlide key={product.id} className="h-auto">
                <div className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden group h-full flex flex-col">
                  {/* Product Image */}
                  <div className="relative aspect-square overflow-hidden">
                    <Image
                      src={getProductImageUrl(product.name, product.id)}
                      alt={product.name}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    {/* Sale Badge */}
                    {product.badge && (
                      <div className="absolute top-3 left-3 bg-red-500 text-white px-2 py-1 rounded-md text-xs font-semibold">
                        {product.badge}
                      </div>
                    )}
                    {/* Discount Badge */}
                    <div className="absolute top-3 right-3 bg-orange-500 text-white px-2 py-1 rounded-md text-xs font-semibold">
                      -{product.discount}%
                    </div>
                    {/* Wishlist Button */}
                    <button className="absolute top-3 right-12 bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full p-2 transition-all duration-200 opacity-0 group-hover:opacity-100">
                      <HeartIcon className="h-4 w-4 text-gray-600 hover:text-red-500" />
                    </button>
                  </div>

                  {/* Product Info */}
                  <div className="p-4 flex flex-col flex-grow">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2 min-h-[3.5rem]">
                      <Link
                        href={`/product/top-deals/general?product-name=${product.slug}`}
                        className="hover:text-blue-600 transition-colors"
                      >
                        {product.name}
                      </Link>
                    </h3>

                    {/* Rating */}
                    <div className="flex items-center space-x-1 mb-3">
                      <div className="flex items-center">
                        {renderStarRating(product.rating)}
                      </div>
                      <span className="text-sm text-gray-500">({product.reviews})</span>
                    </div>

                    {/* Price */}
                    <div className="flex items-center space-x-2 mb-4">
                      <span className="text-xl font-bold text-red-600">₹{product.salePrice}</span>
                      <span className="text-sm text-gray-500 line-through">₹{product.originalPrice}</span>
                    </div>

                    {/* View Product Button */}
                    <Link
                      href={`/product/top-deals/general?product-name=${product.slug}`}
                      className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2 mt-auto"
                    >
                      <EyeIcon className="h-4 w-4" />
                      <span>View Product</span>
                    </Link>
                  </div>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>

          {/* Custom Navigation Buttons */}
          <button className="top-deals-prev absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 z-10 bg-white shadow-lg hover:shadow-xl rounded-full p-3 transition-all duration-200">
            <ChevronLeftIcon className="h-5 w-5 text-gray-600" />
          </button>
          <button className="top-deals-next absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-4 z-10 bg-white shadow-lg hover:shadow-xl rounded-full p-3 transition-all duration-200">
            <ChevronRightIcon className="h-5 w-5 text-gray-600" />
          </button>

          {/* Custom Pagination */}
          <div className="top-deals-pagination flex justify-center mt-8"></div>
        </div>

        {/* View All Button */}
        <div className="text-center mt-8">
          <Link
            href="/deals?type=top-deals"
            className="inline-flex items-center px-6 py-3 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-colors duration-200"
          >
            View All Top Deals
            <ChevronRightIcon className="ml-2 h-4 w-4" />
          </Link>
        </div>
      </div>
    </section>
  );
}
