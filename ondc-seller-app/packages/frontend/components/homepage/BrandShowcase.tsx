'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
// TODO: Replace with real API integration

const BrandShowcase: React.FC = () => {
  const brands = getBrandShowcaseByTenant(defaultTenantId);

  if (brands.length === 0) {
    return null;
  }

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Trusted Brands
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Shop from our carefully curated selection of premium brands and trusted partners
          </p>
        </div>

        {/* Brand Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8">
          {brands.map((brand) => (
            <div
              key={brand.id}
              className="group bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100"
            >
              <Link href={`/brands/${brand.name.toLowerCase().replace(/\s+/g, '-')}`}>
                <div className="text-center">
                  {/* Brand Logo */}
                  <div className="relative h-16 w-full mb-4 flex items-center justify-center">
                    <Image
                      src={brand.logo}
                      alt={`${brand.name} logo`}
                      width={120}
                      height={64}
                      className="object-contain filter grayscale group-hover:grayscale-0 transition-all duration-300"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = '/images/placeholder.svg';
                      }}
                    />
                  </div>

                  {/* Brand Name */}
                  <h3 className="font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                    {brand.name}
                  </h3>

                  {/* Product Count */}
                  <p className="text-sm text-gray-500">
                    {brand.productCount} products
                  </p>

                  {/* Partner Badge */}
                  {brand.isPartner && (
                    <div className="mt-2">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Partner
                      </span>
                    </div>
                  )}
                </div>
              </Link>
            </div>
          ))}
        </div>

        {/* View All Brands Button */}
        <div className="text-center mt-12">
          <Link
            href="/brands"
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-200"
          >
            View All Brands
            <svg
              className="ml-2 -mr-1 w-5 h-5"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default BrandShowcase;
