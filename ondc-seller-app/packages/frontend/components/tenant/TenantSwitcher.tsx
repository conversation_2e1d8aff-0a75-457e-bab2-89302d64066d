'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { ChevronDown, Check, Building, Search, Plus } from 'lucide-react';
// TODO: Replace with real API integration
interface MockTenant {
  id: string;
  name: string;
  domain: string;
  status: string;
  plan: string;
  theme: {
    primary: string;
    secondary: string;
  };
}

interface TenantSwitcherProps {
  currentTenantId?: string;
  onTenantChange?: (tenant: MockTenant) => void;
  className?: string;
  showCreateOption?: boolean;
}

const TenantSwitcher: React.FC<TenantSwitcherProps> = ({
  currentTenantId,
  onTenantChange,
  className = '',
  showCreateOption = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentTenant, setCurrentTenant] = useState<MockTenant | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const router = useRouter();

  // Load current tenant
  useEffect(() => {
    // TODO: Replace with real API call to get tenants
    const mockTenants: MockTenant[] = [];
    const tenant = currentTenantId
      ? mockTenants.find(t => t.id === currentTenantId)
      : mockTenants[0]; // Default to first tenant

    if (tenant) {
      setCurrentTenant(tenant);
    }
  }, [currentTenantId]);

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchQuery('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // TODO: Replace with real API call to get filtered tenants
  const filteredTenants: MockTenant[] = [];

  const handleTenantSelect = (tenant: MockTenant) => {
    setCurrentTenant(tenant);
    setIsOpen(false);
    setSearchQuery('');
    
    // Store in localStorage for persistence
    localStorage.setItem('selectedTenantId', tenant.id);
    
    // Call callback if provided
    if (onTenantChange) {
      onTenantChange(tenant);
    }
    
    // In a real app, you might want to reload the page or update the URL
    // to reflect the tenant change
    window.location.reload();
  };

  const handleCreateTenant = () => {
    setIsOpen(false);
    router.push('/admin/settings/tenant/new');
  };

  if (!currentTenant) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="h-10 bg-gray-200 rounded-lg w-48"></div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Trigger Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-3 w-full px-3 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
      >
        <div 
          className="w-8 h-8 rounded-lg flex items-center justify-center text-white text-sm font-medium"
          style={{ backgroundColor: currentTenant.theme.primary_color }}
        >
          {currentTenant.name[0]}
        </div>
        <div className="flex-1 text-left">
          <div className="text-sm font-medium text-gray-900 truncate">
            {currentTenant.name}
          </div>
          <div className="text-xs text-gray-500 truncate">
            @{currentTenant.handle}
          </div>
        </div>
        <ChevronDown className={`w-4 h-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-80 overflow-hidden">
          {/* Search */}
          <div className="p-3 border-b border-gray-200">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search tenants..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                autoFocus
              />
            </div>
          </div>

          {/* Tenant List */}
          <div className="max-h-60 overflow-y-auto">
            {filteredTenants.map((tenant) => (
              <button
                key={tenant.id}
                onClick={() => handleTenantSelect(tenant)}
                className="w-full flex items-center space-x-3 px-3 py-3 hover:bg-gray-50 transition-colors"
              >
                <div 
                  className="w-8 h-8 rounded-lg flex items-center justify-center text-white text-sm font-medium"
                  style={{ backgroundColor: tenant.theme.primary_color }}
                >
                  {tenant.name[0]}
                </div>
                <div className="flex-1 text-left">
                  <div className="text-sm font-medium text-gray-900">
                    {tenant.name}
                  </div>
                  <div className="text-xs text-gray-500">
                    @{tenant.handle} • {tenant.subscription_plan}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`inline-block w-2 h-2 rounded-full ${
                    tenant.status === 'active' ? 'bg-green-400' : 
                    tenant.status === 'trial' ? 'bg-blue-400' : 'bg-red-400'
                  }`} />
                  {tenant.id === currentTenant.id && (
                    <Check className="w-4 h-4 text-blue-600" />
                  )}
                </div>
              </button>
            ))}

            {filteredTenants.length === 0 && (
              <div className="px-3 py-6 text-center">
                <Building className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-600">No tenants found</p>
                {searchQuery && (
                  <p className="text-xs text-gray-500 mt-1">
                    Try adjusting your search
                  </p>
                )}
              </div>
            )}
          </div>

          {/* Create New Tenant Option */}
          {showCreateOption && (
            <div className="border-t border-gray-200">
              <button
                onClick={handleCreateTenant}
                className="w-full flex items-center space-x-3 px-3 py-3 text-blue-600 hover:bg-blue-50 transition-colors"
              >
                <div className="w-8 h-8 rounded-lg bg-blue-100 flex items-center justify-center">
                  <Plus className="w-4 h-4 text-blue-600" />
                </div>
                <div className="text-left">
                  <div className="text-sm font-medium">Create new tenant</div>
                  <div className="text-xs text-blue-500">Set up a new store</div>
                </div>
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default TenantSwitcher;
