'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useMedusaBackendProduct } from '@/hooks/useMedusaBackendProducts';
import { useMedusaCartContext } from '@/hooks/useMedusaCart';
import { MedusaProduct, MedusaProductVariant } from '@/lib/medusa-backend-api';

interface MedusaProductDetailPageProps {
  productId: string;
}

export default function MedusaProductDetailPage({ productId }: MedusaProductDetailPageProps) {
  const { product, loading, error, refetch } = useMedusaBackendProduct(productId);
  const { addItem, isAddingItem } = useMedusaCartContext();
  
  const [selectedVariant, setSelectedVariant] = useState<MedusaProductVariant | null>(null);
  const [quantity, setQuantity] = useState(1);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isAdding, setIsAdding] = useState(false);

  // Set default variant and image when product loads
  useEffect(() => {
    if (product) {
      if (product.variants && product.variants.length > 0) {
        setSelectedVariant(product.variants[0]);
      }
      if (product.thumbnail) {
        setSelectedImage(product.thumbnail);
      } else if (product.images && product.images.length > 0) {
        setSelectedImage(product.images[0].url);
      }
    }
  }, [product]);

  const handleAddToCart = async () => {
    if (!selectedVariant) {
      alert('Please select a variant');
      return;
    }

    setIsAdding(true);
    try {
      await addItem(selectedVariant.id, quantity);
      alert(`Added ${quantity} ${product?.title} to cart!`);
    } catch (error) {
      console.error('Error adding to cart:', error);
      alert('Failed to add to cart. Please try again.');
    } finally {
      setIsAdding(false);
    }
  };

  const formatPrice = (variant: MedusaProductVariant) => {
    if (variant.prices && variant.prices.length > 0) {
      const price = variant.prices[0];
      return `€${(price.amount / 100).toFixed(2)}`;
    }
    return 'Price not available';
  };

  const getBreadcrumbs = () => {
    const breadcrumbs = [
      { name: 'Home', href: '/' },
      { name: 'Products', href: '/products' }
    ];

    if (product?.categories && product.categories.length > 0) {
      const category = product.categories[0];
      breadcrumbs.push({
        name: category.name,
        href: `/categories/${category.handle || category.id}`
      });
    }

    if (product) {
      breadcrumbs.push({
        name: product.title,
        href: `/products/${product.id}`
      });
    }

    return breadcrumbs;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            {/* Breadcrumb skeleton */}
            <div className="h-4 bg-gray-300 rounded w-1/3 mb-8"></div>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Image skeleton */}
              <div className="space-y-4">
                <div className="w-full h-96 bg-gray-300 rounded-lg"></div>
                <div className="flex space-x-2">
                  {Array.from({ length: 4 }).map((_, i) => (
                    <div key={i} className="w-20 h-20 bg-gray-300 rounded"></div>
                  ))}
                </div>
              </div>
              
              {/* Content skeleton */}
              <div className="space-y-4">
                <div className="h-8 bg-gray-300 rounded w-3/4"></div>
                <div className="h-6 bg-gray-300 rounded w-1/2"></div>
                <div className="h-4 bg-gray-300 rounded w-full"></div>
                <div className="h-4 bg-gray-300 rounded w-5/6"></div>
                <div className="h-12 bg-gray-300 rounded w-full"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Error Loading Product</h1>
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
              <p className="text-red-600">{error}</p>
            </div>
            <button 
              onClick={refetch}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="min-h-screen bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Product Not Found</h1>
            <p className="text-gray-600 mb-4">The product you're looking for doesn't exist.</p>
            <Link 
              href="/products"
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              Browse Products
            </Link>
          </div>
        </div>
      </div>
    );
  }

  const breadcrumbs = getBreadcrumbs();

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumbs */}
        <nav className="flex mb-8" aria-label="Breadcrumb">
          <ol className="flex items-center space-x-2">
            {breadcrumbs.map((breadcrumb, index) => (
              <li key={breadcrumb.href} className="flex items-center">
                {index > 0 && (
                  <svg className="w-4 h-4 text-gray-400 mx-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                  </svg>
                )}
                {index === breadcrumbs.length - 1 ? (
                  <span className="text-gray-500">{breadcrumb.name}</span>
                ) : (
                  <Link href={breadcrumb.href} className="text-blue-600 hover:text-blue-800">
                    {breadcrumb.name}
                  </Link>
                )}
              </li>
            ))}
          </ol>
        </nav>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Product Images */}
          <div className="space-y-4">
            {/* Main Image */}
            <div className="w-full h-96 bg-gray-200 rounded-lg overflow-hidden">
              {selectedImage ? (
                <img
                  src={selectedImage}
                  alt={product.title}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center text-gray-400">
                  No Image Available
                </div>
              )}
            </div>

            {/* Thumbnail Images */}
            {product.images && product.images.length > 1 && (
              <div className="flex space-x-2 overflow-x-auto">
                {product.images.map((image, index) => (
                  <button
                    key={image.id}
                    onClick={() => setSelectedImage(image.url)}
                    className={`flex-shrink-0 w-20 h-20 rounded border-2 overflow-hidden ${
                      selectedImage === image.url ? 'border-blue-500' : 'border-gray-300'
                    }`}
                  >
                    <img
                      src={image.url}
                      alt={`${product.title} ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Product Info */}
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{product.title}</h1>
              {selectedVariant && (
                <p className="text-2xl font-bold text-blue-600 mt-2">
                  {formatPrice(selectedVariant)}
                </p>
              )}
            </div>

            {product.description && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Description</h3>
                <p className="text-gray-600">{product.description}</p>
              </div>
            )}

            {/* Variant Selection */}
            {product.variants && product.variants.length > 1 && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Variants</h3>
                <div className="grid grid-cols-2 gap-2">
                  {product.variants.map((variant) => (
                    <button
                      key={variant.id}
                      onClick={() => setSelectedVariant(variant)}
                      className={`p-3 border rounded text-left ${
                        selectedVariant?.id === variant.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      <div className="font-medium">{variant.title}</div>
                      <div className="text-sm text-gray-600">{formatPrice(variant)}</div>
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Quantity Selection */}
            <div>
              <label htmlFor="quantity" className="block text-lg font-medium text-gray-900 mb-2">
                Quantity
              </label>
              <select
                id="quantity"
                value={quantity}
                onChange={(e) => setQuantity(parseInt(e.target.value))}
                className="border border-gray-300 rounded px-3 py-2"
              >
                {Array.from({ length: 10 }, (_, i) => i + 1).map((num) => (
                  <option key={num} value={num}>
                    {num}
                  </option>
                ))}
              </select>
            </div>

            {/* Add to Cart Button */}
            <button
              onClick={handleAddToCart}
              disabled={!selectedVariant || isAdding || isAddingItem}
              className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed transition-colors"
            >
              {isAdding || isAddingItem ? 'Adding to Cart...' : 'Add to Cart'}
            </button>

            {/* Product Details */}
            {selectedVariant && (
              <div className="border-t pt-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Product Details</h3>
                <dl className="space-y-2">
                  {selectedVariant.sku && (
                    <div className="flex">
                      <dt className="font-medium text-gray-900 w-24">SKU:</dt>
                      <dd className="text-gray-600">{selectedVariant.sku}</dd>
                    </div>
                  )}
                  <div className="flex">
                    <dt className="font-medium text-gray-900 w-24">Status:</dt>
                    <dd className="text-gray-600">{product.status}</dd>
                  </div>
                  {selectedVariant.inventory_quantity !== undefined && (
                    <div className="flex">
                      <dt className="font-medium text-gray-900 w-24">Stock:</dt>
                      <dd className="text-gray-600">
                        {selectedVariant.inventory_quantity > 0 
                          ? `${selectedVariant.inventory_quantity} available`
                          : 'Out of stock'
                        }
                      </dd>
                    </div>
                  )}
                </dl>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
