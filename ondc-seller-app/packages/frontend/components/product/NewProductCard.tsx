'use client';

import React from 'react';
import Link from 'next/link';
import Tooltip from '@mui/material/Tooltip';

interface ProductCardProps {
  id: string;
  title: string;
  description?: string;
  thumbnail?: string;
  price: number;
  originalPrice?: number;
  badge?: string;
}

const NewProductCard: React.FC<ProductCardProps> = ({
  id,
  title,
  description,
  thumbnail,
  price,
  originalPrice,
  badge,
}) => {
  return (
    <div className="bg-white rounded-xl shadow-md hover:shadow-xl transition duration-300 flex flex-col overflow-hidden group h-full border border-gray-100">
      {/* Image */}
      <div className="aspect-[4/3] bg-gray-100 relative overflow-hidden">
        <img
          src={thumbnail || '/images/products/placeholder.svg'}
          alt={title}
          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
          onError={e => {
            e.currentTarget.src = '/images/products/placeholder.svg';
          }}
        />
        {badge && (
          <div className="absolute top-2 left-2 bg-blue-600 text-white text-xs font-semibold px-2 py-1 rounded">
            {badge}
          </div>
        )}
      </div>

      {/* Product Info */}
      <div className="p-4 flex flex-col flex-grow">
      
          <h3 className="font-semibold text-gray-900 text-lg mb-1 line-clamp-1 cursor-help">
            {title}
          </h3>
        
        <Tooltip title={description} arrow placement="bottom">
            <p className="text-sm text-gray-500 mb-3 !line-clamp-2 h-[40px]">
            {description}
            </p>
        </Tooltip>

        {/* Price */}
        <div className="mb-4">
          <span className="text-xl font-bold text-gray-900 transition-colors duration-200 group-hover:text-blue-600">
            ₹{price}
          </span>
          {originalPrice && (
            <span className="ml-2 text-sm text-gray-400 line-through">₹{originalPrice}</span>
          )}
        </div>

        {/* Button */}
        <Link
          href={`/products/${id}`}
          className="mt-auto bg-blue-600 text-white text-center py-2 rounded-md hover:bg-blue-700 hover:scale-[1.02] transition-all duration-200 shadow-sm"
          data-testid="product-link"
        >
          View Product
        </Link>
      </div>
    </div>
  );
};

export default NewProductCard;
