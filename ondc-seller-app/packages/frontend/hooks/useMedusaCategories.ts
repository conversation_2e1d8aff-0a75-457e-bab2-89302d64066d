import { useState, useEffect } from 'react';

// Medusa category data structure
interface MedusaCategory {
  id: string;
  name: string;
  handle: string;
  description?: string;
  rank: number;
  parent_category_id: string | null;
  parent_category?: MedusaCategory | null;
  category_children: MedusaCategory[];
  created_at: string;
  updated_at: string;
  metadata: any;
}

interface CategoriesResponse {
  product_categories: MedusaCategory[];
  count: number;
  offset: number;
  limit: number;
}

// Transformed category structure for UI
export interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  image?: string;
  subcategories: Category[];
  productCount?: number;
}

// Transform Medusa category to UI category
const transformMedusaCategory = (medusaCategory: MedusaCategory): Category => {
  return {
    id: medusaCategory.id,
    name: medusaCategory.name,
    slug: medusaCategory.handle,
    description: medusaCategory.description || `Explore our ${medusaCategory.name.toLowerCase()} collection`,
    subcategories: (medusaCategory.category_children || []).map(transformMedusaCategory),
    productCount: Math.floor(Math.random() * 200) + 50, // Mock product count for now
  };
};

export const useMedusaCategories = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCategories = async () => {
    try {
      setLoading(true);
      setError(null);
      
      console.log('🔄 Fetching categories from Medusa backend...');
      
      // Fetch categories from Medusa backend
      const response = await fetch('http://localhost:9000/store/product-categories', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-publishable-api-key': 'pk_51719ff15f2d615335059dc2bad507d3446466d7d6e2af5c777cf07d7ac53af0',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch categories: ${response.status} ${response.statusText}`);
      }

      const data: CategoriesResponse = await response.json();
      console.log('✅ Categories fetched successfully:', data.product_categories.length, 'categories');

      // Filter to get only parent categories (those without parent_category_id)
      const parentCategories = data.product_categories.filter(cat => cat.parent_category_id === null);
      
      // Transform to UI format
      const transformedCategories = parentCategories.map(transformMedusaCategory);
      
      setCategories(transformedCategories);
      console.log('🎯 Transformed categories for UI:', transformedCategories.length, 'parent categories');
      
    } catch (err) {
      console.error('❌ Error fetching categories:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch categories');
      setCategories([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  return {
    categories,
    loading,
    error,
    refetch: fetchCategories
  };
};
