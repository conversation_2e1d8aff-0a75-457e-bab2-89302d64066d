'use client';

import { useState, useEffect, useCallback } from 'react';
import { apiClient } from '../lib/api-client';
import { useTenant } from './useTenant';

export interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  image?: string;
  parent_id?: string;
  parent?: Category;
  children?: Category[];
  product_count: number;
  status: 'active' | 'inactive';
  sort_order: number;
  tenant_id: string;
  seo?: {
    title?: string;
    description?: string;
    keywords?: string[];
  };
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface CategoryFilters {
  limit?: number;
  offset?: number;
  featured?: boolean;
  parentOnly?: boolean;
  parent_id?: string;
  status?: 'active' | 'inactive';
  includeProducts?: boolean;
  includeChildren?: boolean;
}

export interface UseCategoriesResult {
  categories: Category[];
  loading: boolean;
  error: string | null;
  totalCount: number;
  hasMore: boolean;
  fetchCategories: (filters?: CategoryFilters) => Promise<void>;
  refreshCategories: () => Promise<void>;
  createCategory: (data: Partial<Category>) => Promise<Category | null>;
  updateCategory: (id: string, data: Partial<Category>) => Promise<Category | null>;
  deleteCategory: (id: string) => Promise<boolean>;
}

export function useCategories(initialFilters: CategoryFilters = {}): UseCategoriesResult {
  const { currentTenant } = useTenant();
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [currentFilters, setCurrentFilters] = useState<CategoryFilters>(initialFilters);

  const fetchCategories = useCallback(async (filters: CategoryFilters = {}) => {
    if (!currentTenant) return;

    try {
      setLoading(true);
      setError(null);
      
      const mergedFilters = { ...currentFilters, ...filters };
      setCurrentFilters(mergedFilters);

      const response = await apiClient.getCategories(mergedFilters);
      
      if (response.categories) {
        setCategories(response.categories);
        setTotalCount(response.count || response.categories.length);
      } else {
        setCategories([]);
        setTotalCount(0);
      }
    } catch (err) {
      console.error('Error fetching categories:', err);
      setError('Failed to load categories');
      setCategories([]);
      setTotalCount(0);
    } finally {
      setLoading(false);
    }
  }, [currentTenant, currentFilters]);

  const refreshCategories = useCallback(async () => {
    await fetchCategories(currentFilters);
  }, [fetchCategories, currentFilters]);

  const createCategory = useCallback(async (data: Partial<Category>): Promise<Category | null> => {
    if (!currentTenant) return null;

    try {
      setLoading(true);
      setError(null);

      const response = await apiClient.createCategory(data);
      
      if (response.category) {
        // Add to local state
        setCategories(prev => [...prev, response.category]);
        setTotalCount(prev => prev + 1);
        return response.category;
      }
      
      return null;
    } catch (err) {
      console.error('Error creating category:', err);
      setError('Failed to create category');
      return null;
    } finally {
      setLoading(false);
    }
  }, [currentTenant]);

  const updateCategory = useCallback(async (id: string, data: Partial<Category>): Promise<Category | null> => {
    if (!currentTenant) return null;

    try {
      setLoading(true);
      setError(null);

      const response = await apiClient.updateCategory(id, data);
      
      if (response.category) {
        // Update local state
        setCategories(prev => 
          prev.map(cat => cat.id === id ? response.category : cat)
        );
        return response.category;
      }
      
      return null;
    } catch (err) {
      console.error('Error updating category:', err);
      setError('Failed to update category');
      return null;
    } finally {
      setLoading(false);
    }
  }, [currentTenant]);

  const deleteCategory = useCallback(async (id: string): Promise<boolean> => {
    if (!currentTenant) return false;

    try {
      setLoading(true);
      setError(null);

      await apiClient.deleteCategory(id);
      
      // Remove from local state
      setCategories(prev => prev.filter(cat => cat.id !== id));
      setTotalCount(prev => prev - 1);
      
      return true;
    } catch (err) {
      console.error('Error deleting category:', err);
      setError('Failed to delete category');
      return false;
    } finally {
      setLoading(false);
    }
  }, [currentTenant]);

  // Auto-fetch on tenant change
  useEffect(() => {
    if (currentTenant) {
      fetchCategories(initialFilters);
    }
  }, [currentTenant, fetchCategories, initialFilters]);

  const hasMore = categories.length < totalCount;

  return {
    categories,
    loading,
    error,
    totalCount,
    hasMore,
    fetchCategories,
    refreshCategories,
    createCategory,
    updateCategory,
    deleteCategory
  };
}

export interface UseCategoryResult {
  category: Category | null;
  loading: boolean;
  error: string | null;
  fetchCategory: (id: string) => Promise<void>;
  updateCategory: (data: Partial<Category>) => Promise<Category | null>;
  deleteCategory: () => Promise<boolean>;
}

export function useCategory(categoryId?: string): UseCategoryResult {
  const { currentTenant } = useTenant();
  const [category, setCategory] = useState<Category | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchCategory = useCallback(async (id: string) => {
    if (!currentTenant) return;

    try {
      setLoading(true);
      setError(null);

      const response = await apiClient.getCategory(id);
      
      if (response.category) {
        setCategory(response.category);
      } else {
        setCategory(null);
        setError('Category not found');
      }
    } catch (err) {
      console.error('Error fetching category:', err);
      setError('Failed to load category');
      setCategory(null);
    } finally {
      setLoading(false);
    }
  }, [currentTenant]);

  const updateCategory = useCallback(async (data: Partial<Category>): Promise<Category | null> => {
    if (!currentTenant || !category) return null;

    try {
      setLoading(true);
      setError(null);

      const response = await apiClient.updateCategory(category.id, data);
      
      if (response.category) {
        setCategory(response.category);
        return response.category;
      }
      
      return null;
    } catch (err) {
      console.error('Error updating category:', err);
      setError('Failed to update category');
      return null;
    } finally {
      setLoading(false);
    }
  }, [currentTenant, category]);

  const deleteCategory = useCallback(async (): Promise<boolean> => {
    if (!currentTenant || !category) return false;

    try {
      setLoading(true);
      setError(null);

      await apiClient.deleteCategory(category.id);
      setCategory(null);
      
      return true;
    } catch (err) {
      console.error('Error deleting category:', err);
      setError('Failed to delete category');
      return false;
    } finally {
      setLoading(false);
    }
  }, [currentTenant, category]);

  // Auto-fetch on categoryId or tenant change
  useEffect(() => {
    if (categoryId && currentTenant) {
      fetchCategory(categoryId);
    }
  }, [categoryId, currentTenant, fetchCategory]);

  return {
    category,
    loading,
    error,
    fetchCategory,
    updateCategory,
    deleteCategory
  };
}
