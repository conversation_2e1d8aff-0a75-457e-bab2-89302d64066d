'use client';

import { useState, useCallback } from 'react';
import { 
  medusaAPI, 
  MedusaCustomer, 
  MedusaOrder, 
  CustomerResponse,
  OrdersResponse,
  OrderResponse,
  CODOrderResponse,
  MedusaAPIError 
} from '../lib/medusa-backend-api';

interface UseMedusaCustomerReturn {
  // Customer state
  customer: MedusaCustomer | null;
  orders: MedusaOrder[];
  currentOrder: MedusaOrder | null;
  
  // Loading states
  isRegistering: boolean;
  isLoadingOrders: boolean;
  isLoadingOrder: boolean;
  isCreatingOrder: boolean;
  
  // Error handling
  error: string | null;
  clearError: () => void;
  
  // Customer actions
  registerCustomer: (data: {
    email: string;
    first_name: string;
    last_name: string;
    phone?: string;
  }) => Promise<boolean>;
  
  // Order actions
  getOrders: (email: string, limit?: number, offset?: number) => Promise<void>;
  getOrder: (orderId: string) => Promise<void>;
  completeCartWithCOD: (cartId: string) => Promise<MedusaOrder | null>;
  
  // Utility functions
  resetCustomer: () => void;
  resetOrders: () => void;
  resetCurrentOrder: () => void;
}

export function useMedusaCustomer(): UseMedusaCustomerReturn {
  const [customer, setCustomer] = useState<MedusaCustomer | null>(null);
  const [orders, setOrders] = useState<MedusaOrder[]>([]);
  const [currentOrder, setCurrentOrder] = useState<MedusaOrder | null>(null);
  const [isRegistering, setIsRegistering] = useState(false);
  const [isLoadingOrders, setIsLoadingOrders] = useState(false);
  const [isLoadingOrder, setIsLoadingOrder] = useState(false);
  const [isCreatingOrder, setIsCreatingOrder] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const handleError = useCallback((err: unknown, defaultMessage: string) => {
    console.error('Medusa Customer error:', err);
    if (err instanceof MedusaAPIError) {
      setError(err.message);
    } else if (err instanceof Error) {
      setError(err.message);
    } else {
      setError(defaultMessage);
    }
  }, []);

  const registerCustomer = useCallback(async (data: {
    email: string;
    first_name: string;
    last_name: string;
    phone?: string;
  }): Promise<boolean> => {
    try {
      setIsRegistering(true);
      setError(null);
      
      const response: CustomerResponse = await medusaAPI.registerCustomer(data);
      
      if (response.success) {
        setCustomer(response.customer);
        return true;
      } else {
        // Handle case where customer already exists
        if (response.customer) {
          setCustomer(response.customer);
          return true;
        }
        setError(response.message || 'Registration failed');
        return false;
      }
      
    } catch (err) {
      // Handle customer already exists case
      if (err instanceof MedusaAPIError && err.status === 409) {
        // Customer already exists, this is actually success for our use case
        if (err.response?.customer) {
          setCustomer(err.response.customer);
          return true;
        }
      }
      handleError(err, 'Failed to register customer');
      return false;
    } finally {
      setIsRegistering(false);
    }
  }, [handleError]);

  const getOrders = useCallback(async (email: string, limit = 10, offset = 0) => {
    try {
      setIsLoadingOrders(true);
      setError(null);
      
      const response: OrdersResponse = await medusaAPI.getOrders({
        email,
        limit,
        offset
      });
      
      if (response.success) {
        setOrders(response.orders);
      } else {
        setError('Failed to load orders');
      }
      
    } catch (err) {
      handleError(err, 'Failed to fetch orders');
    } finally {
      setIsLoadingOrders(false);
    }
  }, [handleError]);

  const getOrder = useCallback(async (orderId: string) => {
    try {
      setIsLoadingOrder(true);
      setError(null);
      
      const response: OrderResponse = await medusaAPI.getOrder(orderId);
      
      if (response.success) {
        setCurrentOrder(response.order);
      } else {
        setError('Failed to load order');
      }
      
    } catch (err) {
      handleError(err, 'Failed to fetch order');
    } finally {
      setIsLoadingOrder(false);
    }
  }, [handleError]);

  const completeCartWithCOD = useCallback(async (cartId: string): Promise<MedusaOrder | null> => {
    try {
      setIsCreatingOrder(true);
      setError(null);
      
      const response: CODOrderResponse = await medusaAPI.completeCartWithCOD(cartId);
      
      if (response.success && response.order) {
        setCurrentOrder(response.order);
        // Add to orders list if we have one
        setOrders(prevOrders => [response.order, ...prevOrders]);
        return response.order;
      } else {
        setError(response.message || 'Failed to create order');
        return null;
      }
      
    } catch (err) {
      handleError(err, 'Failed to complete order with Cash on Delivery');
      return null;
    } finally {
      setIsCreatingOrder(false);
    }
  }, [handleError]);

  const resetCustomer = useCallback(() => {
    setCustomer(null);
  }, []);

  const resetOrders = useCallback(() => {
    setOrders([]);
  }, []);

  const resetCurrentOrder = useCallback(() => {
    setCurrentOrder(null);
  }, []);

  return {
    // Customer state
    customer,
    orders,
    currentOrder,
    
    // Loading states
    isRegistering,
    isLoadingOrders,
    isLoadingOrder,
    isCreatingOrder,
    
    // Error handling
    error,
    clearError,
    
    // Customer actions
    registerCustomer,
    
    // Order actions
    getOrders,
    getOrder,
    completeCartWithCOD,
    
    // Utility functions
    resetCustomer,
    resetOrders,
    resetCurrentOrder,
  };
}

// Hook for managing customer session/state
export function useMedusaCustomerSession() {
  const [customer, setCustomer] = useState<MedusaCustomer | null>(null);
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  // Load customer from localStorage on mount
  useState(() => {
    const savedCustomer = localStorage.getItem('medusa_customer');
    if (savedCustomer) {
      try {
        const customerData = JSON.parse(savedCustomer);
        setCustomer(customerData);
        setIsLoggedIn(true);
      } catch (error) {
        console.error('Error loading customer from localStorage:', error);
        localStorage.removeItem('medusa_customer');
      }
    }
  });

  const loginCustomer = useCallback((customerData: MedusaCustomer) => {
    setCustomer(customerData);
    setIsLoggedIn(true);
    localStorage.setItem('medusa_customer', JSON.stringify(customerData));
  }, []);

  const logoutCustomer = useCallback(() => {
    setCustomer(null);
    setIsLoggedIn(false);
    localStorage.removeItem('medusa_customer');
    localStorage.removeItem('medusa_cart_id'); // Also clear cart
  }, []);

  const updateCustomer = useCallback((customerData: Partial<MedusaCustomer>) => {
    if (customer) {
      const updatedCustomer = { ...customer, ...customerData };
      setCustomer(updatedCustomer);
      localStorage.setItem('medusa_customer', JSON.stringify(updatedCustomer));
    }
  }, [customer]);

  return {
    customer,
    isLoggedIn,
    loginCustomer,
    logoutCustomer,
    updateCustomer,
  };
}

// Hook for checkout flow
export function useMedusaCheckout() {
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [completedOrder, setCompletedOrder] = useState<MedusaOrder | null>(null);

  const processCheckoutWithCOD = useCallback(async (
    cartId: string,
    customerData?: {
      email: string;
      first_name: string;
      last_name: string;
      phone?: string;
    }
  ): Promise<MedusaOrder | null> => {
    try {
      setIsProcessing(true);
      setError(null);

      // Register customer if provided
      if (customerData) {
        try {
          await medusaAPI.registerCustomer(customerData);
        } catch (err) {
          // Ignore if customer already exists
          if (!(err instanceof MedusaAPIError && err.status === 409)) {
            throw err;
          }
        }
      }

      // Complete cart with COD
      const response = await medusaAPI.completeCartWithCOD(cartId);
      
      if (response.success && response.order) {
        setCompletedOrder(response.order);
        return response.order;
      } else {
        setError(response.message || 'Failed to complete checkout');
        return null;
      }

    } catch (err) {
      console.error('Checkout error:', err);
      if (err instanceof MedusaAPIError) {
        setError(err.message);
      } else if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('Failed to complete checkout');
      }
      return null;
    } finally {
      setIsProcessing(false);
    }
  }, []);

  const resetCheckout = useCallback(() => {
    setCompletedOrder(null);
    setError(null);
  }, []);

  return {
    isProcessing,
    error,
    completedOrder,
    processCheckoutWithCOD,
    resetCheckout,
  };
}
