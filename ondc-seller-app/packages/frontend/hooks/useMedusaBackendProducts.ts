'use client';

import { useState, useEffect, useCallback } from 'react';
import { 
  medusaAPI, 
  MedusaProduct, 
  MedusaCategory, 
  ProductsResponse, 
  CategoriesResponse,
  MedusaAPIError 
} from '../lib/medusa-backend-api';

interface UseMedusaBackendProductsReturn {
  // Products state
  products: MedusaProduct[];
  categories: MedusaCategory[];
  currentProduct: MedusaProduct | null;
  
  // Pagination
  totalProducts: number;
  currentPage: number;
  totalPages: number;
  
  // Loading states
  loading: boolean;
  loadingProduct: boolean;
  loadingCategories: boolean;
  
  // Error handling
  error: string | null;
  clearError: () => void;
  
  // Actions
  fetchProducts: (params?: {
    limit?: number;
    offset?: number;
    q?: string;
    category_id?: string[];
    collection_id?: string[];
    tags?: string[];
    type_id?: string[];
  }) => Promise<void>;
  fetchProduct: (id: string) => Promise<void>;
  fetchCategories: (params?: {
    limit?: number;
    offset?: number;
    parent_category_id?: string;
    include_descendants_tree?: boolean;
  }) => Promise<void>;
  searchProducts: (query: string, limit?: number) => Promise<void>;
  getProductsByCategory: (categoryId: string, limit?: number) => Promise<void>;
  getFeaturedProducts: (limit?: number) => Promise<void>;
  getTopDeals: (limit?: number) => Promise<void>;
  getHotPicks: (limit?: number) => Promise<void>;
  
  // Utility functions
  resetProducts: () => void;
  resetCurrentProduct: () => void;
}

export function useMedusaBackendProducts(): UseMedusaBackendProductsReturn {
  const [products, setProducts] = useState<MedusaProduct[]>([]);
  const [categories, setCategories] = useState<MedusaCategory[]>([]);
  const [currentProduct, setCurrentProduct] = useState<MedusaProduct | null>(null);
  const [totalProducts, setTotalProducts] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [loadingProduct, setLoadingProduct] = useState(false);
  const [loadingCategories, setLoadingCategories] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const handleError = useCallback((err: unknown, defaultMessage: string) => {
    console.error('Medusa Backend Products error:', err);
    if (err instanceof MedusaAPIError) {
      setError(err.message);
    } else if (err instanceof Error) {
      setError(err.message);
    } else {
      setError(defaultMessage);
    }
  }, []);

  const fetchProducts = useCallback(async (params?: {
    limit?: number;
    offset?: number;
    q?: string;
    category_id?: string[];
    collection_id?: string[];
    tags?: string[];
    type_id?: string[];
  }) => {
    try {
      setLoading(true);
      setError(null);
      
      const response: ProductsResponse = await medusaAPI.getProducts(params);
      
      setProducts(response.products);
      setTotalProducts(response.count);
      
      // Calculate pagination
      const limit = params?.limit || 20;
      const offset = params?.offset || 0;
      setCurrentPage(Math.floor(offset / limit) + 1);
      
    } catch (err) {
      handleError(err, 'Failed to fetch products');
    } finally {
      setLoading(false);
    }
  }, [handleError]);

  const fetchProduct = useCallback(async (id: string) => {
    try {
      setLoadingProduct(true);
      setError(null);
      
      const response = await medusaAPI.getProduct(id);
      setCurrentProduct(response.product);
      
    } catch (err) {
      handleError(err, 'Failed to fetch product');
    } finally {
      setLoadingProduct(false);
    }
  }, [handleError]);

  const fetchCategories = useCallback(async (params?: {
    limit?: number;
    offset?: number;
    parent_category_id?: string;
    include_descendants_tree?: boolean;
  }) => {
    try {
      setLoadingCategories(true);
      setError(null);
      
      const response: CategoriesResponse = await medusaAPI.getCategories(params);
      setCategories(response.product_categories);
      
    } catch (err) {
      handleError(err, 'Failed to fetch categories');
    } finally {
      setLoadingCategories(false);
    }
  }, [handleError]);

  const searchProducts = useCallback(async (query: string, limit = 20) => {
    await fetchProducts({ q: query, limit });
  }, [fetchProducts]);

  const getProductsByCategory = useCallback(async (categoryId: string, limit = 20) => {
    await fetchProducts({ category_id: [categoryId], limit });
  }, [fetchProducts]);

  const getFeaturedProducts = useCallback(async (limit = 10) => {
    // Use collection_id instead of tags for Medusa v2
    await fetchProducts({ collection_id: ['pcol_01JZ81RC5AMYQKDP3YQMHRHRD1'], limit }); // Featured Products collection
  }, [fetchProducts]);

  const getTopDeals = useCallback(async (limit = 10) => {
    // Use collection_id instead of tags for Medusa v2
    await fetchProducts({ collection_id: ['pcol_01JZ81SMDPT78PA350JRQ3BJTZ'], limit }); // Top deals collection
  }, [fetchProducts]);

  const getHotPicks = useCallback(async (limit = 10) => {
    // Use collection_id instead of tags for Medusa v2
    await fetchProducts({ collection_id: ['pcol_01JZ81S667NF4RSB7ZJXK3N0JC'], limit }); // Hot picks collection
  }, [fetchProducts]);

  const resetProducts = useCallback(() => {
    setProducts([]);
    setTotalProducts(0);
    setCurrentPage(1);
  }, []);

  const resetCurrentProduct = useCallback(() => {
    setCurrentProduct(null);
  }, []);

  // Calculate total pages
  const totalPages = Math.ceil(totalProducts / 20); // Assuming default limit of 20

  // Load categories on mount
  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  return {
    // Products state
    products,
    categories,
    currentProduct,
    
    // Pagination
    totalProducts,
    currentPage,
    totalPages,
    
    // Loading states
    loading,
    loadingProduct,
    loadingCategories,
    
    // Error handling
    error,
    clearError,
    
    // Actions
    fetchProducts,
    fetchProduct,
    fetchCategories,
    searchProducts,
    getProductsByCategory,
    getFeaturedProducts,
    getTopDeals,
    getHotPicks,
    
    // Utility functions
    resetProducts,
    resetCurrentProduct,
  };
}

// Hook for single product with automatic loading
export function useMedusaBackendProduct(productId: string | null) {
  const [product, setProduct] = useState<MedusaProduct | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchProduct = useCallback(async () => {
    if (!productId) {
      setProduct(null);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const response = await medusaAPI.getProduct(productId);
      setProduct(response.product);
      
    } catch (err) {
      console.error('Error fetching product:', err);
      if (err instanceof MedusaAPIError) {
        setError(err.message);
      } else if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('Failed to fetch product');
      }
    } finally {
      setLoading(false);
    }
  }, [productId]);

  useEffect(() => {
    fetchProduct();
  }, [fetchProduct]);

  return {
    product,
    loading,
    error,
    refetch: fetchProduct,
  };
}

// Hook for categories with automatic loading
export function useMedusaBackendCategories() {
  const [categories, setCategories] = useState<MedusaCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchCategories = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await medusaAPI.getCategories();
      setCategories(response.product_categories);
      
    } catch (err) {
      console.error('Error fetching categories:', err);
      if (err instanceof MedusaAPIError) {
        setError(err.message);
      } else if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('Failed to fetch categories');
      }
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  return {
    categories,
    loading,
    error,
    refetch: fetchCategories,
  };
}
