import { NextRequest, NextResponse } from 'next/server';

const BACKEND_BASE_URL = 'http://localhost:9000';

export async function GET(request: NextRequest) {
  try {
    // Get headers from the original request
    const tenantId = request.headers.get('x-tenant-id') || 'tenant-electronics-001';
    const publishableApiKey = request.headers.get('x-publishable-api-key') || 'pk_51719ff15f2d615335059dc2bad507d3446466d7d6e2af5c777cf07d7ac53af0';

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const queryString = searchParams.toString();

    console.log('[Proxy] Store products request:', {
      tenantId,
      queryString,
      timestamp: new Date().toISOString(),
    });

    // Make request to backend
    const backendUrl = `${BACKEND_BASE_URL}/store/test-products${queryString ? `?${queryString}` : ''}`;
    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-tenant-id': tenantId,
        'x-publishable-api-key': publishableApiKey,
      },
    });

    if (!response.ok) {
      console.error('[Proxy] Backend error:', {
        status: response.status,
        statusText: response.statusText,
        url: backendUrl,
      });
      
      return NextResponse.json(
        { 
          error: 'Products not found',
          tenant_id: tenantId,
          status: response.status
        },
        { status: response.status }
      );
    }

    const data = await response.json();
    
    console.log('[Proxy] Store products success:', {
      productCount: data.products?.length || 0,
      tenantId: data.tenant_id,
    });

    return NextResponse.json(data);
  } catch (error) {
    console.error('[Proxy] Store products error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch store products',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
