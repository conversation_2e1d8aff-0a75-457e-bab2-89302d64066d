import { NextResponse } from 'next/server';
import { getSubcategoriesByParent } from '@/lib/strapi-api';
// TODO: Replace with real API integration

export async function GET(
  request: Request,
  { params }: { params: { parentId: string } }
) {
  try {
    const parentId = params.parentId;

    console.log('🚀 Subcategories API: Fetching subcategories for parent:', parentId);

    // First try to fetch subcategories from Strapi CMS
    let response = null;

    // Try parsing as number for Strapi
    const numericParentId = parseInt(parentId);
    if (!isNaN(numericParentId)) {
      try {
        response = await getSubcategoriesByParent(numericParentId);
      } catch (error) {
        console.log('⚠️ Subcategories API: Strapi fetch failed, trying mock data...');
      }
    }

    // If not found in Strapi, return empty array
    if (!response || !response.data || response.data.length === 0) {
      console.log('⚠️ Subcategories API: Not found in Strapi');
      // TODO: Add fallback to real API if needed
      return NextResponse.json({
        success: true,
        data: [],
        message: 'No subcategories found'
      });
    }

    if (!response || !response.data) {
      return NextResponse.json({
        success: true,
        data: [],
        meta: {
          pagination: {
            page: 1,
            pageSize: 0,
            pageCount: 0,
            total: 0
          }
        }
      });
    }

    console.log('✅ Subcategories API: Successfully fetched subcategories');
    console.log('📊 Subcategories count:', response.data?.length || 0);

    return NextResponse.json({
      success: true,
      data: response.data,
      meta: response.meta,
    });
  } catch (error) {
    console.error('❌ Subcategories API: Error fetching subcategories:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch subcategories',
        data: null,
      },
      { status: 500 }
    );
  }
}
