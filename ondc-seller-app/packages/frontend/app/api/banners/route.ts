import { NextRequest, NextResponse } from 'next/server';
import { getBanners, getBannersByTenant } from '@/lib/strapi-api';

/**
 * GET /api/banners
 *
 * Fetch banner slides for the homepage hero section.
 * Supports tenant-specific filtering via tenantId query parameter.
 * First tries to fetch from Strapi CMS, falls back to mock data if needed.
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');
    const format = searchParams.get('format'); // Add format parameter

    console.log(`🚀 [API] /api/banners - Fetching banner slides from Strapi CMS${tenantId ? ` for tenant ${tenantId}` : ''}...`);

    // Fetch banners with optional tenant filtering
    // const strapiResponse = tenantId ? await getBannersByTenant(tenantId) : await getBanners();
    const strapiResponse =  await getBanners();

    // If format=raw is requested, return the raw Strapi response (for compatibility with existing components)
    if (format === 'raw') {
      console.log('🔄 [API] /api/banners - Returning raw Strapi response format');
      return NextResponse.json(strapiResponse);
    }

    if (strapiResponse.data && strapiResponse.data.length > 0) {
      console.log(
        '✅ [API] /api/banners - Successfully fetched from Strapi:',
        strapiResponse.data.length,
        'banners'
      );

      // Transform Strapi banner data to match expected format
      const transformedBanners = strapiResponse.data.map((banner: any) => ({
        id: banner.id,
        title: banner.attributes?.title || banner.title || 'Banner Title',
        subtitle: banner.attributes?.subtitle || banner.subtitle || '',
        description: banner.attributes?.description || banner.description || '',
        image:
          banner.attributes?.image?.data?.attributes?.url ||
          banner.image ||
          '/images/banners/default-banner.jpg',
        buttonText: banner.attributes?.buttonText || banner.buttonText || 'Learn More',
        buttonLink: banner.attributes?.buttonLink || banner.buttonLink || '/',
        backgroundColor:
          banner.attributes?.backgroundColor ||
          banner.backgroundColor ||
          'bg-gradient-to-r from-blue-600 to-purple-600',
        active: banner.attributes?.active ?? banner.active ?? true,
        position: banner.attributes?.position || banner.position || 1,
        createdAt: banner.attributes?.createdAt || banner.createdAt || new Date().toISOString(),
        updatedAt: banner.attributes?.updatedAt || banner.updatedAt || new Date().toISOString(),
      }));

      return NextResponse.json({
        success: true,
        data: transformedBanners,
        meta: {
          total: transformedBanners.length,
          page: 1,
          pageSize: transformedBanners.length,
          pageCount: 1,
        },
        timestamp: new Date().toISOString(),
      });
    } else {
      console.log('⚠️ [API] /api/banners - No active banners found in Strapi CMS');
      return NextResponse.json({
        success: true,
        data: [],
        meta: {
          total: 0,
          page: 1,
          pageSize: 0,
          pageCount: 0,
        },
        timestamp: new Date().toISOString(),
      });
    }
  } catch (error) {
    console.error('❌ [API] /api/banners - Error fetching banners from Strapi CMS:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch banners from Strapi CMS',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
