import { NextResponse } from 'next/server';
import { getCategoryBySlug } from '@/lib/strapi-api';
// TODO: Replace with real API integration

export async function GET(
  request: Request,
  { params }: { params: { slug: string } }
) {
  try {
    const slug = params.slug;
    const url = new URL(request.url);
    const tenantId = url.searchParams.get('tenantId');

    console.log('🚀 Category Detail API: Fetching category by slug:', slug);
    console.log('🏢 Category Detail API: Tenant ID:', tenantId);

    // First try to fetch category from Strapi CMS by slug
    let category = await getCategoryBySlug(slug);

    // If not found in Strapi, return null
    if (!category) {
      console.log('⚠️ Category Detail API: Not found in Strapi');
      // TODO: Add fallback to real API if needed
      category = null;
          id: mockCategory.id,
          documentId: mockCategory.documentId,
          name: mockCategory.name,
          slug: mockCategory.slug,
          description: mockCategory.description,
          short_description: mockCategory.short_description,
          image: mockCategory.image,
          icon: mockCategory.icon,
          active: mockCategory.active,
          sort_order: mockCategory.sort_order,
          meta_title: mockCategory.meta_title,
          meta_description: mockCategory.meta_description,
          tenant_id: mockCategory.tenant_id,
          tenant: mockCategory.tenant,
          publishedAt: mockCategory.publishedAt,
          createdAt: mockCategory.createdAt,
          updatedAt: mockCategory.updatedAt,
          attributes: {
            name: mockCategory.name,
            slug: mockCategory.slug,
            description: mockCategory.description,
            short_description: mockCategory.short_description,
            image: mockCategory.image,
            icon: mockCategory.icon,
            active: mockCategory.active,
            sort_order: mockCategory.sort_order,
            meta_title: mockCategory.meta_title,
            meta_description: mockCategory.meta_description,
          }
        };
      }
    }

    if (!category) {
      return NextResponse.json(
        {
          success: false,
          error: 'Category not found',
          data: null,
        },
        { status: 404 }
      );
    }

    console.log('✅ Category Detail API: Successfully fetched category');
    console.log('📊 Category:', category.name);

    return NextResponse.json({
      success: true,
      data: category,
    });
  } catch (error) {
    console.error('❌ Category Detail API: Error fetching category:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch category',
        data: null,
      },
      { status: 500 }
    );
  }
}
