'use client';

import React, { useEffect, useState } from 'react';
import MultiTenantHeroBanner from '@/components/homepage/MultiTenantHeroBanner';
import MedusaProductsCarousel from '@/components/homepage/MedusaProductsCarousel';
import MedusaTopDeals from '@/components/homepage/MedusaTopDeals';
import MedusaFeaturedProducts from '@/components/homepage/MedusaFeaturedProducts';
import MedusaHotPicks from '@/components/homepage/MedusaHotPicks';
import { MedusaCartProvider } from '@/hooks/useMedusaCart';

export default function HomePage() {
  const [pageKey, setPageKey] = useState(0);

  useEffect(() => {
    // Force re-render when component mounts
    console.log('HomePage component mounted/re-mounted');
    setPageKey(prev => prev + 1);
  }, []);

  // Listen for route changes and refresh banner
  useEffect(() => {
    const handleRouteChange = () => {
      console.log('Route changed, refreshing banner...');
      setPageKey(prev => prev + 1);
    };

    // Listen for browser navigation events
    window.addEventListener('popstate', handleRouteChange);

    return () => {
      window.removeEventListener('popstate', handleRouteChange);
    };
  }, []);

  return (
    <MedusaCartProvider>
      <div className="min-h-screen bg-white">
        {/* Multi-Tenant Hero Banner Carousel */}
        <MultiTenantHeroBanner key={`hero-banner-${pageKey}`} />

        {/* Latest Products Carousel - Real Medusa v2 API data */}
        <MedusaProductsCarousel
          title="Latest Products"
          maxProducts={8}
          showViewAll={true}
          viewAllLink="/products"
          productType="latest"
        />

        {/* Top Deals Section - Real Medusa v2 API data */}
        <MedusaTopDeals />

        {/* Featured Products Section - Real Medusa v2 API data */}
        <MedusaFeaturedProducts />

        {/* Hot Picks Section - Real Medusa v2 API data */}
        <MedusaHotPicks />

        {/* Success Message */}
        <div className="bg-green-50 border-l-4 border-green-400 py-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex">
              <div className="ml-3">
                <p className="text-sm text-green-700">
                  ✅ <strong>Real API Integration Complete!</strong> All products are now loaded from the confirmed working Medusa v2 backend with real database operations.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MedusaCartProvider>
  );
}
