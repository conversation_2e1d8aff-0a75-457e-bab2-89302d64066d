'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Box,
  Container,
  Typography,
  Button,
  Card,
  CardContent,
  Grid,
  Chip,
  Avatar,
  IconButton,
  Menu,
  MenuItem,
  Alert,
  Breadcrumbs,
  Link,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Visibility as VisibilityIcon,
  Delete as DeleteIcon,
  NavigateNext as NavigateNextIcon,
  Warning as WarningIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';
import { toast } from 'react-hot-toast';

// Mock Products Data
const MOCK_PRODUCTS = [
  {
    id: 1,
    name: 'Premium Wireless Headphones',
    description: 'High-quality wireless headphones with active noise cancellation',
    price: 8999,
    sale_price: 7999,
    sku: 'WH-001',
    inventory_quantity: 45,
    product_status: 'Published',
    featured: true,
    subcategory: 'Audio Equipment',
    image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop',
    createdAt: '2025-01-01T00:00:00.000Z',
    updatedAt: '2025-01-01T00:00:00.000Z'
  },
  {
    id: 2,
    name: 'Smart Fitness Watch',
    description: 'Advanced fitness tracking watch with heart rate monitoring',
    price: 12999,
    sale_price: undefined,
    sku: 'SW-002',
    inventory_quantity: 32,
    product_status: 'Published',
    featured: false,
    subcategory: 'Wearables',
    image: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop',
    createdAt: '2025-01-02T00:00:00.000Z',
    updatedAt: '2025-01-02T00:00:00.000Z'
  },
  {
    id: 3,
    name: 'Wireless Gaming Mouse',
    description: 'High-precision wireless gaming mouse with RGB lighting',
    price: 4999,
    sale_price: 3999,
    sku: 'GM-003',
    inventory_quantity: 0,
    product_status: 'Out of Stock',
    featured: false,
    subcategory: 'Gaming Accessories',
    image: 'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=400&fit=crop',
    createdAt: '2025-01-03T00:00:00.000Z',
    updatedAt: '2025-01-03T00:00:00.000Z'
  },
  {
    id: 4,
    name: 'Bluetooth Speaker',
    description: 'Portable Bluetooth speaker with 360-degree sound',
    price: 6999,
    sale_price: undefined,
    sku: 'BS-004',
    inventory_quantity: 28,
    product_status: 'Draft',
    featured: false,
    subcategory: 'Audio Equipment',
    image: 'https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=400&h=400&fit=crop',
    createdAt: '2025-01-04T00:00:00.000Z',
    updatedAt: '2025-01-04T00:00:00.000Z'
  },
  {
    id: 5,
    name: 'USB-C Hub',
    description: 'Multi-port USB-C hub with HDMI and power delivery',
    price: 2999,
    sale_price: 2499,
    sku: 'UH-005',
    inventory_quantity: 67,
    product_status: 'Published',
    featured: true,
    subcategory: 'Computer Accessories',
    image: 'https://images.unsplash.com/photo-1625842268584-8f3296236761?w=400&h=400&fit=crop',
    createdAt: '2025-01-05T00:00:00.000Z',
    updatedAt: '2025-01-05T00:00:00.000Z'
  }
];

export default function DemoAdminProductsPage() {
  const router = useRouter();
  const [products, setProducts] = useState(MOCK_PRODUCTS);
  const [loading, setLoading] = useState(true);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedProduct, setSelectedProduct] = useState<any>(null);
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    isOpen: boolean;
    product: any | null;
  }>({
    isOpen: false,
    product: null
  });
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, product: any) => {
    setAnchorEl(event.currentTarget);
    setSelectedProduct(product);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedProduct(null);
  };

  const handleView = (product: any) => {
    router.push(`/demo-admin-products/${product.id}`);
    handleMenuClose();
  };

  const handleEdit = (product: any) => {
    router.push(`/demo-admin-products/${product.id}/edit`);
    handleMenuClose();
  };

  const handleDelete = (product: any) => {
    setDeleteConfirmation({
      isOpen: true,
      product: product
    });
    handleMenuClose();
  };

  const confirmDelete = async () => {
    if (!deleteConfirmation.product) return;

    setIsDeleting(true);
    try {
      // Simulate API call to delete product
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Remove product from local state
      setProducts(prev => prev.filter(p => p.id !== deleteConfirmation.product!.id));

      // Show success toast
      toast.success(`Product "${deleteConfirmation.product.name}" deleted successfully!`);

    } catch (error) {
      console.error('Error deleting product:', error);
      toast.error('Failed to delete product. Please try again.');
    } finally {
      setIsDeleting(false);
      setDeleteConfirmation({ isOpen: false, product: null });
    }
  };

  const cancelDelete = () => {
    setDeleteConfirmation({ isOpen: false, product: null });
  };

  const handleAddProduct = () => {
    router.push('/demo-admin-products/new');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Published': return 'success';
      case 'Draft': return 'warning';
      case 'Out of Stock': return 'error';
      default: return 'default';
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
    }).format(price / 100);
  };

  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ py: 4 }}>
        <Typography>Loading products...</Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Breadcrumbs separator={<NavigateNextIcon fontSize="small" />} sx={{ mb: 2 }}>
          <Link color="inherit" href="/" underline="hover">
            Home
          </Link>
          <Link color="inherit" href="/demo-admin-products" underline="hover">
            Demo Admin
          </Link>
          <Typography color="text.primary">Products</Typography>
        </Breadcrumbs>

        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1" fontWeight="bold">
            Products Management
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAddProduct}
            size="large"
          >
            Add New Product
          </Button>
        </Box>

        <Alert severity="info" sx={{ mb: 3 }}>
          This is a demo admin interface showing mock product data. In the real admin panel, this would connect to your actual product database.
        </Alert>

        {/* Stats */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="text.secondary" gutterBottom>
                  Total Products
                </Typography>
                <Typography variant="h4">
                  {products.length}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="text.secondary" gutterBottom>
                  Published
                </Typography>
                <Typography variant="h4" color="success.main">
                  {products.filter(p => p.product_status === 'Published').length}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="text.secondary" gutterBottom>
                  Draft
                </Typography>
                <Typography variant="h4" color="warning.main">
                  {products.filter(p => p.product_status === 'Draft').length}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="text.secondary" gutterBottom>
                  Out of Stock
                </Typography>
                <Typography variant="h4" color="error.main">
                  {products.filter(p => p.product_status === 'Out of Stock').length}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>

      {/* Products Grid */}
      <Grid container spacing={3}>
        {products.map((product) => (
          <Grid item xs={12} sm={6} md={4} lg={3} key={product.id}>
            <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              <Box sx={{ position: 'relative' }}>
                <Avatar
                  src={product.image}
                  variant="square"
                  sx={{ width: '100%', height: 200 }}
                />
                <IconButton
                  sx={{
                    position: 'absolute',
                    top: 8,
                    right: 8,
                    bgcolor: 'rgba(255, 255, 255, 0.9)',
                    '&:hover': { bgcolor: 'rgba(255, 255, 255, 1)' },
                  }}
                  onClick={(e) => handleMenuOpen(e, product)}
                >
                  <MoreVertIcon />
                </IconButton>
                {product.featured && (
                  <Chip
                    label="Featured"
                    color="primary"
                    size="small"
                    sx={{
                      position: 'absolute',
                      top: 8,
                      left: 8,
                    }}
                  />
                )}
              </Box>
              <CardContent sx={{ flexGrow: 1 }}>
                <Typography variant="h6" component="h2" gutterBottom noWrap>
                  {product.name}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {product.description}
                </Typography>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                  <Typography variant="body2" color="text.secondary">
                    SKU: {product.sku}
                  </Typography>
                  <Chip
                    label={product.product_status}
                    color={getStatusColor(product.product_status) as any}
                    size="small"
                  />
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                  <Box>
                    <Typography variant="h6" color="primary">
                      {formatPrice(product.sale_price || product.price)}
                    </Typography>
                    {product.sale_price && (
                      <Typography variant="body2" color="text.secondary" sx={{ textDecoration: 'line-through' }}>
                        {formatPrice(product.price)}
                      </Typography>
                    )}
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    Stock: {product.inventory_quantity}
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => selectedProduct && handleView(selectedProduct)}>
          <VisibilityIcon sx={{ mr: 1 }} />
          View
        </MenuItem>
        <MenuItem onClick={() => selectedProduct && handleEdit(selectedProduct)}>
          <EditIcon sx={{ mr: 1 }} />
          Edit
        </MenuItem>
        <MenuItem onClick={() => selectedProduct && handleDelete(selectedProduct)} sx={{ color: 'error.main' }}>
          <DeleteIcon sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteConfirmation.isOpen}
        onClose={!isDeleting ? cancelDelete : undefined}
        maxWidth="sm"
        fullWidth
        slotProps={{
          paper: {
            sx: {
              borderRadius: 2,
              p: 1,
            },
          },
          backdrop: {
            sx: {
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
            },
          },
        }}
      >
        <DialogTitle sx={{ pb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: 48,
                height: 48,
                borderRadius: '50%',
                bgcolor: 'error.50',
                color: 'error.main',
              }}
            >
              <WarningIcon sx={{ fontSize: 24 }} />
            </Box>
            <Box>
              <Typography variant="h6" component="h2" fontWeight="bold">
                Delete Product
              </Typography>
              <Typography variant="body2" color="text.secondary">
                This action cannot be undone
              </Typography>
            </Box>
          </Box>
        </DialogTitle>

        <DialogContent sx={{ pt: 1, pb: 2 }}>
          <Alert severity="warning" sx={{ mb: 2 }}>
            <Typography variant="body2">
              Are you sure you want to delete <strong>"{deleteConfirmation.product?.name}"</strong>?
            </Typography>
          </Alert>
          <Typography variant="body2" color="text.secondary">
            This will permanently remove the product from your catalog. All associated data,
            including sales history and customer reviews, will be lost.
          </Typography>
        </DialogContent>

        <DialogActions sx={{ px: 3, pb: 3, gap: 1 }}>
          <Button
            onClick={cancelDelete}
            variant="outlined"
            startIcon={<CancelIcon />}
            disabled={isDeleting}
            sx={{ minWidth: 100 }}
          >
            Cancel
          </Button>
          <Button
            onClick={confirmDelete}
            variant="contained"
            color="error"
            startIcon={<DeleteIcon />}
            disabled={isDeleting}
            sx={{ minWidth: 100 }}
          >
            {isDeleting ? 'Deleting...' : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
}
