'use client';

import { useParams } from 'next/navigation';
import MedusaProductDetailPage from '../../../components/products/MedusaProductDetailPage';
import { MedusaCartProvider } from '@/hooks/useMedusaCart';

export default function ProductPage() {
  const params = useParams();
  const productId = params.id as string;

  return (
    <MedusaCartProvider>
      <MedusaProductDetailPage productId={productId} />
    </MedusaCartProvider>
  );
}

export async function generateMetadata({ params }: { params: { id: string } }) {
  // In a real app, you would fetch the product data here for SEO
  return {
    title: `Product Details - ${params.id}`,
    description: 'View detailed information about this product',
  };
}
