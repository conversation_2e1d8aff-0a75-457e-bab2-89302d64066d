import ProductDetailPage from '../../../components/products/ProductDetailPage';

export default function ProductPage() {
  console.log("product page calling:::::::::::::")
  return <ProductDetailPage />;
}

export async function generateMetadata({ params }: { params: { id: string } }) {
  // In a real app, you would fetch the product data here for SEO
  return {
    title: `Product Details - ${params.id}`,
    description: 'View detailed information about this product',
  };
}
