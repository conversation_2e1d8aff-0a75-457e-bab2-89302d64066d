'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useSearchParams } from 'next/navigation';
import ProductListingPage, { Product, ProductListingConfig } from '@/components/common/ProductListingPage';
// TODO: Replace with real API calls
// import { getProductsByCategory } from '@/lib/medusa-backend-api';

// Define subcategories for each category
const categorySubcategories: Record<string, Array<{ id: string; name: string }>> = {
  electronics: [
    { id: 'audio-equipment', name: 'Audio Equipment' },
    { id: 'wearables', name: 'Wearables' },
    { id: 'cameras-photography', name: 'Cameras & Photography' },
    { id: 'computer-accessories', name: 'Computer Accessories' },
    { id: 'mobile-accessories', name: 'Mobile Accessories' },
    { id: 'gaming', name: 'Gaming' },
  ],
  fashion: [
    { id: 'mens-clothing', name: "Men's Clothing" },
    { id: 'womens-clothing', name: "Women's Clothing" },
    { id: 'shoes', name: 'Shoes' },
    { id: 'accessories', name: 'Accessories' },
    { id: 'bags', name: 'Bags' },
  ],
  'home-garden': [
    { id: 'furniture', name: 'Furniture' },
    { id: 'decor', name: 'Home Decor' },
    { id: 'kitchen', name: 'Kitchen & Dining' },
    { id: 'garden', name: 'Garden & Outdoor' },
    { id: 'storage', name: 'Storage & Organization' },
  ],
  'sports-fitness': [
    { id: 'fitness-equipment', name: 'Fitness Equipment' },
    { id: 'sports-gear', name: 'Sports Gear' },
    { id: 'outdoor-recreation', name: 'Outdoor Recreation' },
    { id: 'athletic-wear', name: 'Athletic Wear' },
  ],
  'books-media': [
    { id: 'books', name: 'Books' },
    { id: 'movies', name: 'Movies & TV' },
    { id: 'music', name: 'Music' },
    { id: 'games', name: 'Video Games' },
  ],
  'beauty-health': [
    { id: 'skincare', name: 'Skincare' },
    { id: 'makeup', name: 'Makeup' },
    { id: 'health-supplements', name: 'Health Supplements' },
    { id: 'personal-care', name: 'Personal Care' },
  ],
  furniture: [
    { id: 'sofas-couches', name: 'Sofas & Couches' },
    { id: 'chairs-recliners', name: 'Chairs & Recliners' },
    { id: 'tables-desks', name: 'Tables & Desks' },
    { id: 'beds-mattresses', name: 'Beds & Mattresses' },
    { id: 'wardrobes-storage', name: 'Wardrobes & Storage' },
  ],
};

export default function CategoryDetailPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const categorySlug = params.slug as string;
  const subcategory = searchParams.get('subcategory');

  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeSubcategory, setActiveSubcategory] = useState<string>('');

  useEffect(() => {
    if (categorySlug) {
      loadProducts(categorySlug, subcategory);
      setActiveSubcategory(subcategory || '');
    }
  }, [categorySlug, subcategory]);

  const loadProducts = async (categoryName: string, subcategoryName?: string) => {
    setIsLoading(true);
    try {
      // TODO: Replace with real API call to Medusa backend
      const products: Product[] = [];
      setProducts(products);
    } catch (error) {
      console.error('Error loading products:', error);
      setProducts([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubcategoryChange = (subcategoryId: string) => {
    if (subcategoryId === 'all') {
      setActiveSubcategory('');
      if (categorySlug) {
        loadProducts(categorySlug);
      }
    } else {
      setActiveSubcategory(subcategoryId);
      if (categorySlug) {
        loadProducts(categorySlug, subcategoryId);
      }
    }
  };

  if (!categorySlug) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Category Not Found</h1>
          <p className="text-gray-600">Please select a valid category.</p>
        </div>
      </div>
    );
  }

  const categoryName = categorySlug.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  const subcategoryFilters = categorySubcategories[categorySlug] || [];

  // Add "All" option to subcategory filters
  const allSubcategoryFilters = [
    { id: 'all', name: 'All', count: products.length },
    ...subcategoryFilters.map(sub => ({
      ...sub,
      count: products.filter(p => p.subcategory === sub.id).length
    }))
  ];

  // Get subcategory display name
  const subcategoryDisplayName = activeSubcategory ?
    allSubcategoryFilters.find(sub => sub.id === activeSubcategory)?.name : null;

  // Format category name for display
  const categoryDisplayName = categoryName.charAt(0).toUpperCase() + categoryName.slice(1);

  const config: ProductListingConfig = {
    title: activeSubcategory && activeSubcategory !== 'all' ? subcategoryDisplayName || activeSubcategory : categoryDisplayName,
    subtitle: activeSubcategory && activeSubcategory !== 'all' ?
      `Discover our collection of ${subcategoryDisplayName?.toLowerCase() || 'products'}` :
      `Discover amazing ${categoryName.toLowerCase()} products`,
    showDiscountBadge: true,
    showSaleBadge: true,
    showPriceComparison: true,
    showSavingsAmount: true,
    buttonText: 'Add to Cart',
    buttonStyle: 'primary',
    breadcrumbs: activeSubcategory && activeSubcategory !== 'all' ? [
      { label: 'Home', href: '/' },
      { label: categoryDisplayName, href: `/categories/${categorySlug}` },
      { label: subcategoryDisplayName || activeSubcategory }
    ] : [
      { label: 'Home', href: '/' },
      { label: categoryDisplayName }
    ],
    subcategoryFilters: allSubcategoryFilters, // Always show subcategory filters on main category page
    activeSubcategory: activeSubcategory,
    showSubcategoryFilter: true // Always show subcategory filters on category page
  };

  return (
    <ProductListingPage
      products={products}
      config={config}
      isLoading={isLoading}
      onSubcategoryChange={handleSubcategoryChange}
    />
  );
}


