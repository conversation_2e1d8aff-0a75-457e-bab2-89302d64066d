'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import ProductListingPage, { Product, ProductListingConfig } from '@/components/common/ProductListingPage';
// TODO: Replace with real API calls
// import { getProductsBySubcategory } from '@/lib/medusa-backend-api';

// Define subcategories for each category
const categorySubcategories: Record<string, Array<{ id: string; name: string }>> = {
  furniture: [
    { id: 'sofas-couches', name: 'Sofas & Couches' },
    { id: 'chairs-recliners', name: 'Chairs & Recliners' },
    { id: 'tables-desks', name: 'Tables & Desks' },
    { id: 'beds-mattresses', name: 'Beds & Mattresses' },
    { id: 'wardrobes-storage', name: 'Wardrobes & Storage' },
  ],
  electronics: [
    { id: 'audio-equipment', name: 'Audio Equipment' },
    { id: 'wearables', name: 'Wearables' },
    { id: 'cameras-photography', name: 'Cameras & Photography' },
    { id: 'computer-accessories', name: 'Computer Accessories' },
    { id: 'mobile-accessories', name: 'Mobile Accessories' },
    { id: 'gaming', name: 'Gaming' },
  ],
  fashion: [
    { id: 'mens-clothing', name: "Men's Clothing" },
    { id: 'womens-clothing', name: "Women's Clothing" },
    { id: 'shoes', name: 'Shoes' },
    { id: 'accessories', name: 'Accessories' },
    { id: 'bags', name: 'Bags' },
  ],
};

export default function SubcategoryDetailPage() {
  const params = useParams();
  const categoryName = params.slug as string;
  const subcategoryName = params.subcategoryName as string;

  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (categoryName && subcategoryName) {
      loadProducts(categoryName, subcategoryName);
    }
  }, [categoryName, subcategoryName]);

  const loadProducts = async (categorySlug: string, subcategorySlug: string) => {
    setIsLoading(true);
    try {
      // TODO: Replace with real API call to Medusa backend
      const products: Product[] = [];
      setProducts(products);
    } catch (error) {
      console.error('Error loading products:', error);
      setProducts([]);
    } finally {
      setIsLoading(false);
    }
  };

  if (!categoryName || !subcategoryName) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Page Not Found</h1>
          <p className="text-gray-600">Please select a valid category and subcategory.</p>
        </div>
      </div>
    );
  }

  // Get display names
  const categoryDisplayName = categoryName.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  const subcategoryInfo = categorySubcategories[categoryName]?.find(sub => sub.id === subcategoryName);
  const subcategoryDisplayName = subcategoryInfo?.name || subcategoryName.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

  // Configuration for ProductListingPage
  const config: ProductListingConfig = {
    title: subcategoryDisplayName,
    description: `Discover our collection of ${subcategoryDisplayName.toLowerCase()} products`,
    showSubcategoryFilter: false, // This is the key difference - no subcategory filter
    showFilters: true,
    showSort: true,
    showViewToggle: true,
    breadcrumbs: [
      { label: 'Home', href: '/' },
      { label: categoryDisplayName, href: `/categories/${categoryName}` },
      { label: subcategoryDisplayName, href: `/categories/${categoryName}/${subcategoryName}` }
    ]
  };

  return (
    <ProductListingPage
      products={products}
      isLoading={isLoading}
      config={config}
    />
  );
}
