'use client';

import React, { useState } from 'react';
import { TenantProvider } from '../../lib/contexts/TenantContext';
import MultiTenantStoreInterface from '../../components/store/MultiTenantStoreInterface';
import MultiTenantAdminInterface from '../../components/admin/MultiTenantAdminInterface';
import { 
  BuildingStorefrontIcon, 
  CogIcon,
  ArrowRightIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

type DemoMode = 'overview' | 'store' | 'admin';

// Overview Component
function DemoOverview({ setMode }: { setMode: (mode: DemoMode) => void }) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Multi-Tenant ONDC Seller App
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Experience our multi-tenant e-commerce platform with real-time tenant switching, 
            isolated data, and comprehensive admin controls.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {/* Store Interface Card */}
          <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
            <div className="p-8">
              <div className="flex items-center mb-4">
                <div className="flex-shrink-0">
                  <BuildingStorefrontIcon className="h-8 w-8 text-blue-600" />
                </div>
                <div className="ml-4">
                  <h2 className="text-2xl font-bold text-gray-900">Store Interface</h2>
                  <p className="text-gray-600">Customer-facing storefront</p>
                </div>
              </div>
              
              <div className="space-y-3 mb-6">
                <div className="flex items-center text-sm text-gray-600">
                  <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                  Real-time tenant switching
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                  Tenant-specific products & branding
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                  Store information display
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                  ONDC configuration details
                </div>
              </div>
              
              <button
                onClick={() => setMode('store')}
                className="w-full bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center justify-center"
              >
                View Store Interface
                <ArrowRightIcon className="h-4 w-4 ml-2" />
              </button>
            </div>
          </div>

          {/* Admin Interface Card */}
          <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
            <div className="p-8">
              <div className="flex items-center mb-4">
                <div className="flex-shrink-0">
                  <CogIcon className="h-8 w-8 text-indigo-600" />
                </div>
                <div className="ml-4">
                  <h2 className="text-2xl font-bold text-gray-900">Admin Interface</h2>
                  <p className="text-gray-600">Administrative controls</p>
                </div>
              </div>
              
              <div className="space-y-3 mb-6">
                <div className="flex items-center text-sm text-gray-600">
                  <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                  JWT authentication
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                  Tenant configuration management
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                  Multi-tenant isolation testing
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                  Real-time data validation
                </div>
              </div>
              
              <button
                onClick={() => setMode('admin')}
                className="w-full bg-indigo-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-indigo-700 transition-colors flex items-center justify-center"
              >
                View Admin Interface
                <ArrowRightIcon className="h-4 w-4 ml-2" />
              </button>
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div className="bg-white rounded-xl shadow-lg p-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            Key Features Demonstrated
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="bg-blue-100 rounded-full p-3 w-12 h-12 mx-auto mb-4 flex items-center justify-center">
                <span className="text-blue-600 font-bold">1</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Multi-Tenant Architecture</h4>
              <p className="text-sm text-gray-600">
                Complete data isolation between Electronics and Fashion tenants
              </p>
            </div>
            
            <div className="text-center">
              <div className="bg-green-100 rounded-full p-3 w-12 h-12 mx-auto mb-4 flex items-center justify-center">
                <span className="text-green-600 font-bold">2</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Real-Time API Integration</h4>
              <p className="text-sm text-gray-600">
                Live data from Medusa backend with proper tenant headers
              </p>
            </div>
            
            <div className="text-center">
              <div className="bg-purple-100 rounded-full p-3 w-12 h-12 mx-auto mb-4 flex items-center justify-center">
                <span className="text-purple-600 font-bold">3</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">ONDC Compliance</h4>
              <p className="text-sm text-gray-600">
                Full ONDC configuration and participant management
              </p>
            </div>
            
            <div className="text-center">
              <div className="bg-yellow-100 rounded-full p-3 w-12 h-12 mx-auto mb-4 flex items-center justify-center">
                <span className="text-yellow-600 font-bold">4</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Security Testing</h4>
              <p className="text-sm text-gray-600">
                Built-in isolation tests to verify tenant security
              </p>
            </div>
            
            <div className="text-center">
              <div className="bg-red-100 rounded-full p-3 w-12 h-12 mx-auto mb-4 flex items-center justify-center">
                <span className="text-red-600 font-bold">5</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">TypeScript SDK</h4>
              <p className="text-sm text-gray-600">
                Fully typed API client with React hooks integration
              </p>
            </div>
            
            <div className="text-center">
              <div className="bg-indigo-100 rounded-full p-3 w-12 h-12 mx-auto mb-4 flex items-center justify-center">
                <span className="text-indigo-600 font-bold">6</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Error Handling</h4>
              <p className="text-sm text-gray-600">
                Comprehensive error handling and user feedback
              </p>
            </div>
          </div>
        </div>

        {/* API Endpoints Section */}
        <div className="bg-white rounded-xl shadow-lg p-8 mt-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            Available API Endpoints
          </h3>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div>
              <h4 className="font-semibold text-gray-900 mb-4">Store APIs (Public)</h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center">
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium mr-3">GET</span>
                  <code className="text-gray-600">/store/test-info</code>
                </div>
                <div className="flex items-center">
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium mr-3">GET</span>
                  <code className="text-gray-600">/store/test-products</code>
                </div>
                <div className="flex items-center">
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium mr-3">GET</span>
                  <code className="text-gray-600">/store/test-products/{'{id}'}</code>
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="font-semibold text-gray-900 mb-4">Admin APIs (Protected)</h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center">
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium mr-3">GET</span>
                  <code className="text-gray-600">/admin/tenant</code>
                </div>
                <div className="flex items-center">
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium mr-3">GET</span>
                  <code className="text-gray-600">/admin/test-multi-tenant</code>
                </div>
                <div className="flex items-center">
                  <span className="bg-orange-100 text-orange-800 px-2 py-1 rounded text-xs font-medium mr-3">POST</span>
                  <code className="text-gray-600">/auth/user/emailpass</code>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Main Demo Page Component
export default function ApiDemoPage() {
  const [mode, setMode] = useState<DemoMode>('overview');

  if (mode === 'overview') {
    return <DemoOverview setMode={setMode} />;
  }

  return (
    <TenantProvider>
      <div className="min-h-screen bg-gray-50">
        {/* Navigation Bar */}
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => setMode('overview')}
                  className="text-gray-600 hover:text-gray-900 font-medium"
                >
                  ← Back to Overview
                </button>
                <div className="h-6 border-l border-gray-300" />
                <h1 className="text-lg font-semibold text-gray-900">
                  {mode === 'store' ? 'Store Interface Demo' : 'Admin Interface Demo'}
                </h1>
              </div>
              
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => setMode('store')}
                  className={`px-3 py-2 rounded-md text-sm font-medium ${
                    mode === 'store'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  Store
                </button>
                <button
                  onClick={() => setMode('admin')}
                  className={`px-3 py-2 rounded-md text-sm font-medium ${
                    mode === 'admin'
                      ? 'bg-indigo-100 text-indigo-700'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  Admin
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Interface Content */}
        {mode === 'store' && <MultiTenantStoreInterface />}
        {mode === 'admin' && <MultiTenantAdminInterface />}
      </div>
    </TenantProvider>
  );
}
