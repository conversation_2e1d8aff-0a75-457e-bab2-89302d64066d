'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { useTenant } from '@/contexts/TenantContext';
import ProductListingPage, { Product, ProductListingConfig } from '@/components/common/ProductListingPage';
// TODO: Replace with real API calls

function DealsPageContent() {
  const searchParams = useSearchParams();
  const dealType = searchParams?.get('type') || 'top-deals';
  const { tenantId } = useTenant();

  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadProducts();
  }, [dealType, tenantId]);

  const loadProducts = async () => {
    setIsLoading(true);
    try {
      let rawProducts: any[] = [];

      switch (dealType) {
        case 'featured-products':
          // TODO: Replace with real API call for featured products
          rawProducts = [];
          break;
            originalPrice: product.originalPrice ? product.originalPrice / 100 : product.price / 100,
            image: product.image,
            rating: product.rating,
            reviewCount: product.reviewCount,
            category: product.category,
            isWishlisted: false,
            badge: product.badge,
            brand: 'Featured Brand',
            inStock: true
          }));
          break;

        case 'hot-picks':
          // TODO: Replace with real API call for hot picks
          rawProducts = [];
          break;
            originalPrice: product.salePrice ? product.price : product.price,
            image: product.image,
            rating: product.rating,
            reviewCount: product.reviews,
            category: 'Hot Picks',
            isWishlisted: false,
            badge: product.badge,
            brand: 'Trending Brand',
            inStock: true
          }));
          break;

        default: // top-deals
          // TODO: Replace with real API call for top deals
          rawProducts = [];
          break;
            originalPrice: product.originalPrice,
            image: product.image,
            rating: product.rating,
            reviewCount: product.reviews,
            category: 'Top Deals',
            isWishlisted: false,
            badge: product.badge,
            brand: 'Deal Brand',
            inStock: true
          }));
          break;
      }



      setProducts(rawProducts);
    } catch (error) {
      console.error('Error loading products:', error);
      setProducts([]);
    } finally {
      setIsLoading(false);
    }
  };

  const getPageConfig = (): ProductListingConfig => {
    switch (dealType) {
      case 'featured-products':
        return {
          title: 'Featured Products',
          subtitle: 'Handpicked products just for you',
          showDiscountBadge: true,
          showSaleBadge: true,
          showPriceComparison: true,
          showSavingsAmount: true,
          buttonText: 'Add to Cart',
          buttonStyle: 'primary',
          productUrlPattern: 'deals', // Use deals URL pattern
          breadcrumbs: [
            { label: 'Home', href: '/' },
            { label: 'Deals', href: '/deals' },
            { label: 'Featured Products' }
          ],
        };
      case 'hot-picks':
        return {
          title: 'Hot Picks',
          subtitle: 'Trending products everyone loves',
          showDiscountBadge: true,
          showSaleBadge: true,
          showPriceComparison: true,
          showSavingsAmount: true,
          buttonText: 'Add to Cart',
          buttonStyle: 'primary',
          productUrlPattern: 'deals', // Use deals URL pattern
          breadcrumbs: [
            { label: 'Home', href: '/' },
            { label: 'Deals', href: '/deals' },
            { label: 'Hot Picks' }
          ],
        };
      default:
        return {
          title: 'Top Deals',
          subtitle: 'Best offers with up to 70% off',
          showDiscountBadge: true,
          showSaleBadge: true,
          showPriceComparison: true,
          showSavingsAmount: true,
          buttonText: 'Add to Cart',
          buttonStyle: 'primary',
          productUrlPattern: 'deals', // Use deals URL pattern
          breadcrumbs: [
            { label: 'Home', href: '/' },
            { label: 'Deals', href: '/deals' },
            { label: 'Top Deals' }
          ],
        };
    }
  };

  return (
    <ProductListingPage
      products={products}
      config={getPageConfig()}
      isLoading={isLoading}
    />
  );
}

export default function DealsPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <DealsPageContent />
    </Suspense>
  );
}
