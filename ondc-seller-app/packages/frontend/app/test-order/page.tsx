'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { CheckCircleIcon } from '@heroicons/react/24/solid';

export default function TestOrderPage() {
  const router = useRouter();
  const [countdown, setCountdown] = useState(3);
  const [orderId, setOrderId] = useState<string>('');

  useEffect(() => {
    // Create a test order
    const testOrderId = `ONDC-${Date.now()}-${Math.random().toString(36).substr(2, 9).toUpperCase()}`;
    setOrderId(testOrderId);

    const testOrder = {
      id: testOrderId,
      orderNumber: testOrderId,
      orderDate: new Date().toISOString(),
      status: 'confirmed',
      paymentStatus: 'paid',
      paymentMethod: 'Credit Card',
      estimatedDelivery: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
      customer: {
        firstName: '<PERSON>',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+91 9876543210'
      },
      shippingAddress: {
        firstName: 'John',
        lastName: 'Doe',
        address: '123 Main Street',
        city: 'Mumbai',
        state: 'Maharashtra',
        postalCode: '400001',
        country: 'India'
      },
      billingAddress: {
        firstName: 'John',
        lastName: 'Doe',
        address: '123 Main Street',
        city: 'Mumbai',
        state: 'Maharashtra',
        postalCode: '400001',
        country: 'India'
      },
      items: [
        {
          id: '1',
          name: 'Premium Wireless Headphones',
          brand: 'ONDC Seller',
          price: 1299.99,
          quantity: 1,
          image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop'
        },
        {
          id: '2',
          name: 'Smart Fitness Watch',
          brand: 'ONDC Seller',
          price: 899.99,
          quantity: 1,
          image: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop'
        }
      ],
      totals: {
        subtotal: 2199.98,
        tax: 150.00,
        shipping: 150.00,
        total: 2499.98
      }
    };

    // Store in localStorage
    localStorage.setItem(`order_${testOrderId}`, JSON.stringify(testOrder));
    console.log('Test order created:', testOrderId);

    // Start countdown
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          router.push(`/orders/${testOrderId}`);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [router]);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          {/* Success Icon */}
          <div className="mx-auto flex items-center justify-center h-20 w-20 rounded-full bg-green-100 mb-6">
            <CheckCircleIcon className="h-12 w-12 text-green-600" />
          </div>

          {/* Success Message */}
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Order Confirmed!</h1>
          <p className="text-lg text-gray-600 mb-4">
            Thank you for your purchase. Your order has been successfully placed.
          </p>
          
          {/* Order ID */}
          <div className="bg-gray-100 rounded-lg p-4 mb-4">
            <p className="text-sm text-gray-600">Order ID</p>
            <p className="text-lg font-semibold text-gray-900">{orderId}</p>
          </div>
          
          {/* Redirect Notice */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8">
            <p className="text-sm text-blue-800">
              {countdown > 0 ? (
                <>Redirecting to order details in <span className="font-bold">{countdown}</span> seconds...</>
              ) : (
                'Redirecting now...'
              )}
            </p>
          </div>

          {/* Manual Navigation */}
          <div className="space-y-4">
            <button
              onClick={() => router.push(`/orders/${orderId}`)}
              className="w-full bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200"
            >
              View Order Details
            </button>

            <div className="flex space-x-4">
              <button
                onClick={() => router.push('/categories')}
                className="flex-1 bg-white text-gray-700 px-6 py-3 rounded-lg font-medium border border-gray-300 hover:bg-gray-50 transition-colors duration-200"
              >
                Continue Shopping
              </button>

              <button
                onClick={() => router.push('/')}
                className="flex-1 bg-white text-gray-700 px-6 py-3 rounded-lg font-medium border border-gray-300 hover:bg-gray-50 transition-colors duration-200"
              >
                Home
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
