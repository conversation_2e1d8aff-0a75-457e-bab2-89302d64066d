import React from 'react';
import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { AuthProvider } from '@/context/AuthContext';
import { CartProvider } from '@/context/CartContext';
import { TenantProvider } from '@/lib/contexts/TenantContext';
import { MSWProvider } from '@/components/MSWProvider';
import LayoutWrapper from '@/components/LayoutWrapper';
import { Toaster } from 'react-hot-toast';
import { MedusaCartProvider } from '@/hooks/useMedusaCart';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'ONDC Seller Platform',
  description: 'A comprehensive platform for ONDC sellers and customers',
  metadataBase: new URL('http://localhost:3002'),
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <MSWProvider>
          <TenantProvider>
            <AuthProvider>
              <CartProvider>
                <MedusaCartProvider>
                  <LayoutWrapper>{children}</LayoutWrapper>
                </MedusaCartProvider>
                <Toaster
                  position="top-right"
                  toastOptions={{
                    duration: 3000,
                    style: {
                      background: '#363636',
                      color: '#fff',
                    },
                    success: {
                      duration: 3000,
                      iconTheme: {
                        primary: '#4ade80',
                        secondary: '#fff',
                      },
                    },
                    error: {
                      duration: 4000,
                      iconTheme: {
                        primary: '#ef4444',
                        secondary: '#fff',
                      },
                    },
                  }}
                />
                </CartProvider>
              </AuthProvider>
            </TenantProvider>
          </MSWProvider>
      </body>
    </html>
  );
}
