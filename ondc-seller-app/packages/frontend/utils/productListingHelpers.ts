import { Product, ProductListingConfig } from '@/components/common/ProductListingPage';

// Helper function to get product image URL
export function getProductImageUrl(productName: string, productId: number): string {
  const imageMap: { [key: string]: string } = {
    // Electronics & Tech
    'headphones': 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop',
    'smartwatch': 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop',
    'speaker': 'https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=400&h=400&fit=crop',
    'charger': 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=400&fit=crop',
    'keyboard': 'https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=400&h=400&fit=crop',
    'webcam': 'https://images.unsplash.com/photo-1587825140708-dfaf72ae4b04?w=400&h=400&fit=crop',
    'smart-tv': 'https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?w=400&h=400&fit=crop',
    'laptop-stand': 'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=400&fit=crop',
    'phone': 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=400&fit=crop',
    'tablet': 'https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=400&h=400&fit=crop',

    // Fashion & Lifestyle
    'handbag': 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=400&fit=crop',
    'running-shoes': 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400&h=400&fit=crop',
    'luxury-watch': 'https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=400&h=400&fit=crop',
    'scarf': 'https://images.unsplash.com/photo-1601924994987-69e26d50dc26?w=400&h=400&fit=crop',
    'denim-jacket': 'https://images.unsplash.com/photo-1551028719-00167b16eac5?w=400&h=400&fit=crop',
    'leather-boots': 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=400&fit=crop',
    'evening-dress': 'https://images.unsplash.com/photo-1566479179817-c0b8b8b5b8b8?w=400&h=400&fit=crop',
    'sunglasses': 'https://images.unsplash.com/photo-1572635196237-14b3f281503f?w=400&h=400&fit=crop',
    'shirt': 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=400&fit=crop',
    'jeans': 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=400&h=400&fit=crop',

    // Home & Furniture
    'office-chair': 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop',
    'desk-lamp': 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop',
    'mattress': 'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=400&h=400&fit=crop',
    'coffee-table': 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop',
    'blender': 'https://images.unsplash.com/photo-1570197788417-0e82375c9371?w=400&h=400&fit=crop',
    'cookware-set': 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=400&fit=crop',
    'luxury-sofa': 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop',
    'security-system': 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop',
    'dining-table': 'https://images.unsplash.com/photo-1449247709967-d4461a6a6103?w=400&h=400&fit=crop',
    'bookshelf': 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop'
  };

  const key = productName.toLowerCase().includes('headphones') ? 'headphones' :
              productName.toLowerCase().includes('watch') && !productName.toLowerCase().includes('luxury') ? 'smartwatch' :
              productName.toLowerCase().includes('luxury') && productName.toLowerCase().includes('watch') ? 'luxury-watch' :
              productName.toLowerCase().includes('speaker') ? 'speaker' :
              productName.toLowerCase().includes('charging') ? 'charger' :
              productName.toLowerCase().includes('keyboard') ? 'keyboard' :
              productName.toLowerCase().includes('webcam') ? 'webcam' :
              productName.toLowerCase().includes('chair') ? 'office-chair' :
              productName.toLowerCase().includes('lamp') ? 'desk-lamp' :
              productName.toLowerCase().includes('mattress') ? 'mattress' :
              productName.toLowerCase().includes('coffee') && productName.toLowerCase().includes('table') ? 'coffee-table' :
              productName.toLowerCase().includes('handbag') ? 'handbag' :
              productName.toLowerCase().includes('running') && productName.toLowerCase().includes('shoes') ? 'running-shoes' :
              productName.toLowerCase().includes('scarf') ? 'scarf' :
              productName.toLowerCase().includes('denim') ? 'denim-jacket' :
              productName.toLowerCase().includes('boots') ? 'leather-boots' :
              productName.toLowerCase().includes('dress') ? 'evening-dress' :
              productName.toLowerCase().includes('sunglasses') ? 'sunglasses' :
              productName.toLowerCase().includes('blender') ? 'blender' :
              productName.toLowerCase().includes('cookware') ? 'cookware-set' :
              productName.toLowerCase().includes('security') && productName.toLowerCase().includes('system') ? 'security-system' :
              productName.toLowerCase().includes('laptop') && productName.toLowerCase().includes('stand') ? 'laptop-stand' :
              productName.toLowerCase().includes('smart') && productName.toLowerCase().includes('tv') ? 'smart-tv' :
              productName.toLowerCase().includes('sofa') ? 'luxury-sofa' :
              productName.toLowerCase().includes('phone') ? 'phone' :
              productName.toLowerCase().includes('tablet') ? 'tablet' :
              productName.toLowerCase().includes('shirt') ? 'shirt' :
              productName.toLowerCase().includes('jeans') ? 'jeans' :
              productName.toLowerCase().includes('dining') && productName.toLowerCase().includes('table') ? 'dining-table' :
              productName.toLowerCase().includes('bookshelf') ? 'bookshelf' :
              'headphones';

  return imageMap[key] || imageMap['headphones'];
}

// Helper function to determine product category
export function getProductCategory(productName: string): string {
  const name = productName.toLowerCase();
  
  if (name.includes('headphones') || name.includes('watch') || name.includes('speaker') || 
      name.includes('charging') || name.includes('keyboard') || name.includes('webcam') ||
      name.includes('tv') || name.includes('laptop') || name.includes('phone') || 
      name.includes('tablet')) {
    return 'Electronics';
  }
  
  if (name.includes('handbag') || name.includes('shoes') || name.includes('scarf') || 
      name.includes('denim') || name.includes('boots') || name.includes('dress') ||
      name.includes('sunglasses') || name.includes('shirt') || name.includes('jeans')) {
    return 'Fashion';
  }
  
  return 'Home & Garden';
}

// Helper function to extract brand from product name
export function extractBrand(productName: string): string {
  if (productName.includes('Designer')) return 'Designer Collection';
  if (productName.includes('Premium')) return 'Premium Brand';
  if (productName.includes('Luxury')) return 'Luxury Line';
  if (productName.includes('Professional')) return 'Professional Series';
  if (productName.includes('Smart')) return 'Smart Tech';
  return 'Brand Collection';
}

// Convert any product data to Product interface
export function convertToProduct(data: any, id?: number): Product {
  return {
    id: id || data.id || Math.floor(Math.random() * 10000),
    name: data.name || data.title || 'Unnamed Product',
    slug: data.slug || data.name?.toLowerCase().replace(/\s+/g, '-') || 'unnamed-product',
    originalPrice: data.originalPrice || data.price || data.salePrice || 0,
    salePrice: data.salePrice || data.price || data.originalPrice || 0,
    discount: data.discount || (data.originalPrice && data.salePrice ? 
      Math.round(((data.originalPrice - data.salePrice) / data.originalPrice) * 100) : 0),
    image: data.image || getProductImageUrl(data.name || 'product', data.id || 1),
    category: data.category || getProductCategory(data.name || ''),
    rating: data.rating || (Math.random() * 2 + 3), // Random rating between 3-5
    reviewCount: data.reviewCount || data.reviews || Math.floor(Math.random() * 500) + 50,
    badge: data.badge || (data.discount && data.discount > 20 ? 'Sale' : undefined),
    brand: data.brand || extractBrand(data.name || ''),
    inStock: data.inStock !== undefined ? data.inStock : true
  };
}

// Predefined configurations for different page types
export const PAGE_CONFIGS: { [key: string]: ProductListingConfig } = {
  'top-deals': {
    title: 'Top Deals',
    subtitle: 'Best offers with up to 70% off',
    showDiscountBadge: true,
    showSaleBadge: true,
    showPriceComparison: true,
    showSavingsAmount: true,
    buttonText: 'Buy Now',
    buttonStyle: 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-md hover:shadow-lg'
  },
  'featured-products': {
    title: 'Featured Products',
    subtitle: 'Specially curated products just for you',
    showDiscountBadge: false,
    showSaleBadge: true,
    showPriceComparison: true,
    showSavingsAmount: false,
    buttonText: 'View Details',
    buttonStyle: 'bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white shadow-md hover:shadow-lg'
  },
  'hot-picks': {
    title: 'Hot Picks',
    subtitle: 'Trending products everyone is talking about',
    showDiscountBadge: true,
    showSaleBadge: true,
    showPriceComparison: true,
    showSavingsAmount: true,
    buttonText: 'Get It Now',
    buttonStyle: 'bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white shadow-md hover:shadow-lg'
  },
  'subcategory': {
    title: 'Products',
    subtitle: 'Discover amazing products in this category',
    showDiscountBadge: true,
    showSaleBadge: true,
    showPriceComparison: true,
    showSavingsAmount: false,
    buttonText: 'View Product',
    buttonStyle: 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-md hover:shadow-lg'
  },
  'search-results': {
    title: 'Search Results',
    subtitle: 'Products matching your search',
    showDiscountBadge: true,
    showSaleBadge: true,
    showPriceComparison: true,
    showSavingsAmount: false,
    buttonText: 'View Product',
    buttonStyle: 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-md hover:shadow-lg'
  }
};

// Helper to create breadcrumbs
export function createBreadcrumbs(items: Array<{ name: string; href?: string }>): Array<{ name: string; href?: string }> {
  return [
    { name: 'Home', href: '/' },
    ...items
  ];
}

// Helper to create subcategory filters
export function createSubcategoryFilters(
  subcategories: Array<{ id: string; name: string }>,
  products: Product[]
): Array<{ id: string; name: string; count: number }> {
  return subcategories.map(sub => ({
    ...sub,
    count: products.filter(p => p.category === sub.name || p.slug.includes(sub.id)).length
  }));
}
