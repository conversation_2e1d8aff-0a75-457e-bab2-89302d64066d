import { apiClient } from './api-client';

export interface OrderItem {
  id: string;
  productId: string;
  name: string;
  quantity: number;
  price: number;
  thumbnail?: string;
}

export interface Order {
  id: string;
  orderNumber: string;
  customerId: string;
  customerName: string;
  customerEmail: string;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  items: OrderItem[];
  subtotal: number;
  shipping: number;
  tax: number;
  total: number;
  shippingAddress: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface OrderListParams {
  page?: number;
  limit?: number;
  status?: string;
  customerId?: string;
  sortBy?: string;
  order?: 'asc' | 'desc';
}

export const orderApi = {
  /**
   * Get list of orders (using correct Medusa v2 endpoint)
   */
  async getOrders(params?: OrderListParams): Promise<{ data: Order[]; total: number }> {
    try {
      // Use Medusa v2 store orders endpoint
      const response = await apiClient.get('/store/orders', { params });
      return {
        data: response.data || [],
        total: response.meta?.total || 0,
      };
    } catch (error) {
      console.error('Error fetching orders:', error);
      throw error;
    }
  },

  /**
   * Get order by ID (using correct Medusa v2 endpoint)
   */
  async getOrder(id: string): Promise<Order> {
    try {
      // Use Medusa v2 store orders endpoint
      const response = await apiClient.get(`/store/orders/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching order ${id}:`, error);
      throw error;
    }
  },

  /**
   * Update order status (using correct Medusa v2 admin endpoint)
   */
  async updateOrderStatus(id: string, status: Order['status']): Promise<Order> {
    try {
      // Use Medusa v2 admin orders endpoint for updates
      const response = await apiClient.put(`/admin/orders/${id}`, { status });
      return response.data;
    } catch (error) {
      console.error(`Error updating order ${id} status:`, error);
      throw error;
    }
  },

  /**
   * Cancel an order (using correct Medusa v2 admin endpoint)
   */
  async cancelOrder(id: string): Promise<Order> {
    try {
      // Use Medusa v2 admin orders endpoint for cancellation
      const response = await apiClient.post(`/admin/orders/${id}/cancel`);
      return response.data;
    } catch (error) {
      console.error(`Error cancelling order ${id}:`, error);
      throw error;
    }
  },

  /**
   * Get order analytics (using correct Medusa v2 admin endpoint)
   */
  async getAnalytics(params?: { startDate?: string; endDate?: string }) {
    try {
      // Use Medusa v2 admin analytics endpoint
      const response = await apiClient.get('/admin/orders/analytics', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching order analytics:', error);
      throw error;
    }
  },
};
