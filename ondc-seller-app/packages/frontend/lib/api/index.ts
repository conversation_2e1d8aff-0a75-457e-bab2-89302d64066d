// Multi-Tenant ONDC Seller App API - Main Export
export * from './types';
export * from './client';
export * from './hooks';

// Re-export the main client instance and convenience functions
export { 
  apiClient,
  switchToElectronicsTenant,
  switchToFashionTenant,
  switchToDefaultTenant,
  MultiTenantApiClient
} from './client';

// Re-export all hooks
export {
  useTenant,
  useStoreInfo,
  useStoreProducts,
  useProductDetails,
  useTenantConfig,
  useMultiTenantTest,
  useAuth,
  useTenantAwareData
} from './hooks';

// Re-export all types
export type {
  TenantId,
  ApiResponse,
  TenantConfig,
  StoreInfo,
  Product,
  ProductDetails,
  Customer,
  TenantConfigResponse,
  StoreInfoResponse,
  StoreProductsResponse,
  ProductDetailsResponse,
  MultiTenantTestResponse,
  AuthTokenResponse,
  ApiClientConfig,
  ApiError,
  RequestHeaders
} from './types';

// Re-export constants
export { API_ENDPOINTS, DEFAULT_CONFIG } from './types';
