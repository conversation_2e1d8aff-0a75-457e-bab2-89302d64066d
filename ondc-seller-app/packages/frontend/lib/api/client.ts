// Multi-Tenant ONDC Seller App API Client
import {
  ApiClientConfig,
  TenantId,
  RequestHeaders,
  ApiError,
  AuthTokenResponse,
  TenantConfigResponse,
  StoreInfoResponse,
  StoreProductsResponse,
  ProductDetailsResponse,
  MultiTenantTestResponse,
  CustomersResponse,
  CustomerResponse,
  CartsResponse,
  CartResponse,
  OrdersResponse,
  OrderResponse,
  CategoriesResponse,
  CategoryResponse,
  API_ENDPOINTS,
  DEFAULT_CONFIG,
} from './types';

export class MultiTenantApiClient {
  private config: ApiClientConfig;

  constructor(config: Partial<ApiClientConfig> = {}) {
    this.config = {
      baseUrl: config.baseUrl || DEFAULT_CONFIG.BASE_URL,
      tenantId: config.tenantId || DEFAULT_CONFIG.TENANT_IDS.DEFAULT,
      adminToken: config.adminToken,
      publishableApiKey: config.publishableApiKey || DEFAULT_CONFIG.PUBLISHABLE_API_KEY,
    };
  }

  // Configuration Methods
  setTenant(tenantId: TenantId): void {
    this.config.tenantId = tenantId;
  }

  setAdminToken(token: string): void {
    this.config.adminToken = token;
  }

  setPublishableApiKey(key: string): void {
    this.config.publishableApiKey = key;
  }

  getTenant(): TenantId {
    return this.config.tenantId;
  }

  // Private Helper Methods
  private getHeaders(type: 'admin' | 'store' = 'store'): RequestHeaders {
    const headers: RequestHeaders = {
      'Content-Type': 'application/json',
      'x-tenant-id': this.config.tenantId,
    };

    if (type === 'admin' && this.config.adminToken) {
      headers['Authorization'] = `Bearer ${this.config.adminToken}`;
    } else if (type === 'store' && this.config.publishableApiKey) {
      headers['x-publishable-api-key'] = this.config.publishableApiKey;
    }

    return headers;
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {},
    type: 'admin' | 'store' = 'store'
  ): Promise<T> {
    const url = `${this.config.baseUrl}${endpoint}`;
    const headers = this.getHeaders(type);

    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          ...headers,
          ...options.headers,
        },
      });

      const data = await response.json();

      if (!response.ok) {
        const error: ApiError = {
          error: data.error || `HTTP ${response.status}`,
          message: data.message || response.statusText,
          tenant_id: this.config.tenantId,
          ...data,
        };
        throw error;
      }

      return data;
    } catch (error) {
      if (error instanceof Error) {
        const apiError: ApiError = {
          error: error.name,
          message: error.message,
          tenant_id: this.config.tenantId,
        };
        throw apiError;
      }
      throw error;
    }
  }

  // Authentication Methods
  async login(email: string, password: string): Promise<AuthTokenResponse> {
    const response = await this.makeRequest<AuthTokenResponse>(
      API_ENDPOINTS.AUTH_LOGIN,
      {
        method: 'POST',
        body: JSON.stringify({ email, password }),
      },
      'admin'
    );

    // Automatically set the admin token
    if (response.token) {
      this.setAdminToken(response.token);
    }

    return response;
  }

  // Admin API Methods
  async getTenantConfig(): Promise<TenantConfigResponse> {
    return this.makeRequest<TenantConfigResponse>(
      API_ENDPOINTS.ADMIN_TENANT,
      {},
      'admin'
    );
  }

  async testMultiTenantIsolation(): Promise<MultiTenantTestResponse> {
    return this.makeRequest<MultiTenantTestResponse>(
      API_ENDPOINTS.ADMIN_TEST_MULTI_TENANT,
      {},
      'admin'
    );
  }

  // Store API Methods
  async getStoreInfo(): Promise<StoreInfoResponse> {
    return this.makeRequest<StoreInfoResponse>(
      API_ENDPOINTS.STORE_INFO,
      {},
      'store'
    );
  }

  async getStoreProducts(): Promise<StoreProductsResponse> {
    return this.makeRequest<StoreProductsResponse>(
      API_ENDPOINTS.STORE_PRODUCTS,
      {},
      'store'
    );
  }

  async getProductDetails(productId: string): Promise<ProductDetailsResponse> {
    console.log("api calling:::::::::");
    
    return this.makeRequest<ProductDetailsResponse>(
      `${API_ENDPOINTS.STORE_PRODUCT_DETAILS}/${productId}`,
      {},
      'store'
    );
  }

  // Customer API Methods
  async getCustomers(params?: {
    limit?: number;
    offset?: number;
    q?: string;
    customer_id?: string;
  }): Promise<CustomersResponse> {
    const queryParams = new URLSearchParams();
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.offset) queryParams.append('offset', params.offset.toString());
    if (params?.q) queryParams.append('q', params.q);
    if (params?.customer_id) queryParams.append('customer_id', params.customer_id);

    const url = `${API_ENDPOINTS.STORE_CUSTOMERS}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

    return this.makeRequest<CustomersResponse>(url, {}, 'store');
  }

  async getCustomerDetails(customerId: string): Promise<CustomerResponse> {
    return this.makeRequest<CustomerResponse>(
      `${API_ENDPOINTS.STORE_CUSTOMER_DETAILS}/${customerId}`,
      {},
      'store'
    );
  }

  async createCustomer(customerData: {
    email: string;
    first_name: string;
    last_name: string;
    phone?: string;
    password?: string;
  }): Promise<CustomerResponse> {
    return this.makeRequest<CustomerResponse>(
      API_ENDPOINTS.STORE_CUSTOMERS,
      {
        method: 'POST',
        body: JSON.stringify(customerData),
      },
      'store'
    );
  }

  async updateCustomer(customerId: string, customerData: {
    email?: string;
    first_name?: string;
    last_name?: string;
    phone?: string;
  }): Promise<CustomerResponse> {
    return this.makeRequest<CustomerResponse>(
      `${API_ENDPOINTS.STORE_CUSTOMER_DETAILS}/${customerId}`,
      {
        method: 'PUT',
        body: JSON.stringify(customerData),
      },
      'store'
    );
  }

  // Cart API Methods
  async getCarts(params?: {
    limit?: number;
    offset?: number;
    customer_id?: string;
  }): Promise<CartsResponse> {
    const queryParams = new URLSearchParams();
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.offset) queryParams.append('offset', params.offset.toString());
    if (params?.customer_id) queryParams.append('customer_id', params.customer_id);

    const url = `${API_ENDPOINTS.STORE_CARTS}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

    return this.makeRequest<CartsResponse>(url, {}, 'store');
  }

  async getCartDetails(cartId: string): Promise<CartResponse> {
    return this.makeRequest<CartResponse>(
      `${API_ENDPOINTS.STORE_CART_DETAILS}/${cartId}`,
      {},
      'store'
    );
  }

  async createCart(cartData?: {
    customer_id?: string;
    region_id?: string;
    currency_code?: string;
  }): Promise<CartResponse> {
    return this.makeRequest<CartResponse>(
      API_ENDPOINTS.STORE_CARTS,
      {
        method: 'POST',
        body: JSON.stringify(cartData || {}),
      },
      'store'
    );
  }

  async addToCart(cartId: string, itemData: {
    product_id: string;
    variant_id: string;
    quantity: number;
    unit_price: number;
    title?: string;
    description?: string;
    thumbnail?: string;
  }): Promise<CartResponse> {
    return this.makeRequest<CartResponse>(
      `${API_ENDPOINTS.STORE_CART_LINE_ITEMS}/${cartId}/line-items`,
      {
        method: 'POST',
        body: JSON.stringify(itemData),
      },
      'store'
    );
  }

  // Order API Methods
  async getOrders(params?: {
    limit?: number;
    offset?: number;
    customer_id?: string;
    status?: string;
  }): Promise<OrdersResponse> {
    const queryParams = new URLSearchParams();
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.offset) queryParams.append('offset', params.offset.toString());
    if (params?.customer_id) queryParams.append('customer_id', params.customer_id);
    if (params?.status) queryParams.append('status', params.status);

    const url = `${API_ENDPOINTS.STORE_ORDERS}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

    return this.makeRequest<OrdersResponse>(url, {}, 'store');
  }

  async getOrderDetails(orderId: string): Promise<OrderResponse> {
    // Use correct Medusa v2 store orders endpoint for order details
    return this.makeRequest<OrderResponse>(
      `/store/orders/${orderId}`,
      {},
      'store'
    );
  }

  async createOrder(orderData: {
    cart_id: string;
    customer_id?: string;
    email: string;
    shipping_address: any;
    billing_address?: any;
    payment_method?: string;
  }): Promise<OrderResponse> {
    // Use correct Medusa v2 store orders endpoint for order creation
    return this.makeRequest<OrderResponse>(
      '/store/orders',
      {
        method: 'POST',
        body: JSON.stringify(orderData),
      },
      'store'
    );
  }

  // Utility Methods
  async healthCheck(): Promise<{ status: string }> {
    try {
      const response = await fetch(`${this.config.baseUrl}/health`);
      return { status: response.ok ? 'healthy' : 'unhealthy' };
    } catch {
      return { status: 'unhealthy' };
    }
  }

  // Tenant-specific convenience methods
  async switchTenant(tenantId: TenantId): Promise<StoreInfoResponse> {
    this.setTenant(tenantId);
    return this.getStoreInfo();
  }

  // Error handling utility
  isApiError(error: any): error is ApiError {
    return error && typeof error === 'object' && 'error' in error;
  }

  // Get current configuration
  getConfig(): ApiClientConfig {
    return { ...this.config };
  }
}

// Default client instance
export const apiClient = new MultiTenantApiClient();

// Convenience functions for common operations
export const switchToElectronicsTenant = () => 
  apiClient.switchTenant(DEFAULT_CONFIG.TENANT_IDS.ELECTRONICS);

export const switchToFashionTenant = () => 
  apiClient.switchTenant(DEFAULT_CONFIG.TENANT_IDS.FASHION);

export const switchToDefaultTenant = () => 
  apiClient.switchTenant(DEFAULT_CONFIG.TENANT_IDS.DEFAULT);

// Export types for convenience
export type { ApiError, TenantId } from './types';
