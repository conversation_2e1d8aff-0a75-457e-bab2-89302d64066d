/**
 * Multi-Tenant API Service
 * Handles tenant-specific data fetching from Strapi CMS
 */

import { BaseAPIService, APIListResponse, APIItemResponse, ListParams } from './base';

const STRAPI_URL = process.env.NEXT_PUBLIC_STRAPI_API_URL || 'http://localhost:1337';
const API_TOKEN = process.env.STRAPI_API_TOKEN;

// Types for multi-tenant data
export interface Seller {
  id: number;
  documentId: string;
  name: string;
  store_name: string | null;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  country: string;
  postal_code: string;
  business_type: string;
  description: string;
  website: string;
  logo: string;
  banner_image: string;
  status: string;
  seller_status: 'Active' | 'Inactive' | 'Pending';
  publishedAt: string | null;
  createdAt: string;
  updatedAt: string;
}

/**
 * Get the store name for branding purposes, with fallback to "E-com Store"
 */
export function getStoreName(seller: Seller | null): string {
  if (!seller) return 'E-com Store';
  return seller.store_name || 'E-com Store';
}

export interface TenantBanner {
  id: number;
  documentId: string;
  title: string;
  subtitle?: string;
  description?: string;
  buttonText?: string;
  buttonLink?: string;
  link?: string;
  active: boolean;
  position: number;
  image?: {
    id: number;
    documentId: string;
    name: string;
    alternativeText?: string;
    caption?: string;
    width: number;
    height: number;
    formats?: any;
    hash: string;
    ext: string;
    mime: string;
    size: number;
    url: string;
    previewUrl?: string;
    provider: string;
    provider_metadata?: any;
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
  };
  tenant: Seller;
  createdAt: string;
  updatedAt: string;
}

export interface TenantPage {
  id: number;
  documentId: string;
  title: string;
  slug: string;
  content: string;
  excerpt?: string;
  metaTitle?: string;
  metaDescription?: string;
  status: string;
  template?: string;
  featured: boolean;
  viewCount: number;
  author?: string;
  createdAt: string;
  updatedAt: string;
}

export interface TenantProduct {
  id: number;
  documentId: string;
  name: string;
  slug?: string;
  description?: any;
  short_description?: string;
  price: number;
  sale_price?: number;
  sku?: string;
  inventory_quantity?: number;
  product_status: string;
  featured: boolean;
  tags?: string;
  weight?: number;
  subcategory?: string;
  seller: Seller;
  createdAt: string;
  updatedAt: string;
}

export interface TenantCategory {
  id: number;
  documentId: string;
  name: string;
  slug: string;
  description?: string;
  tenant: Seller;
  createdAt: string;
  updatedAt: string;
}

export interface TenantSubcategory {
  id: number;
  documentId: string;
  name: string;
  slug: string;
  description?: string;
  category: TenantCategory;
  tenant: Seller;
  createdAt: string;
  updatedAt: string;
}

/**
 * Multi-Tenant API Service
 */
export class MultiTenantAPIService {
  private baseURL: string;
  private headers: Record<string, string>;

  constructor() {
    this.baseURL = `${STRAPI_URL}/api`;
    this.headers = {
      'Content-Type': 'application/json',
    };

    // Remove authentication for public endpoints
    // if (API_TOKEN) {
    //   this.headers['Authorization'] = `Bearer ${API_TOKEN}`;
    // }
  }

  /**
   * Generic request method
   */
  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    
    const config: RequestInit = {
      headers: {
        ...this.headers,
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Multi-tenant API request failed:', error);
      throw error;
    }
  }

  // ============================================================================
  // Sellers/Tenants
  // ============================================================================

  /**
   * Get all sellers with filtering for active and published status and real-time data synchronization
   */
  async getSellers(): Promise<APIListResponse<Seller> & { meta?: any }> {
    // Build query parameters for filtering
    const queryParams = new URLSearchParams();

    // Add cache-busting parameter for real-time data synchronization
    queryParams.append('_t', Date.now().toString());

    // Filter by seller_status = "Active" (exact match)
    queryParams.append('filters[seller_status][$eq]', 'Active');

    // Filter by published status (publishedAt is not null)
    queryParams.append('filters[publishedAt][$notNull]', 'true');

    // Populate related fields if needed
    queryParams.append('populate', '*');

    const endpoint = `/sellers?${queryParams.toString()}`;
    console.log('🔄 Fetching sellers from Strapi with real-time data:', endpoint);
    const response = await this.request<{ data: Seller[]; meta: any }>(endpoint);

    console.log('✅ Raw Strapi sellers response:', response);

    return {
      data: response.data,
      count: response.data.length,
      total: response.meta?.pagination?.total || response.data.length,
      page: response.meta?.pagination?.page || 1,
      limit: response.meta?.pagination?.pageSize || 25,
      hasMore: (response.meta?.pagination?.page || 1) < (response.meta?.pagination?.pageCount || 1),
      meta: response.meta,
    };
  }

  /**
   * Get seller by ID
   */
  async getSeller(id: string): Promise<APIItemResponse<Seller>> {
    const response = await this.request<{ data: Seller }>(`/sellers/${id}`);
    return {
      data: response.data,
    };
  }

  // ============================================================================
  // Tenant-specific Banners
  // ============================================================================


  
  /**
   * Get banners for a specific tenant
   */
  async getBannersByTenant(tenantId: string): Promise<APIListResponse<TenantBanner>> {
    const response = await this.request<{ data: TenantBanner[]; meta: any }>(
      `/banners?populate=*&filters[tenant][id][$eq]=${tenantId}&sort=position:asc`
    );
    return {
      data: response.data,
      meta: response.meta,
    };
  }

  /**
   * Get all active banners
   */
  async getActiveBanners(): Promise<APIListResponse<TenantBanner>> {
    const response = await this.request<{ data: TenantBanner[]; meta: any }>(
      '/banners?populate=*&sort=position:asc'
    );
    return {
      data: response.data,
      meta: response.meta,
    };
  }

  // ============================================================================
  // Tenant-specific Pages
  // ============================================================================

  /**
   * Get pages for a specific tenant
   */
  async getPagesByTenant(tenantId: string): Promise<APIListResponse<TenantPage>> {
    const response = await this.request<{ data: TenantPage[]; meta: any }>(
      `/pages?filters[status][$eq]=published`
    );
    return {
      data: response.data,
      meta: response.meta,
    };
  }

  /**
   * Get page by slug
   */
  async getPageBySlug(slug: string): Promise<APIItemResponse<TenantPage>> {
    const response = await this.request<{ data: TenantPage[] }>(
      `/pages?filters[slug][$eq]=${slug}&filters[status][$eq]=published`
    );
    
    if (response.data && response.data.length > 0) {
      return {
        data: response.data[0],
      };
    }
    
    throw new Error(`Page with slug "${slug}" not found`);
  }

  // ============================================================================
  // Tenant-specific Products
  // ============================================================================

  /**
   * Get products for a specific tenant
   */
  async getProductsByTenant(tenantId: string, params: ListParams = {}): Promise<APIListResponse<TenantProduct>> {
    const queryParams = new URLSearchParams();
    queryParams.append('populate', '*');
    queryParams.append('filters[seller][id][$eq]', tenantId);
    queryParams.append('filters[product_status][$eq]', 'Published');
    
    if (params.limit) queryParams.append('pagination[pageSize]', params.limit.toString());
    if (params.page) queryParams.append('pagination[page]', params.page.toString());
    
    const response = await this.request<{ data: TenantProduct[]; meta: any }>(
      `/products?${queryParams.toString()}`
    );
    return {
      data: response.data,
      meta: response.meta,
    };
  }

  /**
   * Get featured products for a specific tenant
   */
  async getFeaturedProductsByTenant(tenantId: string): Promise<APIListResponse<TenantProduct>> {
    try {
      const response = await this.request<{ data: TenantProduct[]; meta: any }>(
        `/products?populate=*&filters[seller][id][$eq]=${tenantId}&filters[featured][$eq]=true&filters[product_status][$eq]=Published&sort=createdAt:desc`
      );
      return {
        data: response.data || [],
        meta: response.meta || { pagination: { page: 1, pageSize: 25, pageCount: 1, total: 0 } },
      };
    } catch (error) {
      console.error(`Failed to fetch featured products for tenant ${tenantId}:`, error);
      return {
        data: [],
        meta: { pagination: { page: 1, pageSize: 25, pageCount: 1, total: 0 } },
      };
    }
  }

  /**
   * Get products by subcategory and tenant
   */
  async getProductsBySubcategoryAndTenant(
    subcategoryId: string,
    tenantId: string,
    params: ListParams = {}
  ): Promise<APIListResponse<TenantProduct>> {
    try {
      const queryParams = new URLSearchParams();
      queryParams.append('populate', '*');
      queryParams.append('filters[subcategory][id][$eq]', subcategoryId);
      queryParams.append('filters[seller][id][$eq]', tenantId);
      queryParams.append('filters[product_status][$eq]', 'Published');

      if (params.limit) queryParams.append('pagination[pageSize]', params.limit.toString());
      if (params.page) queryParams.append('pagination[page]', params.page.toString());
      if (params.sortBy) queryParams.append('sort', `${params.sortBy}:${params.sortOrder || 'asc'}`);

      const response = await this.request<{ data: TenantProduct[]; meta: any }>(
        `/products?${queryParams.toString()}`
      );
      return {
        data: response.data || [],
        meta: response.meta || { pagination: { page: 1, pageSize: 25, pageCount: 1, total: 0 } },
      };
    } catch (error) {
      console.error(`Failed to fetch products for subcategory ${subcategoryId} and tenant ${tenantId}:`, error);
      return {
        data: [],
        meta: { pagination: { page: 1, pageSize: 25, pageCount: 1, total: 0 } },
      };
    }
  }

  /**
   * Get products by category and tenant
   */
  async getProductsByCategoryAndTenant(
    categoryId: string,
    tenantId: string,
    params: ListParams = {}
  ): Promise<APIListResponse<TenantProduct>> {
    try {
      const queryParams = new URLSearchParams();
      queryParams.append('populate', '*');
      queryParams.append('filters[category][id][$eq]', categoryId);
      queryParams.append('filters[seller][id][$eq]', tenantId);
      queryParams.append('filters[product_status][$eq]', 'Published');

      if (params.limit) queryParams.append('pagination[pageSize]', params.limit.toString());
      if (params.page) queryParams.append('pagination[page]', params.page.toString());
      if (params.sortBy) queryParams.append('sort', `${params.sortBy}:${params.sortOrder || 'asc'}`);

      const response = await this.request<{ data: TenantProduct[]; meta: any }>(
        `/products?${queryParams.toString()}`
      );
      return {
        data: response.data || [],
        meta: response.meta || { pagination: { page: 1, pageSize: 25, pageCount: 1, total: 0 } },
      };
    } catch (error) {
      console.error(`Failed to fetch products for category ${categoryId} and tenant ${tenantId}:`, error);
      return {
        data: [],
        meta: { pagination: { page: 1, pageSize: 25, pageCount: 1, total: 0 } },
      };
    }
  }

  /**
   * Get product count for a subcategory and tenant
   */
  async getProductCountBySubcategoryAndTenant(subcategoryId: string, tenantId: string): Promise<number> {
    try {
      const response = await this.request<{ data: TenantProduct[]; meta: any }>(
        `/products?filters[subcategory][id][$eq]=${subcategoryId}&filters[seller][id][$eq]=${tenantId}&filters[product_status][$eq]=Published&pagination[pageSize]=1`
      );
      return response.meta?.pagination?.total || 0;
    } catch (error) {
      console.error(`Failed to fetch product count for subcategory ${subcategoryId} and tenant ${tenantId}:`, error);
      return 0;
    }
  }

  /**
   * Get product count for a category and tenant
   */
  async getProductCountByCategoryAndTenant(categoryId: string, tenantId: string): Promise<number> {
    try {
      const response = await this.request<{ data: TenantProduct[]; meta: any }>(
        `/products?filters[category][id][$eq]=${categoryId}&filters[seller][id][$eq]=${tenantId}&filters[product_status][$eq]=Published&pagination[pageSize]=1`
      );
      return response.meta?.pagination?.total || 0;
    } catch (error) {
      console.error(`Failed to fetch product count for category ${categoryId} and tenant ${tenantId}:`, error);
      return 0;
    }
  }

  // ============================================================================
  // Tenant-specific Categories
  // ============================================================================

  /**
   * Get categories for a specific tenant
   */
  async getCategoriesByTenant(tenantId: string): Promise<APIListResponse<TenantCategory>> {
    try {
      const response = await this.request<{ data: TenantCategory[]; meta: any }>(
        `/categories?populate=*&filters[tenant][id][$eq]=${tenantId}&filters[active][$eq]=true&sort=sort_order:asc`
      );
      return {
        data: response.data || [],
        meta: response.meta || { pagination: { page: 1, pageSize: 25, pageCount: 1, total: 0 } },
      };
    } catch (error) {
      console.error(`Failed to fetch categories for tenant ${tenantId}:`, error);
      return {
        data: [],
        meta: { pagination: { page: 1, pageSize: 25, pageCount: 1, total: 0 } },
      };
    }
  }

  /**
   * Get all categories (including unassigned ones) for fallback
   */
  async getAllCategories(): Promise<APIListResponse<TenantCategory>> {
    try {
      const response = await this.request<{ data: TenantCategory[]; meta: any }>(
        `/categories?populate=*&filters[active][$eq]=true&sort=sort_order:asc`
      );
      return {
        data: response.data || [],
        meta: response.meta || { pagination: { page: 1, pageSize: 25, pageCount: 1, total: 0 } },
      };
    } catch (error) {
      console.error('Failed to fetch all categories:', error);
      return {
        data: [],
        meta: { pagination: { page: 1, pageSize: 25, pageCount: 1, total: 0 } },
      };
    }
  }

  /**
   * Get subcategories for a specific tenant
   */
  async getSubcategoriesByTenant(tenantId: string): Promise<APIListResponse<TenantSubcategory>> {
    try {
      const response = await this.request<{ data: TenantSubcategory[]; meta: any }>(
        `/subcategories?populate=*&filters[tenant][id][$eq]=${tenantId}&sort=name:asc`
      );
      return {
        data: response.data || [],
        meta: response.meta || { pagination: { page: 1, pageSize: 25, pageCount: 1, total: 0 } },
      };
    } catch (error) {
      console.error(`Failed to fetch subcategories for tenant ${tenantId}:`, error);
      return {
        data: [],
        meta: { pagination: { page: 1, pageSize: 25, pageCount: 1, total: 0 } },
      };
    }
  }

  /**
   * Get subcategories by category and tenant
   */
  async getSubcategoriesByCategoryAndTenant(categoryId: string, tenantId: string): Promise<APIListResponse<TenantSubcategory>> {
    try {
      const response = await this.request<{ data: TenantSubcategory[]; meta: any }>(
        `/subcategories?populate=*&filters[category][id][$eq]=${categoryId}&filters[tenant][id][$eq]=${tenantId}&sort=name:asc`
      );
      return {
        data: response.data || [],
        meta: response.meta || { pagination: { page: 1, pageSize: 25, pageCount: 1, total: 0 } },
      };
    } catch (error) {
      console.error(`Failed to fetch subcategories for category ${categoryId} and tenant ${tenantId}:`, error);
      return {
        data: [],
        meta: { pagination: { page: 1, pageSize: 25, pageCount: 1, total: 0 } },
      };
    }
  }
}

// Export service instance
export const multiTenantAPI = new MultiTenantAPIService();
