/**
 * Medusa Backend API Client
 * Integration with confirmed working backend endpoints from 06-Store-API-WORKING.postman_collection.json
 * All endpoints tested and verified working with real database operations
 */

// API Configuration from confirmed working setup
const API_CONFIG = {
  BASE_URL: 'http://localhost:9000',
  API_KEY: 'pk_3d67561dece2d466dc798c18c1f80523f84f3b2f01316e0bf915e51f3a59b98b',
  REGION_ID: 'reg_01JZ7RPY072WGWKTJ6Q2YE46V7',
  HEADERS: {
    'Content-Type': 'application/json',
    'x-publishable-api-key': 'pk_3d67561dece2d466dc798c18c1f80523f84f3b2f01316e0bf915e51f3a59b98b'
  }
} as const;

// Type definitions based on confirmed API responses
export interface MedusaProduct {
  id: string;
  title: string;
  description: string;
  handle: string;
  status: string;
  thumbnail: string | null;
  variants: MedusaProductVariant[];
  categories?: MedusaCategory[];
  images?: MedusaImage[];
  options?: MedusaProductOption[];
  tags?: MedusaTag[];
  type?: MedusaProductType;
  collection?: MedusaCollection;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface MedusaProductVariant {
  id: string;
  title: string;
  sku: string;
  barcode?: string;
  ean?: string;
  upc?: string;
  inventory_quantity: number;
  allow_backorder: boolean;
  manage_inventory: boolean;
  weight?: number;
  length?: number;
  height?: number;
  width?: number;
  origin_country?: string;
  mid_code?: string;
  material?: string;
  metadata?: Record<string, any>;
  options?: MedusaProductOptionValue[];
  prices?: MedusaPrice[];
}

export interface MedusaCategory {
  id: string;
  name: string;
  description: string;
  handle: string;
  rank: number;
  parent_category_id: string | null;
  parent_category?: MedusaCategory;
  category_children?: MedusaCategory[];
  products?: MedusaProduct[];
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface MedusaCart {
  id: string;
  currency_code: string;
  email?: string;
  region_id: string;
  region?: MedusaRegion;
  items: MedusaCartItem[];
  total: number;
  subtotal: number;
  tax_total?: number;
  shipping_total?: number;
  discount_total?: number;
  shipping_address?: MedusaAddress;
  billing_address?: MedusaAddress;
  customer_id?: string;
  payment_sessions?: any[];
  created_at: string;
  updated_at: string;
  completed_at?: string;
  metadata?: Record<string, any>;
}

export interface MedusaCartItem {
  id: string;
  cart_id: string;
  product_id: string;
  variant_id: string;
  product?: MedusaProduct;
  variant?: MedusaProductVariant;
  title: string;
  description?: string;
  thumbnail?: string;
  quantity: number;
  unit_price: number;
  total: number;
  original_total?: number;
  original_unit_price?: number;
  metadata?: Record<string, any>;
}

export interface MedusaRegion {
  id: string;
  name: string;
  currency_code: string;
  tax_rate?: number;
  countries?: MedusaCountry[];
  payment_providers?: any[];
  fulfillment_providers?: any[];
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface MedusaCustomer {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  phone?: string;
  has_account: boolean;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface MedusaOrder {
  id: string;
  cart_id?: string;
  email: string;
  total: number;
  subtotal: number;
  tax_total?: number;
  shipping_total?: number;
  currency_code: string;
  payment_status: string;
  fulfillment_status: string;
  payment_method?: string;
  items: MedusaOrderItem[];
  shipping_address?: MedusaAddress;
  billing_address?: MedusaAddress;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface MedusaOrderItem {
  id: string;
  title: string;
  quantity: number;
  unit_price: number;
  total: number;
  variant_id?: string;
  product_id?: string;
  metadata?: Record<string, any>;
}

export interface MedusaAddress {
  id?: string;
  first_name: string;
  last_name: string;
  address_1: string;
  address_2?: string;
  city: string;
  postal_code: string;
  province?: string;
  country_code: string;
  phone?: string;
  company?: string;
  metadata?: Record<string, any>;
}

// Additional interfaces
export interface MedusaImage {
  id: string;
  url: string;
  metadata?: Record<string, any>;
}

export interface MedusaProductOption {
  id: string;
  title: string;
  values: MedusaProductOptionValue[];
}

export interface MedusaProductOptionValue {
  id: string;
  value: string;
  option_id: string;
}

export interface MedusaPrice {
  id: string;
  currency_code: string;
  amount: number;
  min_quantity?: number;
  max_quantity?: number;
}

export interface MedusaTag {
  id: string;
  value: string;
}

export interface MedusaProductType {
  id: string;
  value: string;
}

export interface MedusaCollection {
  id: string;
  title: string;
  handle: string;
}

export interface MedusaCountry {
  id: string;
  iso_2: string;
  iso_3: string;
  name: string;
  display_name: string;
}

// API Response interfaces
export interface ApiResponse<T> {
  success?: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface ProductsResponse {
  products: MedusaProduct[];
  count: number;
  offset: number;
  limit: number;
}

export interface CategoriesResponse {
  product_categories: MedusaCategory[];
  count: number;
  offset: number;
  limit: number;
}

export interface RegionsResponse {
  regions: MedusaRegion[];
}

export interface CartResponse {
  cart: MedusaCart;
}

export interface CustomerResponse {
  customer: MedusaCustomer;
  success: boolean;
  message?: string;
}

export interface OrdersResponse {
  success: boolean;
  orders: MedusaOrder[];
  count: number;
  total_count: number;
  limit: number;
  offset: number;
}

export interface OrderResponse {
  success: boolean;
  order: MedusaOrder;
}

export interface CODOrderResponse {
  success: boolean;
  order: MedusaOrder;
  message: string;
}

// Error handling
export class MedusaAPIError extends Error {
  constructor(
    message: string,
    public status?: number,
    public response?: any
  ) {
    super(message);
    this.name = 'MedusaAPIError';
  }
}

// API Client Class
export class MedusaBackendAPI {
  private baseURL: string;
  private headers: Record<string, string>;

  constructor() {
    this.baseURL = API_CONFIG.BASE_URL;
    this.headers = API_CONFIG.HEADERS;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    
    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          ...this.headers,
          ...options.headers,
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new MedusaAPIError(
          errorData.message || `HTTP ${response.status}: ${response.statusText}`,
          response.status,
          errorData
        );
      }

      return await response.json();
    } catch (error) {
      if (error instanceof MedusaAPIError) {
        throw error;
      }
      throw new MedusaAPIError(
        `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  // ✅ 1. Health Check
  async healthCheck(): Promise<string> {
    return await this.request<string>('/health');
  }

  // ✅ 2. Get Regions
  async getRegions(): Promise<RegionsResponse> {
    return await this.request<RegionsResponse>('/store/regions');
  }

  // ✅ 3. Get Products
  async getProducts(params?: {
    limit?: number;
    offset?: number;
    q?: string;
    category_id?: string[];
    collection_id?: string[];
    tags?: string[];
    type_id?: string[];
  }): Promise<ProductsResponse> {
    const searchParams = new URLSearchParams();
    
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.offset) searchParams.append('offset', params.offset.toString());
    if (params?.q) searchParams.append('q', params.q);
    if (params?.category_id) {
      params.category_id.forEach(id => searchParams.append('category_id[]', id));
    }
    if (params?.collection_id) {
      params.collection_id.forEach(id => searchParams.append('collection_id[]', id));
    }
    if (params?.tags) {
      params.tags.forEach(tag => searchParams.append('tags[]', tag));
    }
    if (params?.type_id) {
      params.type_id.forEach(id => searchParams.append('type_id[]', id));
    }

    const queryString = searchParams.toString();
    const endpoint = `/store/products${queryString ? `?${queryString}` : ''}`;
    
    return await this.request<ProductsResponse>(endpoint);
  }

  // ✅ 4. Get Product Categories
  async getCategories(params?: {
    limit?: number;
    offset?: number;
    parent_category_id?: string;
    include_descendants_tree?: boolean;
  }): Promise<CategoriesResponse> {
    const searchParams = new URLSearchParams();
    
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.offset) searchParams.append('offset', params.offset.toString());
    if (params?.parent_category_id) searchParams.append('parent_category_id', params.parent_category_id);
    if (params?.include_descendants_tree) searchParams.append('include_descendants_tree', 'true');

    const queryString = searchParams.toString();
    const endpoint = `/store/product-categories${queryString ? `?${queryString}` : ''}`;
    
    return await this.request<CategoriesResponse>(endpoint);
  }

  // ✅ 5. Get Single Product
  async getProduct(productId: string): Promise<{ product: MedusaProduct }> {
    return await this.request<{ product: MedusaProduct }>(`/store/products/${productId}`);
  }

  // ✅ 6. Create Cart (with customer support)
  async createCart(data?: {
    region_id?: string;
    customer_id?: string;
    email?: string;
  }): Promise<CartResponse> {
    const cartData: any = {
      region_id: data?.region_id || API_CONFIG.REGION_ID
    };

    // Add customer information if provided
    if (data?.customer_id) {
      cartData.customer_id = data.customer_id;
    }
    if (data?.email) {
      cartData.email = data.email;
    }

    return await this.request<CartResponse>('/store/carts', {
      method: 'POST',
      body: JSON.stringify(cartData),
    });
  }

  // ✅ 7. Get Cart Details
  async getCart(cartId: string): Promise<CartResponse> {
    return await this.request<CartResponse>(`/store/carts/${cartId}`);
  }

  // ✅ 7.5. Update Cart Customer Information
  async updateCartCustomer(cartId: string, data: {
    customer_id?: string;
    email?: string;
  }): Promise<CartResponse> {
    return await this.request<CartResponse>(`/store/carts/${cartId}`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // ✅ 8. Add Product to Cart
  async addToCart(cartId: string, data: {
    variant_id: string;
    quantity: number;
    metadata?: Record<string, any>;
  }): Promise<CartResponse> {
    return await this.request<CartResponse>(`/store/carts/${cartId}/line-items`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Update Cart Item Quantity
  async updateCartItem(cartId: string, itemId: string, data: {
    quantity: number;
  }): Promise<CartResponse> {
    return await this.request<CartResponse>(`/store/carts/${cartId}/line-items/${itemId}`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Remove Cart Item
  async removeCartItem(cartId: string, itemId: string): Promise<CartResponse> {
    return await this.request<CartResponse>(`/store/carts/${cartId}/line-items/${itemId}`, {
      method: 'DELETE',
    });
  }

  // ✅ 9. Customer Registration (Simplified)
  async registerCustomer(data: {
    email: string;
    first_name: string;
    last_name: string;
    phone?: string;
  }): Promise<CustomerResponse> {
    return await this.request<CustomerResponse>('/store/customers/register', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // ✅ 10. Complete Cart with Cash on Delivery
  async completeCartWithCOD(cartId: string): Promise<CODOrderResponse> {
    return await this.request<CODOrderResponse>(`/store/carts/${cartId}/complete-cod`, {
      method: 'POST',
      body: JSON.stringify({}),
    });
  }

  // ✅ 11. Get Orders (Simplified)
  async getOrders(params?: {
    email?: string;
    limit?: number;
    offset?: number;
  }): Promise<OrdersResponse> {
    const searchParams = new URLSearchParams();

    if (params?.email) searchParams.append('email', params.email);
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.offset) searchParams.append('offset', params.offset.toString());

    const queryString = searchParams.toString();
    const endpoint = `/store/orders/simple${queryString ? `?${queryString}` : ''}`;

    return await this.request<OrdersResponse>(endpoint);
  }

  // ✅ 12. Get Single Order
  async getOrder(orderId: string): Promise<OrderResponse> {
    return await this.request<OrderResponse>('/store/orders/simple', {
      method: 'POST',
      body: JSON.stringify({ order_id: orderId }),
    });
  }

  // Utility Methods

  // Get default region ID
  getDefaultRegionId(): string {
    return API_CONFIG.REGION_ID;
  }

  // Check if server is healthy
  async isServerHealthy(): Promise<boolean> {
    try {
      const response = await this.healthCheck();
      return response === 'OK';
    } catch {
      return false;
    }
  }

  // Search products by query
  async searchProducts(query: string, limit = 20): Promise<ProductsResponse> {
    return await this.getProducts({ q: query, limit });
  }

  // Get products by category
  async getProductsByCategory(categoryId: string, limit = 20): Promise<ProductsResponse> {
    return await this.getProducts({ category_id: [categoryId], limit });
  }

  // Get featured products (using all products since Medusa v2 doesn't support tag filtering)
  async getFeaturedProducts(limit = 10): Promise<ProductsResponse> {
    // Since Medusa v2 store API doesn't support tag filtering, return all products
    // In a real implementation, you would filter by metadata or use a different approach
    console.info('Medusa v2 store API does not support tag filtering, showing all products as featured');
    return await this.getProducts({ limit });
  }

  // Get top deals (using all products since Medusa v2 doesn't support tag filtering)
  async getTopDeals(limit = 10): Promise<ProductsResponse> {
    // Since Medusa v2 store API doesn't support tag filtering, return all products
    // In a real implementation, you would filter by metadata or use a different approach
    console.info('Medusa v2 store API does not support tag filtering, showing all products as top deals');
    return await this.getProducts({ limit });
  }

  // Get hot picks (using all products since Medusa v2 doesn't support tag filtering)
  async getHotPicks(limit = 10): Promise<ProductsResponse> {
    // Since Medusa v2 store API doesn't support tag filtering, return all products
    // In a real implementation, you would filter by metadata or use a different approach
    console.info('Medusa v2 store API does not support tag filtering, showing all products as hot picks');
    return await this.getProducts({ limit });
  }
}

// Create singleton instance
export const medusaAPI = new MedusaBackendAPI();

// Export default instance
export default medusaAPI;
