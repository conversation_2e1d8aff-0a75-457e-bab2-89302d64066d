'use client';

import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { apiClient } from '../api/client';
import {
  TenantId,
  StoreInfoResponse,
  StoreProductsResponse,
  DEFAULT_CONFIG,
} from '../api/types';

// Tenant State
interface TenantState {
  currentTenant: TenantId;
  storeInfo: StoreInfoResponse | null;
  products: StoreProductsResponse | null;
  loading: boolean;
  error: string | null;
  isInitialized: boolean;
}

// Tenant Actions
type TenantAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_TENANT'; payload: TenantId }
  | { type: 'SET_STORE_INFO'; payload: StoreInfoResponse }
  | { type: 'SET_PRODUCTS'; payload: StoreProductsResponse }
  | { type: 'SET_INITIALIZED'; payload: boolean }
  | { type: 'RESET_DATA' };

// Initial State
const initialState: TenantState = {
  currentTenant: DEFAULT_CONFIG.TENANT_IDS.DEFAULT,
  storeInfo: null,
  products: null,
  loading: false,
  error: null,
  isInitialized: false,
};

// Reducer
function tenantReducer(state: TenantState, action: TenantAction): TenantState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };
    case 'SET_TENANT':
      return { ...state, currentTenant: action.payload };
    case 'SET_STORE_INFO':
      return { ...state, storeInfo: action.payload, error: null };
    case 'SET_PRODUCTS':
      return { ...state, products: action.payload, error: null };
    case 'SET_INITIALIZED':
      return { ...state, isInitialized: action.payload };
    case 'RESET_DATA':
      return { ...state, storeInfo: null, products: null, error: null };
    default:
      return state;
  }
}

// Context Type
interface TenantContextType {
  state: TenantState;
  switchTenant: (tenantId: TenantId) => Promise<void>;
  refreshData: () => Promise<void>;
  switchToElectronics: () => Promise<void>;
  switchToFashion: () => Promise<void>;
  switchToDefault: () => Promise<void>;
  getTenantDisplayName: (tenantId?: TenantId) => string;
  getTenantBranding: () => { primaryColor: string; logo: string } | null;
}

// Create Context
const TenantContext = createContext<TenantContextType | undefined>(undefined);

// Provider Props
interface TenantProviderProps {
  children: ReactNode;
  initialTenant?: TenantId;
}

// Provider Component
export function TenantProvider({ children, initialTenant }: TenantProviderProps) {
  const [state, dispatch] = useReducer(tenantReducer, {
    ...initialState,
    currentTenant: initialTenant || DEFAULT_CONFIG.TENANT_IDS.DEFAULT,
  });

  // Fetch store data
  const fetchStoreData = async (tenantId: TenantId) => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_ERROR', payload: null });

    try {
      // Set tenant in API client
      apiClient.setTenant(tenantId);

      // Fetch store info and products in parallel
      const [storeInfoResponse, productsResponse] = await Promise.all([
        apiClient.getStoreInfo(),
        apiClient.getStoreProducts(),
      ]);

      dispatch({ type: 'SET_STORE_INFO', payload: storeInfoResponse });
      dispatch({ type: 'SET_PRODUCTS', payload: productsResponse });
    } catch (error) {
      const errorMessage = apiClient.isApiError(error) 
        ? error.message || error.error 
        : 'Failed to fetch store data';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  // Switch tenant
  const switchTenant = async (tenantId: TenantId) => {
    if (tenantId === state.currentTenant) return;

    dispatch({ type: 'SET_TENANT', payload: tenantId });
    dispatch({ type: 'RESET_DATA' });
    await fetchStoreData(tenantId);
  };

  // Refresh current tenant data
  const refreshData = async () => {
    await fetchStoreData(state.currentTenant);
  };

  // Convenience methods
  const switchToElectronics = () => switchTenant(DEFAULT_CONFIG.TENANT_IDS.ELECTRONICS);
  const switchToFashion = () => switchTenant(DEFAULT_CONFIG.TENANT_IDS.FASHION);
  const switchToDefault = () => switchTenant(DEFAULT_CONFIG.TENANT_IDS.DEFAULT);

  // Get tenant display name
  const getTenantDisplayName = (tenantId?: TenantId): string => {
    const id = tenantId || state.currentTenant;
    switch (id) {
      case DEFAULT_CONFIG.TENANT_IDS.ELECTRONICS:
        return 'Electronics Store';
      case DEFAULT_CONFIG.TENANT_IDS.FASHION:
        return 'Fashion Store';
      default:
        return 'Default Store';
    }
  };

  // Get tenant branding
  const getTenantBranding = () => {
    return state.storeInfo?.store.branding || null;
  };

  // Initialize on mount
  useEffect(() => {
    if (!state.isInitialized) {
      fetchStoreData(state.currentTenant).finally(() => {
        dispatch({ type: 'SET_INITIALIZED', payload: true });
      });
    }
  }, [state.isInitialized, state.currentTenant]);

  const contextValue: TenantContextType = {
    state,
    switchTenant,
    refreshData,
    switchToElectronics,
    switchToFashion,
    switchToDefault,
    getTenantDisplayName,
    getTenantBranding,
  };

  return (
    <TenantContext.Provider value={contextValue}>
      {children}
    </TenantContext.Provider>
  );
}

// Hook to use tenant context
export function useTenantContext(): TenantContextType {
  const context = useContext(TenantContext);
  if (context === undefined) {
    throw new Error('useTenantContext must be used within a TenantProvider');
  }
  return context;
}

// Tenant selector component
export function TenantSelector() {
  const { state, switchToElectronics, switchToFashion, switchToDefault, getTenantDisplayName } = useTenantContext();

  const tenantOptions = [
    { id: DEFAULT_CONFIG.TENANT_IDS.ELECTRONICS, name: 'Electronics Store', action: switchToElectronics },
    { id: DEFAULT_CONFIG.TENANT_IDS.FASHION, name: 'Fashion Store', action: switchToFashion },
    { id: DEFAULT_CONFIG.TENANT_IDS.DEFAULT, name: 'Default Store', action: switchToDefault },
  ];

  return (
    <div className="tenant-selector">
      <label htmlFor="tenant-select" className="block text-sm font-medium text-gray-700 mb-2">
        Select Store:
      </label>
      <select
        id="tenant-select"
        value={state.currentTenant}
        onChange={(e) => {
          const selectedTenant = e.target.value as TenantId;
          const option = tenantOptions.find(opt => opt.id === selectedTenant);
          option?.action();
        }}
        disabled={state.loading}
        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
      >
        {tenantOptions.map((option) => (
          <option key={option.id} value={option.id}>
            {option.name}
          </option>
        ))}
      </select>
      
      {state.loading && (
        <div className="mt-2 text-sm text-gray-500">
          Loading store data...
        </div>
      )}
      
      {state.error && (
        <div className="mt-2 text-sm text-red-600">
          Error: {state.error}
        </div>
      )}
      
      {state.storeInfo && (
        <div className="mt-2 text-sm text-green-600">
          Connected to: {getTenantDisplayName()}
        </div>
      )}
    </div>
  );
}

// Export types
export type { TenantState, TenantContextType };
