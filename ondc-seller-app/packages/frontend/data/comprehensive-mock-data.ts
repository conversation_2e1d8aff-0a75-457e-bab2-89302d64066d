/**
 * Comprehensive Multi-Tenant E-commerce Mock Data
 * 
 * This file contains complete mock data for a multi-tenant e-commerce platform
 * with proper tenant isolation and hierarchical relationships.
 * 
 * Structure: Tenant → Category → Subcategory → Product
 * Each entity is properly tagged with tenant ID for multi-tenant isolation.
 */

// Base interfaces matching Strapi schema
export interface MockSeller {
  id: number;
  documentId: string;
  name: string;
  store_name: string | null;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  country: string;
  postal_code: string;
  business_type: string;
  description: string;
  website: string;
  logo: string | null;
  banner_image: string | null;
  status: string;
  seller_status: 'Active' | 'Inactive' | 'Pending';
  publishedAt: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface MockCategory {
  id: number;
  documentId: string;
  name: string;
  description: string;
  short_description: string;
  slug: string;
  image: string | null;
  icon: string | null;
  active: boolean;
  sort_order: number;
  meta_title: string;
  meta_description: string;
  tenant: {
    id: number;
    documentId: string;
    name: string;
  };
  publishedAt: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface MockSubcategory {
  id: number;
  documentId: string;
  name: string;
  slug: string;
  description: string;
  category: {
    id: number;
    documentId: string;
    name: string;
  };
  tenant: {
    id: number;
    documentId: string;
    name: string;
  };
  publishedAt: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface MockProduct {
  id: number;
  documentId: string;
  name: string;
  description: string;
  short_description: string;
  price: number;
  sale_price: number | null;
  sku: string;
  inventory_quantity: number;
  slug: string;
  product_status: 'Draft' | 'Published' | 'Out of Stock';
  featured: boolean;
  is_featured: boolean;
  is_top_deal: boolean;
  is_hot_pick: boolean;
  tags: string[];
  weight: number;
  images: Array<{
    id: number;
    url: string;
    alternativeText: string;
  }>;
  categories: Array<{
    id: number;
    documentId: string;
    name: string;
  }>;
  subcategory: {
    id: number;
    documentId: string;
    name: string;
  };
  seller: {
    id: number;
    documentId: string;
    name: string;
  };
  publishedAt: string | null;
  createdAt: string;
  updatedAt: string;
}

// Mock Sellers/Tenants Data
export const mockSellers: MockSeller[] = [
  {
    id: 1,
    documentId: "seller_techhub_001",
    name: "TechHub Electronics",
    store_name: "Tech Store",
    email: "<EMAIL>",
    phone: "+91-9876543210",
    address: "123 Tech Park, Electronic City",
    city: "Bangalore",
    state: "Karnataka",
    country: "India",
    postal_code: "560100",
    business_type: "Electronics Retailer",
    description: "Leading electronics retailer specializing in cutting-edge technology products",
    website: "https://techhub.com",
    logo: "https://placehold.co/200x80/3B82F6/FFFFFF?text=TechHub",
    banner_image: "https://placehold.co/1200x400/3B82F6/FFFFFF?text=TechHub+Electronics",
    status: "active",
    seller_status: "Active",
    publishedAt: "2024-01-01T00:00:00.000Z",
    createdAt: "2024-01-01T00:00:00.000Z",
    updatedAt: "2024-01-15T10:30:00.000Z"
  },
  {
    id: 2,
    documentId: "seller_stylehub_002",
    name: "StyleHub Fashion-3",
    store_name: null, // Will use fallback "E-com Store"
    email: "<EMAIL>",
    phone: "+91-9876543211",
    address: "456 Fashion Street, Commercial Complex",
    city: "Mumbai",
    state: "Maharashtra",
    country: "India",
    postal_code: "400001",
    business_type: "Fashion Retailer",
    description: "Trendy fashion destination for modern lifestyle and contemporary clothing",
    website: "https://stylehub.com",
    logo: "https://placehold.co/200x80/EC4899/FFFFFF?text=StyleHub",
    banner_image: "https://placehold.co/1200x400/EC4899/FFFFFF?text=StyleHub+Fashion",
    status: "active",
    seller_status: "Active",
    publishedAt: "2024-01-05T00:00:00.000Z",
    createdAt: "2024-01-05T00:00:00.000Z",
    updatedAt: "2024-01-20T14:15:00.000Z"
  },
  {
    id: 3,
    documentId: "seller_homehub_003",
    name: "HomeHub Essentials",
    store_name: "Home Store",
    email: "<EMAIL>",
    phone: "+91-9876543212",
    address: "789 Home Center, Furniture District",
    city: "Delhi",
    state: "Delhi",
    country: "India",
    postal_code: "110001",
    business_type: "Home & Furniture Retailer",
    description: "Complete home solutions with furniture, decor, and essential household items",
    website: "https://homehub.com",
    logo: "https://placehold.co/200x80/059669/FFFFFF?text=HomeHub",
    banner_image: "https://placehold.co/1200x400/059669/FFFFFF?text=HomeHub+Essentials",
    status: "active",
    seller_status: "Active",
    publishedAt: "2024-01-10T00:00:00.000Z",
    createdAt: "2024-01-10T00:00:00.000Z",
    updatedAt: "2024-01-25T09:45:00.000Z"
  }
];

// Helper function to get seller by ID
export function getSellerById(id: number): MockSeller | undefined {
  return mockSellers.find(seller => seller.id === id);
}

// Helper function to get seller by document ID
export function getSellerByDocumentId(documentId: string): MockSeller | undefined {
  return mockSellers.find(seller => seller.documentId === documentId);
}
