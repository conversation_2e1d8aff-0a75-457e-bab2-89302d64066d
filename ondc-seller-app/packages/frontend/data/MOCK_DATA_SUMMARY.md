# Comprehensive Multi-Tenant E-commerce Mock Data Summary

## Overview
This document summarizes the comprehensive mock data created for the multi-tenant e-commerce platform using MSW (Mock Service Worker). The data structure follows the Strapi CMS schema and provides complete tenant isolation with hierarchical relationships.

## Data Structure
```
Tenant → Category → Subcategory → Product
```

## 🏢 **Tenants/Sellers (3 Total)**

### 1. TechHub Electronics (`tenant_1`)
- **Store Name**: "Tech Store"
- **Business Focus**: Electronics and technology products
- **Theme**: Blue (#3B82F6)
- **Status**: Active (Pro Plan)

### 2. StyleHub Fashion-3 (`tenant_2`)
- **Store Name**: `null` (uses "E-com Store" fallback)
- **Business Focus**: Fashion and lifestyle products
- **Theme**: Pink (#EC4899)
- **Status**: Active (Enterprise Plan)

### 3. HomeHub Essentials (`tenant_3`)
- **Store Name**: "Home Store"
- **Business Focus**: Home and furniture products
- **Theme**: Green (#059669)
- **Status**: Trial (Basic Plan)

## 📂 **Categories (15 Total - 5 per Tenant)**

### TechHub Electronics Categories:
1. **Electronics** - Latest electronic devices and gadgets
2. **Computers & Laptops** - High-performance computing solutions
3. **Mobile Accessories** - Essential mobile device accessories
4. **Gaming** - Gaming equipment and accessories
5. **Audio & Video** - Premium audio and video equipment

### StyleHub Fashion-3 Categories:
1. **Clothing** - Trendy clothing for all occasions
2. **Footwear** - Stylish shoes and sandals
3. **Accessories** - Fashion accessories and jewelry
4. **Bags & Luggage** - Stylish bags and travel luggage
5. **Beauty & Personal Care** - Beauty products and personal care

### HomeHub Essentials Categories:
1. **Furniture** - Quality furniture for every room
2. **Kitchen & Dining** - Kitchen appliances and dining essentials
3. **Home Decor** - Decorative items and home accessories
4. **Garden & Outdoor** - Garden tools and outdoor furniture
5. **Storage & Organization** - Storage solutions and organization systems

## 📁 **Subcategories (Sample - 3-5 per Category)**

### TechHub Electronics Subcategories:
- **Electronics**: Smartphones, Tablets, Smartwatches, Cameras
- **Computers & Laptops**: Laptops, Desktop Computers, Computer Components
- **Mobile Accessories**: Cases, Chargers, Screen Protectors, Stands
- **Gaming**: Consoles, Controllers, Gaming Headsets, Gaming Keyboards
- **Audio & Video**: Headphones, Speakers, Microphones, Cameras

### StyleHub Fashion-3 Subcategories:
- **Clothing**: Men's Clothing, Women's Clothing, Kids' Clothing, Activewear
- **Footwear**: Casual Shoes, Formal Shoes, Sports Shoes, Sandals
- **Accessories**: Jewelry, Watches, Belts, Sunglasses
- **Bags & Luggage**: Handbags, Backpacks, Travel Bags, Wallets
- **Beauty & Personal Care**: Skincare, Makeup, Hair Care, Fragrances

### HomeHub Essentials Subcategories:
- **Furniture**: Living Room, Bedroom, Office, Dining Room
- **Kitchen & Dining**: Cookware, Small Appliances, Dinnerware, Storage
- **Home Decor**: Wall Art, Lighting, Rugs, Curtains
- **Garden & Outdoor**: Garden Tools, Outdoor Furniture, Plants, Grills
- **Storage & Organization**: Closet Storage, Bathroom Storage, Garage Storage, Shelving

## 🛍️ **Products (5-8 per Subcategory)**

### Product Features:
- **Promotional Flags**: 
  - `is_featured`: For Featured Products carousel
  - `is_top_deal`: For Top Deals carousel
  - `is_hot_pick`: For Hot Picks carousel
- **Realistic Pricing**: Based on tenant's market segment
- **Complete Product Data**: Title, description, price, stock, images
- **Multi-tenant Isolation**: Each product tagged with tenant ID
- **Category Relationships**: Linked to both subcategory and category

### Sample Products by Tenant:

#### TechHub Electronics:
- Premium Wireless Headphones (₹8,999)
- Gaming Mechanical Keyboard (₹3,499)
- Professional Camera Lens (₹45,999)
- Smart Fitness Watch (₹12,999)

#### StyleHub Fashion-3:
- Organic Cotton T-Shirt (₹899)
- Designer Denim Jeans (₹2,499)
- Luxury Silk Scarf (₹1,899)
- Leather Crossbody Bag (₹3,999)

#### HomeHub Essentials:
- Ergonomic Office Chair (₹4,499)
- Premium Coffee Maker (₹1,599)
- Smart LED Floor Lamp (₹2,299)
- Bamboo Cutting Board Set (₹799)

## 🔧 **MSW Integration**

### Available Helper Functions:
```typescript
// Tenant functions
getTenantById(id: string)
getTenantByHandle(handle: string)

// Category functions
getCategoriesByTenant(tenantId: string)
getCategoryById(id: string)
getCategoryBySlug(slug: string, tenantId?: string)

// Subcategory functions
getSubcategoriesByTenant(tenantId: string)
getSubcategoriesByCategory(categoryId: string)
getSubcategoryById(id: string)
getSubcategoryBySlug(slug: string, tenantId?: string)

// Product functions (existing)
getProductsByCategory(category: string)
getFilteredProducts(filters: object)
searchProducts(query: string)
```

## 🎯 **Key Features**

### ✅ **Multi-Tenant Isolation**
- Every entity tagged with `tenant_id`
- No cross-tenant data sharing
- Proper hierarchical relationships maintained

### ✅ **Realistic Business Data**
- Business-appropriate naming for each tenant domain
- Realistic pricing based on market segments
- Professional product descriptions and metadata

### ✅ **Promotional Support**
- Products distributed across promotional flags
- Not all products have promotional flags (realistic distribution)
- Support for Featured Products, Top Deals, and Hot Picks carousels

### ✅ **Complete Schema Compliance**
- Matches Strapi CMS schema structure
- Proper field types and relationships
- Ready for API integration

## 🚀 **Usage**

### Enable MSW:
```bash
# Set environment variable
NEXT_PUBLIC_MSW_ENABLED=true
```

### Access Mock Data:
```typescript
import { 
  mockTenants, 
  mockCategories, 
  mockSubcategories,
  getCategoriesByTenant,
  getSubcategoriesByCategory 
} from '@/data/msw-data';

// Get categories for TechHub Electronics
const techCategories = getCategoriesByTenant('tenant_1');

// Get subcategories for Electronics category
const electronicsSubcats = getSubcategoriesByCategory('cat_tech_1');
```

## 📊 **Data Statistics**
- **3 Tenants** with unique business focuses
- **15 Categories** (5 per tenant)
- **45+ Subcategories** (3-5 per category)
- **200+ Products** (5-8 per subcategory)
- **Complete tenant isolation** maintained
- **Realistic e-commerce data** with proper relationships

This comprehensive mock data provides a solid foundation for developing and testing the multi-tenant e-commerce platform with proper data isolation and realistic business scenarios.
