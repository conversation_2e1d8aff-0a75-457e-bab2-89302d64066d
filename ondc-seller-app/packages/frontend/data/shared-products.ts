import { getProductsByCategoryAndSubcategory, convertMSWProduct, getProductsByCategory } from '@/data/msw-data';

// Product interface for shared use
export interface SharedProduct {
  id: string;
  name: string;
  slug: string;
  description: string;
  images: Array<{ src: string; alt?: string }>;
  price: number;
  originalPrice?: number;
  discount?: number;
  category: string;
  subcategory?: string;
  brand: string;
  rating: number;
  reviewCount: number;
  variants: Array<{
    id: string;
    name: string;
    price: number;
    stock: number;
    sku: string;
    attributes?: Record<string, string>;
  }>;
  features: string[];
  specifications: Record<string, string>;
  reviews: Array<{
    id: string;
    userName: string;
    userAvatar?: string;
    rating: number;
    title: string;
    comment: string;
    date: string;
    verified: boolean;
    helpful: number;
  }>;
  isWishlisted: boolean;
  inStock: boolean;
  badge?: string;
  tenantId?: string;
}

// Simple seeded random number generator for deterministic results
const seededRandom = (seed: number) => {
  let x = Math.sin(seed) * 10000;
  return x - Math.floor(x);
};

// Generate mock reviews for a product
export const generateMockReviews = (productName: string, reviewCount: number) => {
  const reviewTemplates = [
    {
      titles: ["Excellent quality!", "Love this product!", "Highly recommended!", "Great value for money!"],
      comments: [
        "This product exceeded my expectations. The quality is outstanding and it arrived quickly.",
        "I'm very satisfied with this purchase. Great quality and exactly as described.",
        "Excellent product! Would definitely buy again. Fast shipping and great customer service.",
        "Amazing quality for the price. I've been using it for weeks and it's still perfect."
      ]
    },
    {
      titles: ["Good product", "Satisfied with purchase", "Worth buying", "Nice quality"],
      comments: [
        "Good quality product. It does what it's supposed to do. Delivery was on time.",
        "Decent product for the price. No complaints so far. Would recommend to others.",
        "Happy with my purchase. Good quality and fast delivery. Exactly what I needed.",
        "Nice product overall. Good value for money and arrived in perfect condition."
      ]
    },
    {
      titles: ["Could be better", "Average product", "Okay quality", "Mixed feelings"],
      comments: [
        "The product is okay but could be improved. It works fine but nothing exceptional.",
        "Average quality. It does the job but I expected a bit more for the price.",
        "It's an okay product. Not the best I've used but not the worst either.",
        "Mixed feelings about this product. Some good points but also some areas for improvement."
      ]
    }
  ];

  const userNames = [
    "Rajesh Kumar", "Priya Sharma", "Amit Patel", "Sneha Gupta", "Vikram Singh",
    "Anita Reddy", "Rohit Mehta", "Kavya Nair", "Arjun Rao", "Deepika Joshi",
    "Sanjay Verma", "Meera Iyer", "Karan Malhotra", "Ritu Agarwal", "Varun Chopra"
  ];

  const reviews = [];
  const numReviews = Math.min(reviewCount, 15); // Limit to 15 reviews max

  // Create a seed based on product name for deterministic results
  const seed = productName.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);

  for (let i = 0; i < numReviews; i++) {
    // Use seeded random for deterministic results
    const ratingRand = seededRandom(seed + i * 1000);
    const rating = ratingRand < 0.7 ? (seededRandom(seed + i * 1001) < 0.8 ? 5 : 4) : (seededRandom(seed + i * 1002) < 0.6 ? 3 : (seededRandom(seed + i * 1003) < 0.7 ? 2 : 1));
    const templateIndex = rating >= 4 ? 0 : rating >= 3 ? 1 : 2;
    const template = reviewTemplates[templateIndex];

    // Generate deterministic date (within last 90 days)
    const daysAgo = Math.floor(seededRandom(seed + i * 2000) * 90);
    const reviewDate = new Date();
    reviewDate.setDate(reviewDate.getDate() - daysAgo);

    const review = {
      id: `review-${i + 1}`,
      userName: userNames[i % userNames.length],
      rating,
      title: template.titles[Math.floor(seededRandom(seed + i * 3000) * template.titles.length)],
      comment: template.comments[Math.floor(seededRandom(seed + i * 4000) * template.comments.length)],
      date: reviewDate.toLocaleDateString('en-IN'),
      verified: seededRandom(seed + i * 5000) > 0.2, // 80% verified purchases
      helpful: Math.floor(seededRandom(seed + i * 6000) * 20),
    };

    reviews.push(review);
  }

  return reviews.sort((a, b) => b.rating - a.rating); // Sort by rating (highest first)
};

// Convert MSW product to SharedProduct format
export const convertToSharedProduct = (mswProduct: any, categoryName: string, subcategoryName?: string): SharedProduct => {
  const converted = convertMSWProduct(mswProduct);
  
  return {
    id: converted.id,
    name: converted.name,
    slug: converted.slug,
    description: converted.description || `Premium quality ${converted.name.toLowerCase()} with excellent features and modern design. Perfect for everyday use with outstanding performance and reliability.`,
    images: converted.images?.map(img => ({ src: img.src, alt: img.alt })) || [
      { src: `https://picsum.photos/400/400?random=${converted.slug}-${converted.id}`, alt: converted.name }
    ],
    price: converted.salePrice || converted.price,
    originalPrice: converted.originalPrice,
    discount: converted.discount,
    category: categoryName,
    subcategory: subcategoryName || converted.subcategory,
    brand: converted.brand || (converted.name.includes('Designer') ? 'Designer Collection' :
           converted.name.includes('Premium') ? 'Premium Brand' :
           converted.name.includes('Luxury') ? 'Luxury Line' : 'Brand Collection'),
    rating: converted.rating,
    reviewCount: converted.reviewCount,
    variants: converted.variants || [
      {
        id: 'default',
        name: 'Default',
        price: converted.price,
        stock: 50,
        sku: `${converted.slug.toUpperCase()}-001`,
      },
    ],
    features: [
      'High Quality Materials',
      'Modern Design',
      'Excellent Performance',
      'Durable Construction',
      'Easy to Use',
    ],
    specifications: {
      'Brand': converted.brand || 'Premium Brand',
      'Category': categoryName,
      'Subcategory': subcategoryName || 'General',
      'Rating': `${converted.rating}/5`,
      'Reviews': converted.reviewCount?.toString() || '0',
    },
    reviews: generateMockReviews(converted.name, converted.reviewCount || 0),
    isWishlisted: false,
    inStock: converted.inStock !== false,
    badge: converted.badge,
    tenantId: converted.tenantId,
  };
};

// Get products by category and subcategory
export const getSharedProductsByCategory = (categoryName: string, subcategoryName?: string): SharedProduct[] => {
  const mswProducts = subcategoryName
    ? getProductsByCategoryAndSubcategory(categoryName, subcategoryName)
    : getProductsByCategory(categoryName);

  return mswProducts.map(product => convertToSharedProduct(product, categoryName, subcategoryName));
};

// Find a specific product by slug
export const findProductBySlug = (categoryName: string, subcategoryName: string, productSlug: string): SharedProduct | null => {
  const products = getSharedProductsByCategory(categoryName, subcategoryName);
  return products.find(product => product.slug === productSlug) || null;
};

// Find a product across all categories (for search)
export const findProductBySlugGlobal = (productSlug: string): SharedProduct | null => {
  const categories = ['furniture', 'electronics', 'fashion'];
  
  for (const category of categories) {
    const products = getSharedProductsByCategory(category);
    const product = products.find(p => p.slug === productSlug);
    if (product) {
      return product;
    }
  }
  
  return null;
};
