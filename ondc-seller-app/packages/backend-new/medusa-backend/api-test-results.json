{"timestamp": "2025-07-03T08:31:38.433Z", "summary": {"total": 27, "passed": 19, "failed": 8, "critical_failures": 1, "success_rate": "70.37%"}, "status": "CRITICAL_ISSUES", "tests": [{"name": "Admin Au<PERSON>ntication", "status": "PASS", "critical": false}, {"name": "Invalid Credentials Rejection", "status": "FAIL", "error": "Request failed with status code 500", "critical": false}, {"name": "Get Admin User Info", "status": "PASS", "critical": false}, {"name": "Tenant Configuration: default", "status": "PASS", "critical": false}, {"name": "Tenant Configuration: tenant-electronics-001", "status": "PASS", "critical": false}, {"name": "Tenant Configuration: tenant-fashion-002", "status": "PASS", "critical": false}, {"name": "Tenant Configuration: tenant-books-003", "status": "PASS", "critical": false}, {"name": "List Products (Admin)", "status": "PASS", "critical": false}, {"name": "List Products (Store)", "status": "PASS", "critical": false}, {"name": "Get Single Product (Store)", "status": "PASS", "critical": false}, {"name": "Multi-Tenant Product Isolation", "status": "CRITICAL FAIL", "error": "CRITICAL SECURITY BREACH: Product accessible across tenants", "critical": true}, {"name": "Create <PERSON><PERSON>", "status": "PASS", "critical": false}, {"name": "Retrieve <PERSON>", "status": "FAIL", "error": "Request failed with status code 404", "critical": false}, {"name": "Add Product to Cart", "status": "FAIL", "error": "Request failed with status code 400", "critical": false}, {"name": "Update Cart Item Quantity", "status": "FAIL", "error": "No cart ID or line item ID available", "critical": false}, {"name": "Add Shipping Address to Cart", "status": "FAIL", "error": "Request failed with status code 404", "critical": false}, {"name": "Get Shipping Options", "status": "PASS", "critical": false}, {"name": "Create Payment Session", "status": "PASS", "critical": false}, {"name": "Complete Cart (Create Order)", "status": "FAIL", "error": "Request failed with status code 500", "critical": false}, {"name": "List Customers (Admin)", "status": "PASS", "critical": false}, {"name": "Create Customer", "status": "FAIL", "error": "Request failed with status code 401", "critical": false}, {"name": "List Orders (Admin)", "status": "PASS", "critical": false}, {"name": "Get Order by ID (if available)", "status": "PASS", "critical": false}, {"name": "List Customer Orders (Store)", "status": "PASS", "critical": false}, {"name": "List Categories (Store)", "status": "PASS", "critical": false}, {"name": "Unauthorized Access Rejection", "status": "PASS", "critical": false}, {"name": "Non-existent Resource Handling", "status": "PASS", "critical": false}]}