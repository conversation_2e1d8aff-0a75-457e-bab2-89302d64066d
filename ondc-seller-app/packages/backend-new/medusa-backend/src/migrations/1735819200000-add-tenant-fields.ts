import { Migration } from "@mikro-orm/migrations"

/**
 * Migration to add tenant_id fields to all relevant Medusa tables
 * This enables multi-tenant data isolation
 */
export class Migration20250102120000 extends Migration {
  async up(): Promise<void> {
    // Add tenant_id column to core entities
    
    // Products table
    this.addSql(`
      ALTER TABLE "product" 
      ADD COLUMN IF NOT EXISTS "tenant_id" VARCHAR(255) NOT NULL DEFAULT 'default';
    `)
    
    // Product variants table
    this.addSql(`
      ALTER TABLE "product_variant" 
      ADD COLUMN IF NOT EXISTS "tenant_id" VARCHAR(255) NOT NULL DEFAULT 'default';
    `)
    
    // Customers table
    this.addSql(`
      ALTER TABLE "customer" 
      ADD COLUMN IF NOT EXISTS "tenant_id" VARCHAR(255) NOT NULL DEFAULT 'default';
    `)
    
    // Orders table
    this.addSql(`
      ALTER TABLE "order" 
      ADD COLUMN IF NOT EXISTS "tenant_id" VARCHAR(255) NOT NULL DEFAULT 'default';
    `)
    
    // Cart table
    this.addSql(`
      ALTER TABLE "cart" 
      ADD COLUMN IF NOT EXISTS "tenant_id" VARCHAR(255) NOT NULL DEFAULT 'default';
    `)
    
    // Product categories table
    this.addSql(`
      ALTER TABLE "product_category" 
      ADD COLUMN IF NOT EXISTS "tenant_id" VARCHAR(255) NOT NULL DEFAULT 'default';
    `)
    
    // Product collections table
    this.addSql(`
      ALTER TABLE "product_collection" 
      ADD COLUMN IF NOT EXISTS "tenant_id" VARCHAR(255) NOT NULL DEFAULT 'default';
    `)
    
    // Inventory items table
    this.addSql(`
      ALTER TABLE "inventory_item" 
      ADD COLUMN IF NOT EXISTS "tenant_id" VARCHAR(255) NOT NULL DEFAULT 'default';
    `)
    
    // Stock locations table
    this.addSql(`
      ALTER TABLE "stock_location" 
      ADD COLUMN IF NOT EXISTS "tenant_id" VARCHAR(255) NOT NULL DEFAULT 'default';
    `)
    
    // Promotions table
    this.addSql(`
      ALTER TABLE "promotion" 
      ADD COLUMN IF NOT EXISTS "tenant_id" VARCHAR(255) NOT NULL DEFAULT 'default';
    `)
    
    // Price lists table
    this.addSql(`
      ALTER TABLE "price_list" 
      ADD COLUMN IF NOT EXISTS "tenant_id" VARCHAR(255) NOT NULL DEFAULT 'default';
    `)
    
    // Customer groups table
    this.addSql(`
      ALTER TABLE "customer_group" 
      ADD COLUMN IF NOT EXISTS "tenant_id" VARCHAR(255) NOT NULL DEFAULT 'default';
    `)
    
    // Add indexes for better query performance
    this.addSql(`CREATE INDEX IF NOT EXISTS "idx_product_tenant_id" ON "product" ("tenant_id");`)
    this.addSql(`CREATE INDEX IF NOT EXISTS "idx_product_variant_tenant_id" ON "product_variant" ("tenant_id");`)
    this.addSql(`CREATE INDEX IF NOT EXISTS "idx_customer_tenant_id" ON "customer" ("tenant_id");`)
    this.addSql(`CREATE INDEX IF NOT EXISTS "idx_order_tenant_id" ON "order" ("tenant_id");`)
    this.addSql(`CREATE INDEX IF NOT EXISTS "idx_cart_tenant_id" ON "cart" ("tenant_id");`)
    this.addSql(`CREATE INDEX IF NOT EXISTS "idx_product_category_tenant_id" ON "product_category" ("tenant_id");`)
    this.addSql(`CREATE INDEX IF NOT EXISTS "idx_product_collection_tenant_id" ON "product_collection" ("tenant_id");`)
    this.addSql(`CREATE INDEX IF NOT EXISTS "idx_inventory_item_tenant_id" ON "inventory_item" ("tenant_id");`)
    this.addSql(`CREATE INDEX IF NOT EXISTS "idx_stock_location_tenant_id" ON "stock_location" ("tenant_id");`)
    this.addSql(`CREATE INDEX IF NOT EXISTS "idx_promotion_tenant_id" ON "promotion" ("tenant_id");`)
    this.addSql(`CREATE INDEX IF NOT EXISTS "idx_price_list_tenant_id" ON "price_list" ("tenant_id");`)
    this.addSql(`CREATE INDEX IF NOT EXISTS "idx_customer_group_tenant_id" ON "customer_group" ("tenant_id");`)
    
    // Add unique constraints for tenant isolation
    this.addSql(`
      ALTER TABLE "product" 
      ADD CONSTRAINT "unique_product_handle_tenant" 
      UNIQUE ("handle", "tenant_id");
    `)
    
    this.addSql(`
      ALTER TABLE "customer" 
      ADD CONSTRAINT "unique_customer_email_tenant" 
      UNIQUE ("email", "tenant_id");
    `)
    
    this.addSql(`
      ALTER TABLE "product_category" 
      ADD CONSTRAINT "unique_category_handle_tenant" 
      UNIQUE ("handle", "tenant_id");
    `)
    
    this.addSql(`
      ALTER TABLE "product_collection" 
      ADD CONSTRAINT "unique_collection_handle_tenant" 
      UNIQUE ("handle", "tenant_id");
    `)
    
    this.addSql(`
      ALTER TABLE "customer_group" 
      ADD CONSTRAINT "unique_customer_group_name_tenant" 
      UNIQUE ("name", "tenant_id");
    `)
    
    // Create tenant configuration table
    this.addSql(`
      CREATE TABLE IF NOT EXISTS "tenant_config" (
        "id" VARCHAR(255) PRIMARY KEY,
        "name" VARCHAR(255) NOT NULL,
        "domain" VARCHAR(255),
        "status" VARCHAR(50) NOT NULL DEFAULT 'active',
        "settings" JSONB NOT NULL DEFAULT '{}',
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
      );
    `)
    
    this.addSql(`CREATE INDEX IF NOT EXISTS "idx_tenant_config_status" ON "tenant_config" ("status");`)
    this.addSql(`CREATE INDEX IF NOT EXISTS "idx_tenant_config_domain" ON "tenant_config" ("domain");`)
    
    // Insert default tenant configurations
    this.addSql(`
      INSERT INTO "tenant_config" ("id", "name", "domain", "status", "settings") 
      VALUES 
        ('default', 'Default Tenant', 'localhost', 'active', '{"currency": "INR", "timezone": "Asia/Kolkata", "features": ["all"]}'),
        ('tenant-electronics-001', 'Electronics Store', 'electronics.ondc-seller.com', 'active', '{"currency": "INR", "timezone": "Asia/Kolkata", "features": ["products", "orders", "customers", "analytics", "inventory"]}'),
        ('tenant-fashion-002', 'Fashion Store', 'fashion.ondc-seller.com', 'active', '{"currency": "INR", "timezone": "Asia/Kolkata", "features": ["products", "orders", "customers", "promotions"]}'),
        ('tenant-books-003', 'Books Store', 'books.ondc-seller.com', 'active', '{"currency": "INR", "timezone": "Asia/Kolkata", "features": ["products", "orders", "customers"]}')
      ON CONFLICT ("id") DO NOTHING;
    `)
  }

  async down(): Promise<void> {
    // Remove tenant_id columns and related constraints
    
    // Drop unique constraints first
    this.addSql(`ALTER TABLE "product" DROP CONSTRAINT IF EXISTS "unique_product_handle_tenant";`)
    this.addSql(`ALTER TABLE "customer" DROP CONSTRAINT IF EXISTS "unique_customer_email_tenant";`)
    this.addSql(`ALTER TABLE "product_category" DROP CONSTRAINT IF EXISTS "unique_category_handle_tenant";`)
    this.addSql(`ALTER TABLE "product_collection" DROP CONSTRAINT IF EXISTS "unique_collection_handle_tenant";`)
    this.addSql(`ALTER TABLE "customer_group" DROP CONSTRAINT IF EXISTS "unique_customer_group_name_tenant";`)
    
    // Drop indexes
    this.addSql(`DROP INDEX IF EXISTS "idx_product_tenant_id";`)
    this.addSql(`DROP INDEX IF EXISTS "idx_product_variant_tenant_id";`)
    this.addSql(`DROP INDEX IF EXISTS "idx_customer_tenant_id";`)
    this.addSql(`DROP INDEX IF EXISTS "idx_order_tenant_id";`)
    this.addSql(`DROP INDEX IF EXISTS "idx_cart_tenant_id";`)
    this.addSql(`DROP INDEX IF EXISTS "idx_product_category_tenant_id";`)
    this.addSql(`DROP INDEX IF EXISTS "idx_product_collection_tenant_id";`)
    this.addSql(`DROP INDEX IF EXISTS "idx_inventory_item_tenant_id";`)
    this.addSql(`DROP INDEX IF EXISTS "idx_stock_location_tenant_id";`)
    this.addSql(`DROP INDEX IF EXISTS "idx_promotion_tenant_id";`)
    this.addSql(`DROP INDEX IF EXISTS "idx_price_list_tenant_id";`)
    this.addSql(`DROP INDEX IF EXISTS "idx_customer_group_tenant_id";`)
    
    // Drop tenant_id columns
    this.addSql(`ALTER TABLE "product" DROP COLUMN IF EXISTS "tenant_id";`)
    this.addSql(`ALTER TABLE "product_variant" DROP COLUMN IF EXISTS "tenant_id";`)
    this.addSql(`ALTER TABLE "customer" DROP COLUMN IF EXISTS "tenant_id";`)
    this.addSql(`ALTER TABLE "order" DROP COLUMN IF EXISTS "tenant_id";`)
    this.addSql(`ALTER TABLE "cart" DROP COLUMN IF EXISTS "tenant_id";`)
    this.addSql(`ALTER TABLE "product_category" DROP COLUMN IF EXISTS "tenant_id";`)
    this.addSql(`ALTER TABLE "product_collection" DROP COLUMN IF EXISTS "tenant_id";`)
    this.addSql(`ALTER TABLE "inventory_item" DROP COLUMN IF EXISTS "tenant_id";`)
    this.addSql(`ALTER TABLE "stock_location" DROP COLUMN IF EXISTS "tenant_id";`)
    this.addSql(`ALTER TABLE "promotion" DROP COLUMN IF EXISTS "tenant_id";`)
    this.addSql(`ALTER TABLE "price_list" DROP COLUMN IF EXISTS "tenant_id";`)
    this.addSql(`ALTER TABLE "customer_group" DROP COLUMN IF EXISTS "tenant_id";`)
    
    // Drop tenant configuration table
    this.addSql(`DROP TABLE IF EXISTS "tenant_config";`)
  }
}
