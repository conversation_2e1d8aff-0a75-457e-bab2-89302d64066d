import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { ContainerRegistrationKeys } from "@medusajs/framework/utils"

/**
 * Admin Customer Management API
 * Full CRUD operations for customer management
 */

/**
 * GET /admin/customers
 * List all customers with filtering and pagination
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { 
      limit = 20, 
      offset = 0, 
      q, 
      email, 
      has_account,
      created_at_gte,
      created_at_lte,
      updated_at_gte,
      updated_at_lte
    } = req.query;
    
    const customerModuleService = req.scope.resolve("customer");
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);

    logger.info(`[ADMIN CUSTOMERS] GET request for tenant: ${tenantId}`);

    // Build filters
    const filters: any = {};

    if (q && typeof q === 'string') {
      filters.$or = [
        { email: { $ilike: `%${q}%` } },
        { first_name: { $ilike: `%${q}%` } },
        { last_name: { $ilike: `%${q}%` } }
      ];
    }

    if (email && typeof email === 'string') {
      filters.email = { $ilike: `%${email}%` };
    }

    if (has_account !== undefined) {
      filters.has_account = has_account === 'true';
    }

    if (created_at_gte) {
      filters.created_at = { ...filters.created_at, $gte: new Date(created_at_gte as string) };
    }

    if (created_at_lte) {
      filters.created_at = { ...filters.created_at, $lte: new Date(created_at_lte as string) };
    }

    if (updated_at_gte) {
      filters.updated_at = { ...filters.updated_at, $gte: new Date(updated_at_gte as string) };
    }

    if (updated_at_lte) {
      filters.updated_at = { ...filters.updated_at, $lte: new Date(updated_at_lte as string) };
    }

    // Get customers with pagination
    const customers = await customerModuleService.listCustomers(filters, {
      take: Number(limit),
      skip: Number(offset),
      order: { created_at: 'DESC' }
    });

    // Get total count
    const totalCustomers = await customerModuleService.listCustomers(filters);
    const total = totalCustomers.length;

    // Filter by tenant if metadata exists
    const tenantFilteredCustomers = customers.filter(customer => 
      !customer.metadata?.tenant_id || customer.metadata.tenant_id === tenantId
    );

    logger.info(`[ADMIN CUSTOMERS] Found ${tenantFilteredCustomers.length}/${customers.length} customers for tenant: ${tenantId}`);

    res.status(200).json({
      customers: tenantFilteredCustomers.map(customer => ({
        id: customer.id,
        email: customer.email,
        first_name: customer.first_name,
        last_name: customer.last_name,
        phone: customer.phone,
        has_account: customer.has_account,
        created_at: customer.created_at,
        updated_at: customer.updated_at,
        metadata: customer.metadata
      })),
      count: tenantFilteredCustomers.length,
      offset: Number(offset),
      limit: Number(limit),
      total: total,
      metadata: {
        tenantId,
        filtering: {
          totalBeforeFiltering: customers.length,
          totalAfterFiltering: tenantFilteredCustomers.length,
          returned: tenantFilteredCustomers.length
        },
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);
    logger.error('[ADMIN CUSTOMERS] Error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error.message || 'Failed to retrieve customers',
      details: process.env.NODE_ENV === "development" ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * POST /admin/customers
 * Create a new customer
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { 
      email, 
      first_name, 
      last_name, 
      phone, 
      has_account = false,
      metadata = {} 
    } = req.body;
    
    const customerModuleService = req.scope.resolve("customer");
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);

    logger.info(`[ADMIN CUSTOMERS] POST request for tenant: ${tenantId}`);

    // Validate required fields
    if (!email || !first_name || !last_name) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'email, first_name, and last_name are required',
        timestamp: new Date().toISOString()
      });
    }

    // Check if customer already exists
    const existingCustomers = await customerModuleService.listCustomers({
      email: email.toLowerCase().trim()
    });

    if (existingCustomers && existingCustomers.length > 0) {
      return res.status(409).json({
        error: 'Customer already exists',
        message: 'A customer with this email already exists',
        existing_customer: {
          id: existingCustomers[0].id,
          email: existingCustomers[0].email
        },
        timestamp: new Date().toISOString()
      });
    }

    // Create customer data
    const customerData = {
      email: email.toLowerCase().trim(),
      first_name: first_name.trim(),
      last_name: last_name.trim(),
      phone: phone || null,
      has_account: Boolean(has_account),
      metadata: {
        ...metadata,
        tenant_id: tenantId,
        created_via: "admin_api",
        created_by: "admin",
        creation_date: new Date().toISOString()
      }
    };

    logger.info(`[ADMIN CUSTOMERS] Creating customer:`, { email: customerData.email, tenant: tenantId });

    // Create the customer
    const customer = await customerModuleService.createCustomers(customerData);

    logger.info(`[ADMIN CUSTOMERS] Customer created successfully:`, customer.id);

    res.status(201).json({
      customer: {
        id: customer.id,
        email: customer.email,
        first_name: customer.first_name,
        last_name: customer.last_name,
        phone: customer.phone,
        has_account: customer.has_account,
        created_at: customer.created_at,
        updated_at: customer.updated_at,
        metadata: customer.metadata
      },
      message: 'Customer created successfully',
      tenant_id: tenantId,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);
    logger.error('[ADMIN CUSTOMERS] Error creating customer:', error);
    res.status(500).json({
      error: 'Customer creation failed',
      message: error.message || 'Failed to create customer',
      details: process.env.NODE_ENV === "development" ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * PUT /admin/customers
 * Bulk update customers (requires customer_ids in body)
 */
export async function PUT(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { customer_ids, updates } = req.body;
    
    const customerModuleService = req.scope.resolve("customer");
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);

    logger.info(`[ADMIN CUSTOMERS] PUT request for tenant: ${tenantId}`);

    if (!customer_ids || !Array.isArray(customer_ids) || customer_ids.length === 0) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'customer_ids array is required',
        timestamp: new Date().toISOString()
      });
    }

    if (!updates || typeof updates !== 'object') {
      return res.status(400).json({
        error: 'Validation error',
        message: 'updates object is required',
        timestamp: new Date().toISOString()
      });
    }

    const updatedCustomers = [];
    const errors = [];

    for (const customerId of customer_ids) {
      try {
        // Get existing customer
        const existingCustomer = await customerModuleService.retrieveCustomer(customerId);
        
        if (!existingCustomer) {
          errors.push({ customer_id: customerId, error: 'Customer not found' });
          continue;
        }

        // Check tenant access
        if (existingCustomer.metadata?.tenant_id && existingCustomer.metadata.tenant_id !== tenantId) {
          errors.push({ customer_id: customerId, error: 'Access denied' });
          continue;
        }

        // Prepare update data
        const updateData = {
          ...updates,
          metadata: {
            ...existingCustomer.metadata,
            ...updates.metadata,
            tenant_id: tenantId,
            updated_via: "admin_api",
            updated_by: "admin",
            last_updated: new Date().toISOString()
          }
        };

        // Update customer
        const updatedCustomer = await customerModuleService.updateCustomers(customerId, updateData);
        updatedCustomers.push(updatedCustomer);

      } catch (error) {
        logger.error(`[ADMIN CUSTOMERS] Error updating customer ${customerId}:`, error);
        errors.push({ 
          customer_id: customerId, 
          error: error instanceof Error ? error.message : 'Update failed' 
        });
      }
    }

    res.status(200).json({
      updated_customers: updatedCustomers,
      success_count: updatedCustomers.length,
      error_count: errors.length,
      errors: errors,
      message: `Updated ${updatedCustomers.length} customers successfully`,
      tenant_id: tenantId,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);
    logger.error('[ADMIN CUSTOMERS] Error in bulk update:', error);
    res.status(500).json({
      error: 'Bulk update failed',
      message: error.message || 'Failed to update customers',
      details: process.env.NODE_ENV === "development" ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });
  }
}
