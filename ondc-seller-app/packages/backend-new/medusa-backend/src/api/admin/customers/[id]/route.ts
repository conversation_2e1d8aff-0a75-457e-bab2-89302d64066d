import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { ContainerRegistrationKeys } from "@medusajs/framework/utils"

/**
 * Admin Individual Customer Management API
 * CRUD operations for individual customers
 */

/**
 * GET /admin/customers/{id}
 * Get a specific customer by ID
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { id } = req.params;
    
    const customerModuleService = req.scope.resolve("customer");
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);

    logger.info(`[ADMIN CUSTOMER] GET request for customer ${id}, tenant: ${tenantId}`);

    if (!id) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'Customer ID is required',
        timestamp: new Date().toISOString()
      });
    }

    // Get customer
    const customer = await customerModuleService.retrieveCustomer(id);

    if (!customer) {
      return res.status(404).json({
        error: 'Customer not found',
        message: `Customer with ID ${id} not found`,
        timestamp: new Date().toISOString()
      });
    }

    // Check tenant access
    if (customer.metadata?.tenant_id && customer.metadata.tenant_id !== tenantId) {
      return res.status(404).json({
        error: 'Customer not found',
        message: `Customer with ID ${id} not found`,
        timestamp: new Date().toISOString()
      });
    }

    res.status(200).json({
      customer: {
        id: customer.id,
        email: customer.email,
        first_name: customer.first_name,
        last_name: customer.last_name,
        phone: customer.phone,
        has_account: customer.has_account,
        created_at: customer.created_at,
        updated_at: customer.updated_at,
        metadata: customer.metadata
      },
      tenant_id: tenantId,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);
    logger.error(`[ADMIN CUSTOMER] Error retrieving customer:`, error);
    res.status(500).json({
      error: 'Internal server error',
      message: error.message || 'Failed to retrieve customer',
      details: process.env.NODE_ENV === "development" ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * PUT /admin/customers/{id}
 * Update a specific customer
 */
export async function PUT(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { id } = req.params;
    const { 
      email, 
      first_name, 
      last_name, 
      phone, 
      has_account,
      metadata = {} 
    } = req.body;
    
    const customerModuleService = req.scope.resolve("customer");
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);

    logger.info(`[ADMIN CUSTOMER] PUT request for customer ${id}, tenant: ${tenantId}`);

    if (!id) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'Customer ID is required',
        timestamp: new Date().toISOString()
      });
    }

    // Get existing customer
    const existingCustomer = await customerModuleService.retrieveCustomer(id);

    if (!existingCustomer) {
      return res.status(404).json({
        error: 'Customer not found',
        message: `Customer with ID ${id} not found`,
        timestamp: new Date().toISOString()
      });
    }

    // Check tenant access
    if (existingCustomer.metadata?.tenant_id && existingCustomer.metadata.tenant_id !== tenantId) {
      return res.status(404).json({
        error: 'Customer not found',
        message: `Customer with ID ${id} not found`,
        timestamp: new Date().toISOString()
      });
    }

    // If email is being updated, check for conflicts
    if (email && email.toLowerCase().trim() !== existingCustomer.email) {
      const existingWithEmail = await customerModuleService.listCustomers({
        email: email.toLowerCase().trim()
      });

      if (existingWithEmail && existingWithEmail.length > 0) {
        return res.status(409).json({
          error: 'Email already exists',
          message: 'A customer with this email already exists',
          timestamp: new Date().toISOString()
        });
      }
    }

    // Prepare update data
    const updateData: any = {};
    
    if (email !== undefined) updateData.email = email.toLowerCase().trim();
    if (first_name !== undefined) updateData.first_name = first_name.trim();
    if (last_name !== undefined) updateData.last_name = last_name.trim();
    if (phone !== undefined) updateData.phone = phone;
    if (has_account !== undefined) updateData.has_account = Boolean(has_account);
    
    updateData.metadata = {
      ...existingCustomer.metadata,
      ...metadata,
      tenant_id: tenantId,
      updated_via: "admin_api",
      updated_by: "admin",
      last_updated: new Date().toISOString()
    };

    logger.info(`[ADMIN CUSTOMER] Updating customer ${id}:`, updateData);

    // Update customer
    const updatedCustomer = await customerModuleService.updateCustomers(id, updateData);

    logger.info(`[ADMIN CUSTOMER] Customer ${id} updated successfully`);

    res.status(200).json({
      customer: {
        id: updatedCustomer.id,
        email: updatedCustomer.email,
        first_name: updatedCustomer.first_name,
        last_name: updatedCustomer.last_name,
        phone: updatedCustomer.phone,
        has_account: updatedCustomer.has_account,
        created_at: updatedCustomer.created_at,
        updated_at: updatedCustomer.updated_at,
        metadata: updatedCustomer.metadata
      },
      message: 'Customer updated successfully',
      tenant_id: tenantId,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);
    logger.error(`[ADMIN CUSTOMER] Error updating customer:`, error);
    res.status(500).json({
      error: 'Customer update failed',
      message: error.message || 'Failed to update customer',
      details: process.env.NODE_ENV === "development" ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * DELETE /admin/customers/{id}
 * Delete a specific customer (soft delete by updating metadata)
 */
export async function DELETE(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { id } = req.params;
    
    const customerModuleService = req.scope.resolve("customer");
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);

    logger.info(`[ADMIN CUSTOMER] DELETE request for customer ${id}, tenant: ${tenantId}`);

    if (!id) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'Customer ID is required',
        timestamp: new Date().toISOString()
      });
    }

    // Get existing customer
    const existingCustomer = await customerModuleService.retrieveCustomer(id);

    if (!existingCustomer) {
      return res.status(404).json({
        error: 'Customer not found',
        message: `Customer with ID ${id} not found`,
        timestamp: new Date().toISOString()
      });
    }

    // Check tenant access
    if (existingCustomer.metadata?.tenant_id && existingCustomer.metadata.tenant_id !== tenantId) {
      return res.status(404).json({
        error: 'Customer not found',
        message: `Customer with ID ${id} not found`,
        timestamp: new Date().toISOString()
      });
    }

    // Soft delete by updating metadata
    const updateData = {
      metadata: {
        ...existingCustomer.metadata,
        deleted: true,
        deleted_at: new Date().toISOString(),
        deleted_via: "admin_api",
        deleted_by: "admin",
        tenant_id: tenantId
      }
    };

    logger.info(`[ADMIN CUSTOMER] Soft deleting customer ${id}`);

    // Update customer with deletion metadata
    await customerModuleService.updateCustomers(id, updateData);

    logger.info(`[ADMIN CUSTOMER] Customer ${id} soft deleted successfully`);

    res.status(200).json({
      success: true,
      message: 'Customer deleted successfully',
      customer_id: id,
      tenant_id: tenantId,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);
    logger.error(`[ADMIN CUSTOMER] Error deleting customer:`, error);
    res.status(500).json({
      error: 'Customer deletion failed',
      message: error.message || 'Failed to delete customer',
      details: process.env.NODE_ENV === "development" ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });
  }
}
