import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { ContainerRegistrationKeys } from "@medusajs/framework/utils"

/**
 * Admin Order Management API
 * Full CRUD operations for order management
 */

/**
 * GET /admin/orders
 * List all orders with filtering and pagination
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { 
      limit = 20, 
      offset = 0, 
      q, 
      status,
      customer_id,
      email,
      payment_status,
      fulfillment_status,
      created_at_gte,
      created_at_lte,
      updated_at_gte,
      updated_at_lte
    } = req.query;
    
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);

    logger.info(`[ADMIN ORDERS] GET request for tenant: ${tenantId}`);

    // Build filters
    const filters: any = {};

    if (q && typeof q === 'string') {
      filters.$or = [
        { email: { $ilike: `%${q}%` } },
        { display_id: { $ilike: `%${q}%` } }
      ];
    }

    if (status && typeof status === 'string') {
      filters.status = status;
    }

    if (customer_id && typeof customer_id === 'string') {
      filters.customer_id = customer_id;
    }

    if (email && typeof email === 'string') {
      filters.email = { $ilike: `%${email}%` };
    }

    if (created_at_gte) {
      filters.created_at = { ...filters.created_at, $gte: new Date(created_at_gte as string) };
    }

    if (created_at_lte) {
      filters.created_at = { ...filters.created_at, $lte: new Date(created_at_lte as string) };
    }

    if (updated_at_gte) {
      filters.updated_at = { ...filters.updated_at, $gte: new Date(updated_at_gte as string) };
    }

    if (updated_at_lte) {
      filters.updated_at = { ...filters.updated_at, $lte: new Date(updated_at_lte as string) };
    }

    // Get orders with relations
    const { data: allOrders } = await query.graph({
      entity: "order",
      fields: [
        "id",
        "display_id",
        "status",
        "currency_code",
        "email",
        "customer_id",
        "total",
        "subtotal",
        "tax_total",
        "shipping_total",
        "created_at",
        "updated_at",
        "metadata",
        "items.*",
        "items.product.*",
        "items.variant.*",
        "shipping_address.*",
        "billing_address.*"
      ],
      filters,
      pagination: {
        skip: 0,
        take: 200 // Fetch more to allow for tenant filtering
      }
    });

    // Apply tenant-specific filtering (for admin, show all but mark tenant ownership)
    const tenantFilteredOrders = allOrders.map(order => ({
      ...order,
      tenant_owned: order.metadata?.tenant_id === tenantId,
      tenant_id: order.metadata?.tenant_id || 'unassigned'
    }));

    // Apply pagination after tenant processing
    const startIndex = Number(offset);
    const endIndex = startIndex + Number(limit);
    const orders = tenantFilteredOrders.slice(startIndex, endIndex);

    logger.info(`[ADMIN ORDERS] Found ${orders.length}/${tenantFilteredOrders.length} orders for tenant: ${tenantId}`);

    res.status(200).json({
      orders: orders,
      count: orders.length,
      offset: Number(offset),
      limit: Number(limit),
      total: tenantFilteredOrders.length,
      metadata: {
        tenantId,
        filtering: {
          totalBeforeFiltering: allOrders.length,
          totalAfterFiltering: tenantFilteredOrders.length,
          returned: orders.length
        },
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);
    logger.error('[ADMIN ORDERS] Error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error.message || 'Failed to retrieve orders',
      details: process.env.NODE_ENV === "development" ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * POST /admin/orders
 * Create a new order (manual order creation)
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { 
      email, 
      customer_id,
      currency_code = 'EUR',
      region_id,
      sales_channel_id,
      items = [],
      shipping_address,
      billing_address,
      metadata = {} 
    } = req.body;
    
    const orderModuleService = req.scope.resolve("order");
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);

    logger.info(`[ADMIN ORDERS] POST request for tenant: ${tenantId}`);

    // Validate required fields
    if (!email || !items || items.length === 0) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'email and items are required',
        timestamp: new Date().toISOString()
      });
    }

    if (!shipping_address || !shipping_address.first_name || !shipping_address.address_1) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'shipping_address with first_name and address_1 is required',
        timestamp: new Date().toISOString()
      });
    }

    // Calculate totals
    const subtotal = items.reduce((sum: number, item: any) => {
      return sum + (item.unit_price * item.quantity);
    }, 0);

    const tax_total = Math.round(subtotal * 0.18); // 18% GST
    const shipping_total = subtotal > 50000 ? 0 : 500; // Free shipping above €500
    const total = subtotal + tax_total + shipping_total;

    // Create order data
    const orderData = {
      currency_code: currency_code,
      email: email.toLowerCase().trim(),
      customer_id: customer_id || null,
      region_id: region_id || null,
      sales_channel_id: sales_channel_id || null,
      status: "pending",
      metadata: {
        ...metadata,
        tenant_id: tenantId,
        created_via: "admin_api",
        created_by: "admin",
        creation_date: new Date().toISOString()
      },
      items: items.map((item: any) => ({
        title: item.title || "Product",
        subtitle: item.subtitle || "",
        quantity: item.quantity,
        unit_price: item.unit_price,
        variant_id: item.variant_id || null,
        product_id: item.product_id || null,
        metadata: {
          admin_created: true
        }
      })),
      shipping_address: {
        first_name: shipping_address.first_name,
        last_name: shipping_address.last_name || "",
        address_1: shipping_address.address_1,
        address_2: shipping_address.address_2 || "",
        city: shipping_address.city || "",
        postal_code: shipping_address.postal_code || "",
        province: shipping_address.province || shipping_address.state || "",
        country_code: shipping_address.country_code || "IN",
        phone: shipping_address.phone || ""
      },
      billing_address: billing_address || shipping_address
    };

    logger.info(`[ADMIN ORDERS] Creating order:`, { email: orderData.email, items: orderData.items.length, tenant: tenantId });

    // Create the order
    const order = await orderModuleService.createOrders(orderData);

    logger.info(`[ADMIN ORDERS] Order created successfully:`, order.id);

    res.status(201).json({
      order: {
        id: order.id,
        display_id: order.display_id || order.id,
        status: order.status,
        currency_code: order.currency_code,
        email: order.email,
        customer_id: order.customer_id,
        items: order.items || orderData.items,
        shipping_address: order.shipping_address || orderData.shipping_address,
        billing_address: order.billing_address || orderData.billing_address,
        subtotal: subtotal,
        tax_total: tax_total,
        shipping_total: shipping_total,
        total: total,
        created_at: order.created_at,
        metadata: order.metadata,
        tenant_owned: true,
        tenant_id: tenantId
      },
      message: 'Order created successfully',
      tenant_id: tenantId,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);
    logger.error('[ADMIN ORDERS] Error creating order:', error);
    res.status(500).json({
      error: 'Order creation failed',
      message: error.message || 'Failed to create order',
      details: process.env.NODE_ENV === "development" ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * PUT /admin/orders
 * Bulk update orders (requires order_ids in body)
 */
export async function PUT(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { order_ids, updates } = req.body;
    
    const orderModuleService = req.scope.resolve("order");
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);

    logger.info(`[ADMIN ORDERS] PUT request for tenant: ${tenantId}`);

    if (!order_ids || !Array.isArray(order_ids) || order_ids.length === 0) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'order_ids array is required',
        timestamp: new Date().toISOString()
      });
    }

    if (!updates || typeof updates !== 'object') {
      return res.status(400).json({
        error: 'Validation error',
        message: 'updates object is required',
        timestamp: new Date().toISOString()
      });
    }

    const updatedOrders = [];
    const errors = [];

    for (const orderId of order_ids) {
      try {
        // Get existing order
        const existingOrder = await orderModuleService.retrieveOrder(orderId);
        
        if (!existingOrder) {
          errors.push({ order_id: orderId, error: 'Order not found' });
          continue;
        }

        // Prepare update data
        const updateData = {
          ...updates,
          metadata: {
            ...existingOrder.metadata,
            ...updates.metadata,
            tenant_id: tenantId,
            updated_via: "admin_api",
            updated_by: "admin",
            last_updated: new Date().toISOString()
          }
        };

        // Update order
        const updatedOrder = await orderModuleService.updateOrders(orderId, updateData);
        updatedOrders.push(updatedOrder);

      } catch (error) {
        logger.error(`[ADMIN ORDERS] Error updating order ${orderId}:`, error);
        errors.push({ 
          order_id: orderId, 
          error: error instanceof Error ? error.message : 'Update failed' 
        });
      }
    }

    res.status(200).json({
      updated_orders: updatedOrders,
      success_count: updatedOrders.length,
      error_count: errors.length,
      errors: errors,
      message: `Updated ${updatedOrders.length} orders successfully`,
      tenant_id: tenantId,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);
    logger.error('[ADMIN ORDERS] Error in bulk update:', error);
    res.status(500).json({
      error: 'Bulk update failed',
      message: error.message || 'Failed to update orders',
      details: process.env.NODE_ENV === "development" ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });
  }
}
