import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { ContainerRegistrationKeys } from "@medusajs/framework/utils"

/**
 * Admin Individual Order Management API
 * CRUD operations for individual orders
 */

/**
 * GET /admin/orders/{id}
 * Get a specific order by ID
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { id } = req.params;
    
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);

    logger.info(`[ADMIN ORDER] GET request for order ${id}, tenant: ${tenantId}`);

    if (!id) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'Order ID is required',
        timestamp: new Date().toISOString()
      });
    }

    // Get order with all relations
    const { data: orders } = await query.graph({
      entity: "order",
      fields: [
        "id",
        "display_id",
        "status",
        "currency_code",
        "email",
        "customer_id",
        "total",
        "subtotal",
        "tax_total",
        "shipping_total",
        "created_at",
        "updated_at",
        "metadata",
        "items.*",
        "items.product.*",
        "items.variant.*",
        "shipping_address.*",
        "billing_address.*"
      ],
      filters: {
        id: id
      }
    });

    if (!orders || orders.length === 0) {
      return res.status(404).json({
        error: 'Order not found',
        message: `Order with ID ${id} not found`,
        timestamp: new Date().toISOString()
      });
    }

    const order = orders[0];

    res.status(200).json({
      order: {
        ...order,
        tenant_owned: order.metadata?.tenant_id === tenantId,
        tenant_id: order.metadata?.tenant_id || 'unassigned'
      },
      tenant_id: tenantId,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);
    logger.error(`[ADMIN ORDER] Error retrieving order:`, error);
    res.status(500).json({
      error: 'Internal server error',
      message: error.message || 'Failed to retrieve order',
      details: process.env.NODE_ENV === "development" ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * PUT /admin/orders/{id}
 * Update a specific order
 */
export async function PUT(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { id } = req.params;
    const { 
      status,
      email,
      shipping_address,
      billing_address,
      metadata = {} 
    } = req.body;
    
    const orderModuleService = req.scope.resolve("order");
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);

    logger.info(`[ADMIN ORDER] PUT request for order ${id}, tenant: ${tenantId}`);

    if (!id) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'Order ID is required',
        timestamp: new Date().toISOString()
      });
    }

    // Get existing order
    const existingOrder = await orderModuleService.retrieveOrder(id);

    if (!existingOrder) {
      return res.status(404).json({
        error: 'Order not found',
        message: `Order with ID ${id} not found`,
        timestamp: new Date().toISOString()
      });
    }

    // Prepare update data
    const updateData: any = {};
    
    if (status !== undefined) updateData.status = status;
    if (email !== undefined) updateData.email = email.toLowerCase().trim();
    if (shipping_address !== undefined) updateData.shipping_address = shipping_address;
    if (billing_address !== undefined) updateData.billing_address = billing_address;
    
    updateData.metadata = {
      ...existingOrder.metadata,
      ...metadata,
      tenant_id: tenantId,
      updated_via: "admin_api",
      updated_by: "admin",
      last_updated: new Date().toISOString()
    };

    logger.info(`[ADMIN ORDER] Updating order ${id}:`, { status: updateData.status, email: updateData.email });

    // Update order
    const updatedOrder = await orderModuleService.updateOrders(id, updateData);

    logger.info(`[ADMIN ORDER] Order ${id} updated successfully`);

    res.status(200).json({
      order: {
        id: updatedOrder.id,
        display_id: updatedOrder.display_id || updatedOrder.id,
        status: updatedOrder.status,
        currency_code: updatedOrder.currency_code,
        email: updatedOrder.email,
        customer_id: updatedOrder.customer_id,
        total: updatedOrder.total,
        subtotal: updatedOrder.subtotal,
        tax_total: updatedOrder.tax_total,
        shipping_total: updatedOrder.shipping_total,
        created_at: updatedOrder.created_at,
        updated_at: updatedOrder.updated_at,
        metadata: updatedOrder.metadata,
        tenant_owned: updatedOrder.metadata?.tenant_id === tenantId,
        tenant_id: updatedOrder.metadata?.tenant_id || 'unassigned'
      },
      message: 'Order updated successfully',
      tenant_id: tenantId,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);
    logger.error(`[ADMIN ORDER] Error updating order:`, error);
    res.status(500).json({
      error: 'Order update failed',
      message: error.message || 'Failed to update order',
      details: process.env.NODE_ENV === "development" ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * DELETE /admin/orders/{id}
 * Cancel/Delete a specific order (soft delete by updating status and metadata)
 */
export async function DELETE(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { id } = req.params;
    
    const orderModuleService = req.scope.resolve("order");
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);

    logger.info(`[ADMIN ORDER] DELETE request for order ${id}, tenant: ${tenantId}`);

    if (!id) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'Order ID is required',
        timestamp: new Date().toISOString()
      });
    }

    // Get existing order
    const existingOrder = await orderModuleService.retrieveOrder(id);

    if (!existingOrder) {
      return res.status(404).json({
        error: 'Order not found',
        message: `Order with ID ${id} not found`,
        timestamp: new Date().toISOString()
      });
    }

    // Check if order can be cancelled
    if (existingOrder.status === 'completed' || existingOrder.status === 'shipped') {
      return res.status(400).json({
        error: 'Cannot cancel order',
        message: 'Order cannot be cancelled as it is already completed or shipped',
        current_status: existingOrder.status,
        timestamp: new Date().toISOString()
      });
    }

    // Cancel order by updating status and metadata
    const updateData = {
      status: 'cancelled',
      metadata: {
        ...existingOrder.metadata,
        cancelled: true,
        cancelled_at: new Date().toISOString(),
        cancelled_via: "admin_api",
        cancelled_by: "admin",
        cancellation_reason: "Admin cancellation",
        tenant_id: tenantId
      }
    };

    logger.info(`[ADMIN ORDER] Cancelling order ${id}`);

    // Update order with cancellation data
    const cancelledOrder = await orderModuleService.updateOrders(id, updateData);

    logger.info(`[ADMIN ORDER] Order ${id} cancelled successfully`);

    res.status(200).json({
      success: true,
      message: 'Order cancelled successfully',
      order: {
        id: cancelledOrder.id,
        display_id: cancelledOrder.display_id || cancelledOrder.id,
        status: cancelledOrder.status,
        cancelled_at: updateData.metadata.cancelled_at,
        tenant_id: tenantId
      },
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);
    logger.error(`[ADMIN ORDER] Error cancelling order:`, error);
    res.status(500).json({
      error: 'Order cancellation failed',
      message: error.message || 'Failed to cancel order',
      details: process.env.NODE_ENV === "development" ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });
  }
}
