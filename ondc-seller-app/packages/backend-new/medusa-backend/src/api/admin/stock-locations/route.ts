import { MedusaRequest, MedusaResponse } from "@medusajs/medusa"
import { TenantRequest, getTenantId } from "../../../middleware/tenant"
import { 
  resolveTenantAwareService, 
  enhanceResponseWithTenantInfo 
} from "../../../middleware/tenant-query-filter"

/**
 * Tenant-Aware Stock Locations API
 * Complete stock location management with tenant isolation
 */

export async function GET(req: TenantRequest, res: MedusaResponse) {
  try {
    const tenantId = getTenantId(req)
    
    // Get tenant-aware stock location service
    const stockLocationService = resolveTenantAwareService(req, "stockLocationModuleService")
    
    // Build query with tenant filtering
    const query = {
      ...req.query,
      tenant_id: tenantId
    }

    // Get stock locations with tenant filtering
    const result = await stockLocationService.listAndCount(query)
    
    // Enhance response with tenant info
    const response = enhanceResponseWithTenantInfo(req, {
      stock_locations: result.stock_locations || result[0] || [],
      count: result.count || result[1] || 0,
      offset: parseInt(query.offset as string) || 0,
      limit: parseInt(query.limit as string) || 50
    })

    res.json(response)
  } catch (error) {
    console.error('Stock Locations GET error:', error)
    res.status(500).json({
      type: "server_error",
      message: "Failed to fetch stock locations",
      tenant_id: getTenantId(req),
      error: error.message
    })
  }
}

export async function POST(req: TenantRequest, res: MedusaResponse) {
  try {
    const tenantId = getTenantId(req)
    
    // Get tenant-aware stock location service
    const stockLocationService = resolveTenantAwareService(req, "stockLocationModuleService")
    
    // Add tenant context to stock location data
    const stockLocationData = {
      ...req.body,
      tenant_id: tenantId
    }

    // Create stock location with tenant context
    const stockLocation = await stockLocationService.create(stockLocationData)
    
    // Enhance response with tenant info
    const response = enhanceResponseWithTenantInfo(req, {
      stock_location: stockLocation
    })

    res.status(201).json(response)
  } catch (error) {
    console.error('Stock Locations POST error:', error)
    res.status(500).json({
      type: "server_error",
      message: "Failed to create stock location",
      tenant_id: getTenantId(req),
      error: error.message
    })
  }
}
