import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { ContainerRegistrationKeys } from "@medusajs/framework/utils"

/**
 * Admin Individual Product Management API
 * CRUD operations for individual products
 */

/**
 * GET /admin/products/{id}
 * Get a specific product by ID
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { id } = req.params;
    
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);

    logger.info(`[ADMIN PRODUCT] GET request for product ${id}, tenant: ${tenantId}`);

    if (!id) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'Product ID is required',
        timestamp: new Date().toISOString()
      });
    }

    // Get product with all relations
    const { data: products } = await query.graph({
      entity: "product",
      fields: [
        "id",
        "title",
        "description",
        "handle",
        "status",
        "thumbnail",
        "created_at",
        "updated_at",
        "metadata",
        "variants.*",
        "variants.prices.*",
        "images.*",
        "tags.*",
        "categories.*",
        "collection.*"
      ],
      filters: {
        id: id
      }
    });

    if (!products || products.length === 0) {
      return res.status(404).json({
        error: 'Product not found',
        message: `Product with ID ${id} not found`,
        timestamp: new Date().toISOString()
      });
    }

    const product = products[0];

    res.status(200).json({
      product: {
        ...product,
        tenant_owned: product.metadata?.tenant_id === tenantId,
        tenant_id: product.metadata?.tenant_id || 'unassigned'
      },
      tenant_id: tenantId,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);
    logger.error(`[ADMIN PRODUCT] Error retrieving product:`, error);
    res.status(500).json({
      error: 'Internal server error',
      message: error.message || 'Failed to retrieve product',
      details: process.env.NODE_ENV === "development" ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * PUT /admin/products/{id}
 * Update a specific product
 */
export async function PUT(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { id } = req.params;
    const { 
      title, 
      description, 
      handle,
      status,
      thumbnail,
      images,
      variants,
      categories,
      collection_id,
      tags,
      metadata = {} 
    } = req.body;
    
    const productModuleService = req.scope.resolve("product");
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);

    logger.info(`[ADMIN PRODUCT] PUT request for product ${id}, tenant: ${tenantId}`);

    if (!id) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'Product ID is required',
        timestamp: new Date().toISOString()
      });
    }

    // Get existing product
    const existingProduct = await productModuleService.retrieveProduct(id);

    if (!existingProduct) {
      return res.status(404).json({
        error: 'Product not found',
        message: `Product with ID ${id} not found`,
        timestamp: new Date().toISOString()
      });
    }

    // If handle is being updated, check for conflicts
    if (handle && handle !== existingProduct.handle) {
      const existingWithHandle = await productModuleService.listProducts({
        handle: handle
      });

      if (existingWithHandle && existingWithHandle.length > 0) {
        return res.status(409).json({
          error: 'Handle already exists',
          message: 'A product with this handle already exists',
          timestamp: new Date().toISOString()
        });
      }
    }

    // Prepare update data
    const updateData: any = {};
    
    if (title !== undefined) updateData.title = title.trim();
    if (description !== undefined) updateData.description = description;
    if (handle !== undefined) updateData.handle = handle;
    if (status !== undefined) updateData.status = status;
    if (thumbnail !== undefined) updateData.thumbnail = thumbnail;
    if (images !== undefined) updateData.images = images;
    if (variants !== undefined) updateData.variants = variants;
    if (categories !== undefined) updateData.categories = categories;
    if (collection_id !== undefined) updateData.collection_id = collection_id;
    if (tags !== undefined) updateData.tags = tags;
    
    updateData.metadata = {
      ...existingProduct.metadata,
      ...metadata,
      tenant_id: tenantId,
      updated_via: "admin_api",
      updated_by: "admin",
      last_updated: new Date().toISOString()
    };

    logger.info(`[ADMIN PRODUCT] Updating product ${id}:`, { title: updateData.title, handle: updateData.handle });

    // Update product
    const updatedProduct = await productModuleService.updateProducts(id, updateData);

    logger.info(`[ADMIN PRODUCT] Product ${id} updated successfully`);

    res.status(200).json({
      product: {
        id: updatedProduct.id,
        title: updatedProduct.title,
        description: updatedProduct.description,
        handle: updatedProduct.handle,
        status: updatedProduct.status,
        thumbnail: updatedProduct.thumbnail,
        created_at: updatedProduct.created_at,
        updated_at: updatedProduct.updated_at,
        metadata: updatedProduct.metadata,
        tenant_owned: updatedProduct.metadata?.tenant_id === tenantId,
        tenant_id: updatedProduct.metadata?.tenant_id || 'unassigned'
      },
      message: 'Product updated successfully',
      tenant_id: tenantId,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);
    logger.error(`[ADMIN PRODUCT] Error updating product:`, error);
    res.status(500).json({
      error: 'Product update failed',
      message: error.message || 'Failed to update product',
      details: process.env.NODE_ENV === "development" ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * DELETE /admin/products/{id}
 * Delete a specific product (soft delete by updating metadata)
 */
export async function DELETE(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { id } = req.params;
    
    const productModuleService = req.scope.resolve("product");
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);

    logger.info(`[ADMIN PRODUCT] DELETE request for product ${id}, tenant: ${tenantId}`);

    if (!id) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'Product ID is required',
        timestamp: new Date().toISOString()
      });
    }

    // Get existing product
    const existingProduct = await productModuleService.retrieveProduct(id);

    if (!existingProduct) {
      return res.status(404).json({
        error: 'Product not found',
        message: `Product with ID ${id} not found`,
        timestamp: new Date().toISOString()
      });
    }

    // Soft delete by updating status and metadata
    const updateData = {
      status: 'deleted',
      metadata: {
        ...existingProduct.metadata,
        deleted: true,
        deleted_at: new Date().toISOString(),
        deleted_via: "admin_api",
        deleted_by: "admin",
        tenant_id: tenantId
      }
    };

    logger.info(`[ADMIN PRODUCT] Soft deleting product ${id}`);

    // Update product with deletion metadata
    await productModuleService.updateProducts(id, updateData);

    logger.info(`[ADMIN PRODUCT] Product ${id} soft deleted successfully`);

    res.status(200).json({
      success: true,
      message: 'Product deleted successfully',
      product_id: id,
      tenant_id: tenantId,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);
    logger.error(`[ADMIN PRODUCT] Error deleting product:`, error);
    res.status(500).json({
      error: 'Product deletion failed',
      message: error.message || 'Failed to delete product',
      details: process.env.NODE_ENV === "development" ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });
  }
}
