import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { ContainerRegistrationKeys } from "@medusajs/framework/utils"

/**
 * Admin Product Management API
 * Full CRUD operations for product management
 */

/**
 * GET /admin/products
 * List all products with filtering and pagination
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { 
      limit = 20, 
      offset = 0, 
      q, 
      status,
      collection_id,
      category_id,
      created_at_gte,
      created_at_lte,
      updated_at_gte,
      updated_at_lte
    } = req.query;
    
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);

    logger.info(`[ADMIN PRODUCTS] GET request for tenant: ${tenantId}`);

    // Build filters
    const filters: any = {};

    if (q && typeof q === 'string') {
      filters.$or = [
        { title: { $ilike: `%${q}%` } },
        { description: { $ilike: `%${q}%` } },
        { handle: { $ilike: `%${q}%` } }
      ];
    }

    if (status && typeof status === 'string') {
      filters.status = status;
    }

    if (collection_id && typeof collection_id === 'string') {
      filters.collection_id = collection_id;
    }

    if (category_id && typeof category_id === 'string') {
      filters.categories = {
        id: category_id
      };
    }

    if (created_at_gte) {
      filters.created_at = { ...filters.created_at, $gte: new Date(created_at_gte as string) };
    }

    if (created_at_lte) {
      filters.created_at = { ...filters.created_at, $lte: new Date(created_at_lte as string) };
    }

    if (updated_at_gte) {
      filters.updated_at = { ...filters.updated_at, $gte: new Date(updated_at_gte as string) };
    }

    if (updated_at_lte) {
      filters.updated_at = { ...filters.updated_at, $lte: new Date(updated_at_lte as string) };
    }

    // Get products with relations
    const { data: allProducts } = await query.graph({
      entity: "product",
      fields: [
        "id",
        "title",
        "description",
        "handle",
        "status",
        "thumbnail",
        "created_at",
        "updated_at",
        "metadata",
        "variants.*",
        "images.*",
        "tags.*",
        "categories.*",
        "collection.*"
      ],
      filters,
      pagination: {
        skip: 0,
        take: 200 // Fetch more to allow for tenant filtering
      }
    });

    // Apply tenant-specific filtering (for admin, show all but mark tenant ownership)
    const tenantFilteredProducts = allProducts.map(product => ({
      ...product,
      tenant_owned: product.metadata?.tenant_id === tenantId,
      tenant_id: product.metadata?.tenant_id || 'unassigned'
    }));

    // Apply pagination after tenant processing
    const startIndex = Number(offset);
    const endIndex = startIndex + Number(limit);
    const products = tenantFilteredProducts.slice(startIndex, endIndex);

    logger.info(`[ADMIN PRODUCTS] Found ${products.length}/${tenantFilteredProducts.length} products for tenant: ${tenantId}`);

    res.status(200).json({
      products: products,
      count: products.length,
      offset: Number(offset),
      limit: Number(limit),
      total: tenantFilteredProducts.length,
      metadata: {
        tenantId,
        filtering: {
          totalBeforeFiltering: allProducts.length,
          totalAfterFiltering: tenantFilteredProducts.length,
          returned: products.length
        },
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);
    logger.error('[ADMIN PRODUCTS] Error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error.message || 'Failed to retrieve products',
      details: process.env.NODE_ENV === "development" ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * POST /admin/products
 * Create a new product
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { 
      title, 
      description, 
      handle,
      status = 'draft',
      thumbnail,
      images = [],
      variants = [],
      categories = [],
      collection_id,
      tags = [],
      metadata = {} 
    } = req.body;
    
    const productModuleService = req.scope.resolve("product");
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);

    logger.info(`[ADMIN PRODUCTS] POST request for tenant: ${tenantId}`);

    // Validate required fields
    if (!title) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'title is required',
        timestamp: new Date().toISOString()
      });
    }

    // Generate handle if not provided
    const productHandle = handle || title.toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim('-');

    // Check if handle already exists
    try {
      const existingProducts = await productModuleService.listProducts({
        handle: productHandle
      });

      if (existingProducts && existingProducts.length > 0) {
        return res.status(409).json({
          error: 'Product handle already exists',
          message: 'A product with this handle already exists',
          existing_product: {
            id: existingProducts[0].id,
            handle: existingProducts[0].handle
          },
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      logger.warn(`[ADMIN PRODUCTS] Could not check existing product: ${error}`);
    }

    // Create product data
    const productData = {
      title: title.trim(),
      description: description || '',
      handle: productHandle,
      status: status,
      thumbnail: thumbnail || null,
      images: images,
      variants: variants.length > 0 ? variants : [{
        title: 'Default Variant',
        sku: `${productHandle}-default`,
        manage_inventory: true,
        allow_backorder: false,
        prices: []
      }],
      categories: categories,
      collection_id: collection_id || null,
      tags: tags,
      metadata: {
        ...metadata,
        tenant_id: tenantId,
        created_via: "admin_api",
        created_by: "admin",
        creation_date: new Date().toISOString()
      }
    };

    logger.info(`[ADMIN PRODUCTS] Creating product:`, { title: productData.title, handle: productData.handle, tenant: tenantId });

    // Create the product
    const product = await productModuleService.createProducts(productData);

    logger.info(`[ADMIN PRODUCTS] Product created successfully:`, product.id);

    res.status(201).json({
      product: {
        id: product.id,
        title: product.title,
        description: product.description,
        handle: product.handle,
        status: product.status,
        thumbnail: product.thumbnail,
        created_at: product.created_at,
        updated_at: product.updated_at,
        metadata: product.metadata,
        variants: product.variants || [],
        images: product.images || [],
        categories: product.categories || [],
        tags: product.tags || []
      },
      message: 'Product created successfully',
      tenant_id: tenantId,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);
    logger.error('[ADMIN PRODUCTS] Error creating product:', error);
    res.status(500).json({
      error: 'Product creation failed',
      message: error.message || 'Failed to create product',
      details: process.env.NODE_ENV === "development" ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * PUT /admin/products
 * Bulk update products (requires product_ids in body)
 */
export async function PUT(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { product_ids, updates } = req.body;
    
    const productModuleService = req.scope.resolve("product");
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);

    logger.info(`[ADMIN PRODUCTS] PUT request for tenant: ${tenantId}`);

    if (!product_ids || !Array.isArray(product_ids) || product_ids.length === 0) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'product_ids array is required',
        timestamp: new Date().toISOString()
      });
    }

    if (!updates || typeof updates !== 'object') {
      return res.status(400).json({
        error: 'Validation error',
        message: 'updates object is required',
        timestamp: new Date().toISOString()
      });
    }

    const updatedProducts = [];
    const errors = [];

    for (const productId of product_ids) {
      try {
        // Get existing product
        const existingProduct = await productModuleService.retrieveProduct(productId);
        
        if (!existingProduct) {
          errors.push({ product_id: productId, error: 'Product not found' });
          continue;
        }

        // Check tenant access (admin can update any product but should mark ownership)
        const updateData = {
          ...updates,
          metadata: {
            ...existingProduct.metadata,
            ...updates.metadata,
            tenant_id: tenantId,
            updated_via: "admin_api",
            updated_by: "admin",
            last_updated: new Date().toISOString()
          }
        };

        // Update product
        const updatedProduct = await productModuleService.updateProducts(productId, updateData);
        updatedProducts.push(updatedProduct);

      } catch (error) {
        logger.error(`[ADMIN PRODUCTS] Error updating product ${productId}:`, error);
        errors.push({ 
          product_id: productId, 
          error: error instanceof Error ? error.message : 'Update failed' 
        });
      }
    }

    res.status(200).json({
      updated_products: updatedProducts,
      success_count: updatedProducts.length,
      error_count: errors.length,
      errors: errors,
      message: `Updated ${updatedProducts.length} products successfully`,
      tenant_id: tenantId,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);
    logger.error('[ADMIN PRODUCTS] Error in bulk update:', error);
    res.status(500).json({
      error: 'Bulk update failed',
      message: error.message || 'Failed to update products',
      details: process.env.NODE_ENV === "development" ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });
  }
}
