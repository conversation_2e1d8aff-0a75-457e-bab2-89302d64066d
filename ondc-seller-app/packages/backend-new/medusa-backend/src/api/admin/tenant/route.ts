import { MedusaRequest, MedusaResponse } from "@medusajs/medusa"

/**
 * Tenant Information API
 * Simple endpoint to test tenant functionality
 */

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  // Extract tenant ID from header
  const tenantId = req.headers['x-tenant-id'] as string || 'default'

  // Mock tenant configurations
  const tenantConfigs = {
    'tenant-electronics-001': {
      id: 'tenant-electronics-001',
      name: 'Electronics Store',
      domain: 'electronics.ondc-seller.com',
      settings: {
        currency: 'INR',
        timezone: 'Asia/Kolkata',
        features: ['products', 'orders', 'customers', 'analytics', 'inventory'],
        ondcConfig: {
          participantId: 'electronics-participant-001',
          subscriberId: 'electronics-subscriber-001',
          bppId: 'ondc-bpp-electronics-001',
        },
      },
      status: 'active'
    },
    'tenant-fashion-002': {
      id: 'tenant-fashion-002',
      name: 'Fashion Store',
      domain: 'fashion.ondc-seller.com',
      settings: {
        currency: 'INR',
        timezone: 'Asia/Kolkata',
        features: ['products', 'orders', 'customers', 'promotions'],
        ondcConfig: {
          participantId: 'fashion-participant-002',
          subscriberId: 'fashion-subscriber-002',
          bppId: 'ondc-bpp-fashion-002',
        },
      },
      status: 'active'
    },
    'tenant-books-003': {
      id: 'tenant-books-003',
      name: 'Books Store',
      domain: 'books.ondc-seller.com',
      settings: {
        currency: 'INR',
        timezone: 'Asia/Kolkata',
        features: ['products', 'orders', 'customers'],
        ondcConfig: {
          participantId: 'books-participant-003',
          subscriberId: 'books-subscriber-003',
          bppId: 'ondc-bpp-books-003',
        },
      },
      status: 'active'
    },
    'default': {
      id: 'default',
      name: 'Default Store',
      domain: 'localhost',
      settings: {
        currency: 'INR',
        timezone: 'Asia/Kolkata',
        features: ['all'],
        ondcConfig: {
          participantId: 'default-participant',
          subscriberId: 'default-subscriber',
          bppId: 'ondc-bpp-default',
        },
      },
      status: 'active'
    }
  }

  const tenantConfig = tenantConfigs[tenantId] || tenantConfigs['default']

  res.json({
    success: true,
    tenant: tenantConfig,
    request_info: {
      tenant_header: req.headers['x-tenant-id'],
      resolved_tenant: tenantId,
      timestamp: new Date().toISOString()
    },
    multi_tenancy: {
      enabled: true,
      available_tenants: Object.keys(tenantConfigs),
      database_isolation: {
        products: 'tenant_id column added',
        customers: 'tenant_id column added',
        orders: 'tenant_id column added'
      }
    }
  })
}

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const tenantId = req.headers['x-tenant-id'] as string || 'default'
  
  res.json({
    message: `Tenant configuration updated for: ${tenantId}`,
    tenant_id: tenantId,
    updated_config: req.body,
    timestamp: new Date().toISOString()
  })
}
