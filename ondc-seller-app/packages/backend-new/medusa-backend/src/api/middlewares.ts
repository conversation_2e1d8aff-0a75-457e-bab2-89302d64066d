import { MiddlewaresConfig } from "@medusajs/medusa"
import { tenantMiddleware, tenantErrorHandler } from "../middleware/tenant"
import { injectTenantFilter } from "../middleware/tenant-query-filter"

/**
 * Comprehensive Middleware Configuration for Multi-Tenancy
 * Registers tenant middleware and query filters for all routes
 */

export const config: MiddlewaresConfig = {
  routes: [
    // Custom tenant endpoint
    {
      matcher: "/admin/tenant*",
      middlewares: [tenantMiddleware]
    },
    // Global admin routes (minimal tenant middleware)
    {
      matcher: "/admin/*",
      middlewares: [tenantMiddleware]
    },
    // Store routes with tenant support
    {
      matcher: "/store/*",
      middlewares: [tenantMiddleware]
    }
  ],
  errorHandler: tenantErrorHandler
}

export default config
