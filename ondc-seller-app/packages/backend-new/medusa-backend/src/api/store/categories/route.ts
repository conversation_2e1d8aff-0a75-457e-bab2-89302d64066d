import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";
import { ContainerRegistrationKeys } from "@medusajs/framework/utils";

// Tenant-specific category filtering configuration
const TENANT_CATEGORY_CONFIG = {
  'tenant-electronics-001': {
    allowedCategories: ['Electronics', 'Tech', 'Gadgets', 'Jackets'],
    categoryFilter: (categories: any[]) => {
      return categories.filter(category =>
        category.name?.toLowerCase().includes('jacket') ||
        category.name?.toLowerCase().includes('electronics') ||
        category.name?.toLowerCase().includes('tech') ||
        category.name?.toLowerCase().includes('men') ||
        category.parent_category?.name?.toLowerCase().includes('men')
      )
    }
  },
  'tenant-fashion-002': {
    allowedCategories: ['Fashion', 'Clothing', 'Accessories', 'Women', 'Men'],
    categoryFilter: (categories: any[]) => {
      return categories.filter(category =>
        category.name?.toLowerCase().includes('women') ||
        category.name?.toLowerCase().includes('fashion') ||
        category.name?.toLowerCase().includes('clothing') ||
        category.name?.toLowerCase().includes('shirt') ||
        category.parent_category?.name?.toLowerCase().includes('women')
      )
    }
  },
  'default': {
    allowedCategories: [],
    categoryFilter: (categories: any[]) => categories // Return all categories for default tenant
  }
}

// Legacy mock data for reference (will be replaced with real data)
const mockCategories = {
  'tenant-electronics-001': [
    {
      id: 'cat_electronics_001',
      name: 'Smartphones',
      slug: 'smartphones',
      description: 'Latest smartphones and mobile devices',
      image: '/images/categories/smartphones.jpg',
      parent_id: null,
      product_count: 15,
      status: 'active',
      sort_order: 1,
      tenant_id: 'tenant-electronics-001',
      seo: {
        title: 'Smartphones - Latest Mobile Devices',
        description: 'Shop the latest smartphones with best prices',
        keywords: ['smartphones', 'mobile', 'android', 'ios']
      },
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    },
    {
      id: 'cat_electronics_002',
      name: 'Laptops',
      slug: 'laptops',
      description: 'High-performance laptops and notebooks',
      image: '/images/categories/laptops.jpg',
      parent_id: null,
      product_count: 12,
      status: 'active',
      sort_order: 2,
      tenant_id: 'tenant-electronics-001',
      seo: {
        title: 'Laptops - High Performance Computing',
        description: 'Best laptops for work, gaming, and productivity',
        keywords: ['laptops', 'computers', 'gaming', 'work']
      },
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    },
    {
      id: 'cat_electronics_003',
      name: 'Audio',
      slug: 'audio',
      description: 'Headphones, speakers, and audio accessories',
      image: '/images/categories/audio.jpg',
      parent_id: null,
      product_count: 8,
      status: 'active',
      sort_order: 3,
      tenant_id: 'tenant-electronics-001',
      seo: {
        title: 'Audio - Headphones & Speakers',
        description: 'Premium audio equipment and accessories',
        keywords: ['headphones', 'speakers', 'audio', 'music']
      },
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    }
  ],
  'tenant-fashion-002': [
    {
      id: 'cat_fashion_001',
      name: 'Men\'s Clothing',
      slug: 'mens-clothing',
      description: 'Stylish clothing for men',
      image: '/images/categories/mens-clothing.jpg',
      parent_id: null,
      product_count: 20,
      status: 'active',
      sort_order: 1,
      tenant_id: 'tenant-fashion-002',
      seo: {
        title: 'Men\'s Clothing - Fashion & Style',
        description: 'Latest fashion trends for men',
        keywords: ['mens', 'clothing', 'fashion', 'style']
      },
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    },
    {
      id: 'cat_fashion_002',
      name: 'Women\'s Clothing',
      slug: 'womens-clothing',
      description: 'Elegant clothing for women',
      image: '/images/categories/womens-clothing.jpg',
      parent_id: null,
      product_count: 25,
      status: 'active',
      sort_order: 2,
      tenant_id: 'tenant-fashion-002',
      seo: {
        title: 'Women\'s Clothing - Fashion & Style',
        description: 'Latest fashion trends for women',
        keywords: ['womens', 'clothing', 'fashion', 'style']
      },
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    },
    {
      id: 'cat_fashion_003',
      name: 'Accessories',
      slug: 'accessories',
      description: 'Fashion accessories and jewelry',
      image: '/images/categories/accessories.jpg',
      parent_id: null,
      product_count: 15,
      status: 'active',
      sort_order: 3,
      tenant_id: 'tenant-fashion-002',
      seo: {
        title: 'Fashion Accessories - Complete Your Look',
        description: 'Stylish accessories and jewelry',
        keywords: ['accessories', 'jewelry', 'bags', 'fashion']
      },
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    }
  ],
  'default': [
    {
      id: 'cat_default_001',
      name: 'General',
      slug: 'general',
      description: 'General category',
      image: '/images/categories/general.jpg',
      parent_id: null,
      product_count: 5,
      status: 'active',
      sort_order: 1,
      tenant_id: 'default',
      seo: {
        title: 'General Products',
        description: 'General product category',
        keywords: ['general', 'products']
      },
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    }
  ]
};

/**
 * GET /store/categories
 * List categories for a tenant using real Medusa categories with tenant filtering
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const {
      limit = 20,
      offset = 0,
      featured,
      parentOnly,
      parent_id,
      status,
      includeProducts,
      includeChildren
    } = req.query;

    console.log(`[CATEGORIES API] GET request for tenant: ${tenantId}`);

    // Use the query system to get all categories
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

    // Build filters for Medusa categories
    const filters: any = {
      is_active: true
    };

    if (parent_id) {
      filters.parent_category_id = parent_id;
    } else if (parentOnly === 'true') {
      filters.parent_category_id = null;
    }

    // Get categories with relations
    const { data: allCategories } = await query.graph({
      entity: "product_category",
      fields: [
        "id",
        "name",
        "description",
        "handle",
        "is_active",
        "rank",
        "metadata",
        "parent_category_id",
        "parent_category.*",
        "category_children.*",
        "created_at",
        "updated_at"
      ],
      filters,
      pagination: {
        skip: 0,
        take: 100 // Fetch more to allow for tenant filtering
      }
    });

    // Apply tenant-specific filtering
    const tenantConfig = TENANT_CATEGORY_CONFIG[tenantId] || TENANT_CATEGORY_CONFIG['default'];
    const tenantFilteredCategories = tenantConfig.categoryFilter(allCategories);

    // Apply additional filters
    let categories = tenantFilteredCategories;

    // Apply additional filters
    if (featured === 'true') {
      categories = categories.filter(cat => cat.rank <= 3); // Featured = top 3 by rank
    }

    // Apply pagination
    const startIndex = Number(offset);
    const endIndex = startIndex + Number(limit);
    const paginatedCategories = categories.slice(startIndex, endIndex);

    // Transform categories to match expected format
    const transformedCategories = paginatedCategories.map(category => ({
      id: category.id,
      name: category.name,
      description: category.description || '',
      handle: category.handle,
      is_active: category.is_active,
      rank: category.rank,
      parent_category_id: category.parent_category_id,
      parent_category: category.parent_category,
      category_children: category.category_children || [],
      metadata: category.metadata,
      created_at: category.created_at,
      updated_at: category.updated_at,
      // Legacy fields for compatibility
      slug: category.handle,
      status: category.is_active ? 'active' : 'inactive',
      sort_order: category.rank || 999,
      tenant_id: tenantId
    }));

    console.log(`[CATEGORIES API] Found ${transformedCategories.length}/${categories.length} categories for tenant: ${tenantId}`);

    res.status(200).json({
      product_categories: transformedCategories, // Use Medusa standard field name
      categories: transformedCategories, // Keep legacy field for compatibility
      count: transformedCategories.length,
      offset: Number(offset),
      limit: Number(limit),
      total: categories.length,
      metadata: {
        tenantId,
        tenantConfig: {
          allowedCategories: tenantConfig.allowedCategories
        },
        filtering: {
          totalBeforeFiltering: allCategories.length,
          totalAfterFiltering: categories.length,
          returned: transformedCategories.length
        },
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('[CATEGORIES API] Error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * POST /store/categories
 * Create a new category (admin only)
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { 
      name, 
      slug, 
      description, 
      image, 
      parent_id, 
      status = 'active', 
      sort_order = 999,
      seo,
      metadata 
    } = req.body;

    console.log(`[CATEGORIES API] POST request for tenant: ${tenantId}`);

    // Validate required fields
    if (!name) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'name is required',
        timestamp: new Date().toISOString()
      });
    }

    // Generate slug if not provided
    const categorySlug = slug || name.toLowerCase().replace(/[^a-z0-9]+/g, '-');

    // Check if category with same slug exists
    const existingCategories = mockCategories[tenantId] || [];
    const existingCategory = existingCategories.find(c => c.slug === categorySlug);
    
    if (existingCategory) {
      return res.status(409).json({
        error: 'Category already exists',
        message: 'A category with this slug already exists',
        timestamp: new Date().toISOString()
      });
    }

    // Create new category
    const newCategory = {
      id: `cat_${tenantId}_${Date.now()}`,
      name,
      slug: categorySlug,
      description: description || '',
      image: image || null,
      parent_id: parent_id || null,
      product_count: 0,
      status,
      sort_order: Number(sort_order),
      tenant_id: tenantId,
      seo: seo || {},
      metadata: metadata || {},
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Add to mock data
    if (!mockCategories[tenantId]) {
      mockCategories[tenantId] = [];
    }
    mockCategories[tenantId].push(newCategory);

    res.status(201).json({
      category: newCategory,
      message: 'Category created successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[CATEGORIES API] Error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}
