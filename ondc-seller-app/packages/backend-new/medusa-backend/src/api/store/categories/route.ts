import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";

// Mock category data for multi-tenant testing
const mockCategories = {
  'tenant-electronics-001': [
    {
      id: 'cat_electronics_001',
      name: 'Smartphones',
      slug: 'smartphones',
      description: 'Latest smartphones and mobile devices',
      image: '/images/categories/smartphones.jpg',
      parent_id: null,
      product_count: 15,
      status: 'active',
      sort_order: 1,
      tenant_id: 'tenant-electronics-001',
      seo: {
        title: 'Smartphones - Latest Mobile Devices',
        description: 'Shop the latest smartphones with best prices',
        keywords: ['smartphones', 'mobile', 'android', 'ios']
      },
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    },
    {
      id: 'cat_electronics_002',
      name: 'Laptops',
      slug: 'laptops',
      description: 'High-performance laptops and notebooks',
      image: '/images/categories/laptops.jpg',
      parent_id: null,
      product_count: 12,
      status: 'active',
      sort_order: 2,
      tenant_id: 'tenant-electronics-001',
      seo: {
        title: 'Laptops - High Performance Computing',
        description: 'Best laptops for work, gaming, and productivity',
        keywords: ['laptops', 'computers', 'gaming', 'work']
      },
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    },
    {
      id: 'cat_electronics_003',
      name: 'Audio',
      slug: 'audio',
      description: 'Headphones, speakers, and audio accessories',
      image: '/images/categories/audio.jpg',
      parent_id: null,
      product_count: 8,
      status: 'active',
      sort_order: 3,
      tenant_id: 'tenant-electronics-001',
      seo: {
        title: 'Audio - Headphones & Speakers',
        description: 'Premium audio equipment and accessories',
        keywords: ['headphones', 'speakers', 'audio', 'music']
      },
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    }
  ],
  'tenant-fashion-002': [
    {
      id: 'cat_fashion_001',
      name: 'Men\'s Clothing',
      slug: 'mens-clothing',
      description: 'Stylish clothing for men',
      image: '/images/categories/mens-clothing.jpg',
      parent_id: null,
      product_count: 20,
      status: 'active',
      sort_order: 1,
      tenant_id: 'tenant-fashion-002',
      seo: {
        title: 'Men\'s Clothing - Fashion & Style',
        description: 'Latest fashion trends for men',
        keywords: ['mens', 'clothing', 'fashion', 'style']
      },
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    },
    {
      id: 'cat_fashion_002',
      name: 'Women\'s Clothing',
      slug: 'womens-clothing',
      description: 'Elegant clothing for women',
      image: '/images/categories/womens-clothing.jpg',
      parent_id: null,
      product_count: 25,
      status: 'active',
      sort_order: 2,
      tenant_id: 'tenant-fashion-002',
      seo: {
        title: 'Women\'s Clothing - Fashion & Style',
        description: 'Latest fashion trends for women',
        keywords: ['womens', 'clothing', 'fashion', 'style']
      },
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    },
    {
      id: 'cat_fashion_003',
      name: 'Accessories',
      slug: 'accessories',
      description: 'Fashion accessories and jewelry',
      image: '/images/categories/accessories.jpg',
      parent_id: null,
      product_count: 15,
      status: 'active',
      sort_order: 3,
      tenant_id: 'tenant-fashion-002',
      seo: {
        title: 'Fashion Accessories - Complete Your Look',
        description: 'Stylish accessories and jewelry',
        keywords: ['accessories', 'jewelry', 'bags', 'fashion']
      },
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    }
  ],
  'default': [
    {
      id: 'cat_default_001',
      name: 'General',
      slug: 'general',
      description: 'General category',
      image: '/images/categories/general.jpg',
      parent_id: null,
      product_count: 5,
      status: 'active',
      sort_order: 1,
      tenant_id: 'default',
      seo: {
        title: 'General Products',
        description: 'General product category',
        keywords: ['general', 'products']
      },
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    }
  ]
};

/**
 * GET /store/categories
 * List categories for a tenant
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { 
      limit = 20, 
      offset = 0, 
      featured, 
      parentOnly, 
      parent_id, 
      status,
      includeProducts,
      includeChildren 
    } = req.query;

    console.log(`[CATEGORIES API] GET request for tenant: ${tenantId}`);

    // Get categories for the tenant
    let categories = mockCategories[tenantId] || [];

    // Apply filters
    if (featured === 'true') {
      categories = categories.filter(cat => cat.sort_order <= 3); // Featured = top 3
    }

    if (parentOnly === 'true') {
      categories = categories.filter(cat => !cat.parent_id);
    }

    if (parent_id) {
      categories = categories.filter(cat => cat.parent_id === parent_id);
    }

    if (status) {
      categories = categories.filter(cat => cat.status === status);
    }

    // Apply pagination
    const startIndex = Number(offset);
    const endIndex = startIndex + Number(limit);
    const paginatedCategories = categories.slice(startIndex, endIndex);

    // Add children if requested
    if (includeChildren === 'true') {
      paginatedCategories.forEach(category => {
        category.children = categories.filter(cat => cat.parent_id === category.id);
      });
    }

    res.status(200).json({
      categories: paginatedCategories,
      count: categories.length,
      offset: Number(offset),
      limit: Number(limit),
      tenant_id: tenantId,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[CATEGORIES API] Error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * POST /store/categories
 * Create a new category (admin only)
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { 
      name, 
      slug, 
      description, 
      image, 
      parent_id, 
      status = 'active', 
      sort_order = 999,
      seo,
      metadata 
    } = req.body;

    console.log(`[CATEGORIES API] POST request for tenant: ${tenantId}`);

    // Validate required fields
    if (!name) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'name is required',
        timestamp: new Date().toISOString()
      });
    }

    // Generate slug if not provided
    const categorySlug = slug || name.toLowerCase().replace(/[^a-z0-9]+/g, '-');

    // Check if category with same slug exists
    const existingCategories = mockCategories[tenantId] || [];
    const existingCategory = existingCategories.find(c => c.slug === categorySlug);
    
    if (existingCategory) {
      return res.status(409).json({
        error: 'Category already exists',
        message: 'A category with this slug already exists',
        timestamp: new Date().toISOString()
      });
    }

    // Create new category
    const newCategory = {
      id: `cat_${tenantId}_${Date.now()}`,
      name,
      slug: categorySlug,
      description: description || '',
      image: image || null,
      parent_id: parent_id || null,
      product_count: 0,
      status,
      sort_order: Number(sort_order),
      tenant_id: tenantId,
      seo: seo || {},
      metadata: metadata || {},
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Add to mock data
    if (!mockCategories[tenantId]) {
      mockCategories[tenantId] = [];
    }
    mockCategories[tenantId].push(newCategory);

    res.status(201).json({
      category: newCategory,
      message: 'Category created successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[CATEGORIES API] Error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}
