import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";

// Import mock data from parent route
const mockCategories = {
  'tenant-electronics-001': [
    {
      id: 'cat_electronics_001',
      name: 'Smartphones',
      slug: 'smartphones',
      description: 'Latest smartphones and mobile devices',
      image: '/images/categories/smartphones.jpg',
      parent_id: null,
      product_count: 15,
      status: 'active',
      sort_order: 1,
      tenant_id: 'tenant-electronics-001',
      seo: {
        title: 'Smartphones - Latest Mobile Devices',
        description: 'Shop the latest smartphones with best prices',
        keywords: ['smartphones', 'mobile', 'android', 'ios']
      },
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    },
    {
      id: 'cat_electronics_002',
      name: 'Laptops',
      slug: 'laptops',
      description: 'High-performance laptops and notebooks',
      image: '/images/categories/laptops.jpg',
      parent_id: null,
      product_count: 12,
      status: 'active',
      sort_order: 2,
      tenant_id: 'tenant-electronics-001',
      seo: {
        title: 'Laptops - High Performance Computing',
        description: 'Best laptops for work, gaming, and productivity',
        keywords: ['laptops', 'computers', 'gaming', 'work']
      },
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    },
    {
      id: 'cat_electronics_003',
      name: 'Audio',
      slug: 'audio',
      description: 'Headphones, speakers, and audio accessories',
      image: '/images/categories/audio.jpg',
      parent_id: null,
      product_count: 8,
      status: 'active',
      sort_order: 3,
      tenant_id: 'tenant-electronics-001',
      seo: {
        title: 'Audio - Headphones & Speakers',
        description: 'Premium audio equipment and accessories',
        keywords: ['headphones', 'speakers', 'audio', 'music']
      },
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    }
  ],
  'tenant-fashion-002': [
    {
      id: 'cat_fashion_001',
      name: 'Men\'s Clothing',
      slug: 'mens-clothing',
      description: 'Stylish clothing for men',
      image: '/images/categories/mens-clothing.jpg',
      parent_id: null,
      product_count: 20,
      status: 'active',
      sort_order: 1,
      tenant_id: 'tenant-fashion-002',
      seo: {
        title: 'Men\'s Clothing - Fashion & Style',
        description: 'Latest fashion trends for men',
        keywords: ['mens', 'clothing', 'fashion', 'style']
      },
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    },
    {
      id: 'cat_fashion_002',
      name: 'Women\'s Clothing',
      slug: 'womens-clothing',
      description: 'Elegant clothing for women',
      image: '/images/categories/womens-clothing.jpg',
      parent_id: null,
      product_count: 25,
      status: 'active',
      sort_order: 2,
      tenant_id: 'tenant-fashion-002',
      seo: {
        title: 'Women\'s Clothing - Fashion & Style',
        description: 'Latest fashion trends for women',
        keywords: ['womens', 'clothing', 'fashion', 'style']
      },
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    },
    {
      id: 'cat_fashion_003',
      name: 'Accessories',
      slug: 'accessories',
      description: 'Fashion accessories and jewelry',
      image: '/images/categories/accessories.jpg',
      parent_id: null,
      product_count: 15,
      status: 'active',
      sort_order: 3,
      tenant_id: 'tenant-fashion-002',
      seo: {
        title: 'Fashion Accessories - Complete Your Look',
        description: 'Stylish accessories and jewelry',
        keywords: ['accessories', 'jewelry', 'bags', 'fashion']
      },
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    }
  ],
  'default': []
};

/**
 * GET /store/categories/[id]
 * Get category details by ID or slug
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const categoryId = req.params.id;

    console.log(`[CATEGORIES API] GET category ${categoryId} for tenant: ${tenantId}`);

    // Get categories for the tenant
    const categories = mockCategories[tenantId] || [];
    const category = categories.find(c => c.id === categoryId || c.slug === categoryId);

    if (!category) {
      return res.status(404).json({
        error: 'Category not found',
        message: `Category with ID/slug ${categoryId} not found for tenant ${tenantId}`,
        timestamp: new Date().toISOString()
      });
    }

    // Add children categories
    const children = categories.filter(c => c.parent_id === category.id);
    const categoryWithChildren = {
      ...category,
      children
    };

    res.status(200).json({
      category: categoryWithChildren,
      tenant_id: tenantId,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[CATEGORIES API] Error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * PUT /store/categories/[id]
 * Update category details
 */
export async function PUT(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const categoryId = req.params.id;
    const { 
      name, 
      slug, 
      description, 
      image, 
      parent_id, 
      status, 
      sort_order,
      seo,
      metadata 
    } = req.body;

    console.log(`[CATEGORIES API] PUT category ${categoryId} for tenant: ${tenantId}`);

    // Get categories for the tenant
    const categories = mockCategories[tenantId] || [];
    const categoryIndex = categories.findIndex(c => c.id === categoryId);

    if (categoryIndex === -1) {
      return res.status(404).json({
        error: 'Category not found',
        message: `Category with ID ${categoryId} not found for tenant ${tenantId}`,
        timestamp: new Date().toISOString()
      });
    }

    // Check if new slug conflicts with existing categories
    if (slug && slug !== categories[categoryIndex].slug) {
      const existingCategory = categories.find(c => c.slug === slug && c.id !== categoryId);
      if (existingCategory) {
        return res.status(409).json({
          error: 'Slug already exists',
          message: 'A category with this slug already exists',
          timestamp: new Date().toISOString()
        });
      }
    }

    // Update category
    const updatedCategory = {
      ...categories[categoryIndex],
      name: name || categories[categoryIndex].name,
      slug: slug || categories[categoryIndex].slug,
      description: description !== undefined ? description : categories[categoryIndex].description,
      image: image !== undefined ? image : categories[categoryIndex].image,
      parent_id: parent_id !== undefined ? parent_id : categories[categoryIndex].parent_id,
      status: status || categories[categoryIndex].status,
      sort_order: sort_order !== undefined ? Number(sort_order) : categories[categoryIndex].sort_order,
      seo: seo ? { ...categories[categoryIndex].seo, ...seo } : categories[categoryIndex].seo,
      metadata: metadata ? { ...categories[categoryIndex].metadata, ...metadata } : categories[categoryIndex].metadata,
      updated_at: new Date().toISOString()
    };

    categories[categoryIndex] = updatedCategory;

    res.status(200).json({
      category: updatedCategory,
      message: 'Category updated successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[CATEGORIES API] Error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * DELETE /store/categories/[id]
 * Delete category (soft delete)
 */
export async function DELETE(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const categoryId = req.params.id;

    console.log(`[CATEGORIES API] DELETE category ${categoryId} for tenant: ${tenantId}`);

    // Get categories for the tenant
    const categories = mockCategories[tenantId] || [];
    const categoryIndex = categories.findIndex(c => c.id === categoryId);

    if (categoryIndex === -1) {
      return res.status(404).json({
        error: 'Category not found',
        message: `Category with ID ${categoryId} not found for tenant ${tenantId}`,
        timestamp: new Date().toISOString()
      });
    }

    // Check if category has children
    const hasChildren = categories.some(c => c.parent_id === categoryId);
    if (hasChildren) {
      return res.status(400).json({
        error: 'Cannot delete category',
        message: 'Category has subcategories. Please delete or move subcategories first.',
        timestamp: new Date().toISOString()
      });
    }

    // Remove category (in real implementation, this would be a soft delete)
    categories.splice(categoryIndex, 1);

    res.status(200).json({
      message: 'Category deleted successfully',
      category_id: categoryId,
      tenant_id: tenantId,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[CATEGORIES API] Error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}
