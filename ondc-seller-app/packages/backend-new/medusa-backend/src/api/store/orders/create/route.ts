import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { ContainerRegistrationKeys } from "@medusajs/framework/utils"

/**
 * Simple Order Creation from Cart
 * This endpoint creates an order from a cart with COD payment
 */
export async function POST(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { 
      cart_id, 
      customer_id,
      email,
      shipping_address,
      billing_address,
      payment_method = 'cod' // Default to Cash on Delivery
    } = req.body;

    const orderModuleService = req.scope.resolve("order");
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);

    logger.info(`[Order Creation] Creating order for tenant ${tenantId}, cart: ${cart_id}`);

    // Validate required fields
    if (!cart_id) {
      res.status(400).json({
        error: "Validation error",
        message: "cart_id is required"
      });
      return;
    }

    if (!shipping_address || !shipping_address.first_name || !shipping_address.address_1) {
      res.status(400).json({
        error: "Validation error", 
        message: "shipping_address with first_name and address_1 is required"
      });
      return;
    }

    // Get cart details using query system
    let cart;
    try {
      const { data: carts } = await query.graph({
        entity: "cart",
        fields: [
          "id",
          "currency_code",
          "region_id",
          "sales_channel_id",
          "customer_id",
          "email",
          "total",
          "subtotal",
          "items.*",
          "items.product.*",
          "items.variant.*",
          "region.*"
        ],
        filters: {
          id: cart_id
        }
      });

      if (!carts || carts.length === 0) {
        res.status(404).json({
          error: "Cart not found",
          message: `Cart with ID ${cart_id} not found`
        });
        return;
      }

      cart = carts[0];

      if (!cart.items || cart.items.length === 0) {
        res.status(400).json({
          error: "Empty cart",
          message: "Cart must have at least one item"
        });
        return;
      }

    } catch (error) {
      logger.error(`[Order Creation] Error retrieving cart: ${error}`);
      res.status(404).json({
        error: "Cart not found",
        message: `Cart with ID ${cart_id} not found`
      });
      return;
    }

    // Calculate totals
    const subtotal = cart.items.reduce((sum: number, item: any) => {
      return sum + (item.unit_price * item.quantity);
    }, 0);

    const tax_total = Math.round(subtotal * 0.18); // 18% GST
    const shipping_total = subtotal > 50000 ? 0 : 500; // Free shipping above ₹500
    const total = subtotal + tax_total + shipping_total;

    // Create order data
    const orderData = {
      currency_code: cart.region?.currency_code || "INR",
      email: email || customer_id || `customer-${Date.now()}@example.com`,
      customer_id: customer_id || null,
      region_id: cart.region_id,
      sales_channel_id: cart.sales_channel_id,
      status: "pending",
      metadata: {
        tenant_id: tenantId,
        payment_method: payment_method,
        cart_id: cart_id,
        created_via: "store_api",
        order_date: new Date().toISOString()
      },
      // Order items
      items: cart.items.map((item: any) => ({
        title: item.product_title || item.title || "Product",
        subtitle: item.variant_title || item.subtitle || "",
        quantity: item.quantity,
        unit_price: item.unit_price,
        variant_id: item.variant_id,
        product_id: item.product_id,
        metadata: {
          cart_item_id: item.id
        }
      })),
      // Shipping address
      shipping_address: {
        first_name: shipping_address.first_name,
        last_name: shipping_address.last_name || "",
        address_1: shipping_address.address_1,
        address_2: shipping_address.address_2 || "",
        city: shipping_address.city || "",
        postal_code: shipping_address.postal_code || "",
        province: shipping_address.province || shipping_address.state || "",
        country_code: shipping_address.country_code || "IN",
        phone: shipping_address.phone || ""
      },
      // Billing address (use shipping if not provided)
      billing_address: billing_address || shipping_address
    };

    logger.info(`[Order Creation] Creating order with data:`, {
      items: orderData.items.length,
      total: total,
      currency: orderData.currency_code
    });

    // Create the order
    const order = await orderModuleService.createOrders(orderData);
    
    logger.info(`[Order Creation] Order created successfully:`, order.id);

    // Note: Cart clearing can be implemented later if needed
    logger.info(`[Order Creation] Order created from cart ${cart_id}`);

    // Return success response
    res.status(201).json({
      success: true,
      order: {
        id: order.id,
        display_id: order.display_id || order.id,
        status: order.status,
        currency_code: order.currency_code,
        email: order.email,
        customer_id: order.customer_id,
        items: order.items || orderData.items,
        shipping_address: order.shipping_address || orderData.shipping_address,
        billing_address: order.billing_address || orderData.billing_address,
        subtotal: subtotal,
        tax_total: tax_total,
        shipping_total: shipping_total,
        total: total,
        payment_method: payment_method,
        created_at: order.created_at,
        metadata: order.metadata
      },
      message: "Order created successfully",
      tenant_id: tenantId,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);
    logger.error(`[Order Creation] Error creating order:`, error);
    
    res.status(500).json({
      error: "Order creation failed",
      message: error.message || "Failed to create order",
      details: process.env.NODE_ENV === "development" ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });
  }
}
