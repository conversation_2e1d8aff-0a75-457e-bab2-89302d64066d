import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";

// Mock order data (shared with parent route)
const mockOrders = {
  'tenant-electronics-001': [
    {
      id: 'order_electronics_001',
      display_id: 'ELE-001',
      customer_id: 'cus_electronics_001',
      tenant_id: 'tenant-electronics-001',
      email: '<EMAIL>',
      status: 'pending',
      fulfillment_status: 'not_fulfilled',
      payment_status: 'awaiting',
      currency_code: 'INR',
      items: [
        {
          id: 'item_001',
          order_id: 'order_electronics_001',
          product_id: 'prod_electronics_001',
          variant_id: 'variant_001',
          title: 'Smartphone Pro Max',
          description: 'Latest smartphone with advanced features',
          thumbnail: '/images/products/smartphone-pro.jpg',
          quantity: 1,
          unit_price: 79999,
          total: 79999
        }
      ],
      shipping_address: {
        first_name: '<PERSON>',
        last_name: '<PERSON><PERSON>',
        address_1: '123 Tech Street',
        city: 'Mumbai',
        postal_code: '400001',
        country_code: 'IN',
        phone: '+91-9876543210'
      },
      billing_address: {
        first_name: '<PERSON>',
        last_name: '<PERSON><PERSON>',
        address_1: '123 Tech Street',
        city: 'Mumbai',
        postal_code: '400001',
        country_code: 'IN',
        phone: '+91-9876543210'
      },
      subtotal: 79999,
      tax_total: 14399,
      shipping_total: 0,
      total: 94398,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    }
  ],
  'tenant-fashion-002': [
    {
      id: 'order_fashion_001',
      display_id: 'FAS-001',
      customer_id: 'cus_fashion_001',
      tenant_id: 'tenant-fashion-002',
      email: '<EMAIL>',
      status: 'completed',
      fulfillment_status: 'fulfilled',
      payment_status: 'captured',
      currency_code: 'INR',
      items: [
        {
          id: 'item_002',
          order_id: 'order_fashion_001',
          product_id: 'prod_fashion_001',
          variant_id: 'variant_002',
          title: 'Designer Dress',
          description: 'Elegant evening dress',
          thumbnail: '/images/products/designer-dress.jpg',
          quantity: 1,
          unit_price: 12999,
          total: 12999
        }
      ],
      shipping_address: {
        first_name: 'Alice',
        last_name: 'Johnson',
        address_1: '456 Fashion Avenue',
        city: 'Delhi',
        postal_code: '110001',
        country_code: 'IN',
        phone: '+91-9876543212'
      },
      billing_address: {
        first_name: 'Alice',
        last_name: 'Johnson',
        address_1: '456 Fashion Avenue',
        city: 'Delhi',
        postal_code: '110001',
        country_code: 'IN',
        phone: '+91-9876543212'
      },
      subtotal: 12999,
      tax_total: 2339,
      shipping_total: 500,
      total: 15838,
      created_at: '2024-01-02T00:00:00Z',
      updated_at: '2024-01-02T00:00:00Z'
    }
  ],
  'default': []
};

/**
 * GET /store/orders/[id]
 * Get order details by ID
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const orderId = req.params.id;

    console.log(`[ORDERS API] GET order ${orderId} for tenant: ${tenantId}`);

    // Get orders for the tenant
    const orders = mockOrders[tenantId] || [];
    const order = orders.find(o => o.id === orderId || o.display_id === orderId);

    if (!order) {
      return res.status(404).json({
        error: 'Order not found',
        message: `Order with ID ${orderId} not found for tenant ${tenantId}`,
        timestamp: new Date().toISOString()
      });
    }

    res.status(200).json({
      order,
      tenant_id: tenantId,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[ORDERS API] Error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * PUT /store/orders/[id]
 * Update order status
 */
export async function PUT(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const orderId = req.params.id;
    const { status, fulfillment_status, payment_status } = req.body;

    console.log(`[ORDERS API] PUT order ${orderId} for tenant: ${tenantId}`);

    // Get orders for the tenant
    const orders = mockOrders[tenantId] || [];
    const orderIndex = orders.findIndex(o => o.id === orderId || o.display_id === orderId);

    if (orderIndex === -1) {
      return res.status(404).json({
        error: 'Order not found',
        message: `Order with ID ${orderId} not found for tenant ${tenantId}`,
        timestamp: new Date().toISOString()
      });
    }

    // Update order
    const updatedOrder = {
      ...orders[orderIndex],
      status: status || orders[orderIndex].status,
      fulfillment_status: fulfillment_status || orders[orderIndex].fulfillment_status,
      payment_status: payment_status || orders[orderIndex].payment_status,
      updated_at: new Date().toISOString()
    };

    orders[orderIndex] = updatedOrder;

    res.status(200).json({
      order: updatedOrder,
      message: 'Order updated successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[ORDERS API] Error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}
