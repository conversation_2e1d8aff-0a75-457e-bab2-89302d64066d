import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { ContainerRegistrationKeys } from "@medusajs/framework/utils"

/**
 * Simple Orders Retrieval
 * This endpoint retrieves orders without complex authentication
 */
export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  try {
    const { email, limit = 10, offset = 0 } = req.query
    const orderModuleService = req.scope.resolve("order")
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER)

    logger.info(`[Orders Retrieval] Getting orders for email: ${email}`)

    // Build query filters
    const filters: any = {}
    if (email) {
      filters.email = email
    }

    // Get orders
    const orders = await orderModuleService.listOrders(filters, {
      relations: ["items", "shipping_address", "billing_address"],
      take: parseInt(limit as string),
      skip: parseInt(offset as string),
      order: { created_at: "DESC" }
    })

    logger.info(`[Orders Retrieval] Found ${orders.length} orders`)

    // Format orders for response
    const formattedOrders = orders.map((order: any) => ({
      id: order.id,
      cart_id: order.cart_id,
      email: order.email,
      total: order.total,
      subtotal: order.subtotal,
      tax_total: order.tax_total,
      shipping_total: order.shipping_total,
      currency_code: order.currency_code,
      payment_status: order.payment_status,
      fulfillment_status: order.fulfillment_status,
      payment_method: order.payment_method || order.metadata?.payment_method,
      created_at: order.created_at,
      updated_at: order.updated_at,
      items: order.items?.map((item: any) => ({
        id: item.id,
        title: item.title,
        quantity: item.quantity,
        unit_price: item.unit_price,
        total: item.total,
        variant_id: item.variant_id,
        product_id: item.product_id,
        metadata: item.metadata
      })) || [],
      shipping_address: order.shipping_address,
      billing_address: order.billing_address,
      metadata: order.metadata
    }))

    // Return success response
    res.status(200).json({
      success: true,
      orders: formattedOrders,
      count: formattedOrders.length,
      total_count: formattedOrders.length, // In a real implementation, you'd get total count separately
      limit: parseInt(limit as string),
      offset: parseInt(offset as string)
    })

  } catch (error: any) {
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER)
    logger.error(`[Orders Retrieval] Error getting orders:`, error)
    
    res.status(500).json({
      error: "Orders retrieval failed",
      message: error.message || "Failed to retrieve orders",
      details: process.env.NODE_ENV === "development" ? error.stack : undefined
    })
  }
}

/**
 * Get Single Order by ID
 */
export async function POST(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  try {
    const { order_id } = req.body
    const orderModuleService = req.scope.resolve("order")
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER)

    if (!order_id) {
      res.status(400).json({
        error: "Validation error",
        message: "order_id is required"
      })
      return
    }

    logger.info(`[Order Retrieval] Getting order: ${order_id}`)

    // Get single order
    const order = await orderModuleService.retrieveOrder(order_id, {
      relations: ["items", "shipping_address", "billing_address"]
    })

    if (!order) {
      res.status(404).json({
        error: "Order not found",
        message: `Order with ID ${order_id} not found`
      })
      return
    }

    // Format order for response
    const formattedOrder = {
      id: order.id,
      cart_id: order.cart_id,
      email: order.email,
      total: order.total,
      subtotal: order.subtotal,
      tax_total: order.tax_total,
      shipping_total: order.shipping_total,
      currency_code: order.currency_code,
      payment_status: order.payment_status,
      fulfillment_status: order.fulfillment_status,
      payment_method: order.payment_method || order.metadata?.payment_method,
      created_at: order.created_at,
      updated_at: order.updated_at,
      items: order.items?.map((item: any) => ({
        id: item.id,
        title: item.title,
        quantity: item.quantity,
        unit_price: item.unit_price,
        total: item.total,
        variant_id: item.variant_id,
        product_id: item.product_id,
        metadata: item.metadata
      })) || [],
      shipping_address: order.shipping_address,
      billing_address: order.billing_address,
      metadata: order.metadata
    }

    // Return success response
    res.status(200).json({
      success: true,
      order: formattedOrder
    })

  } catch (error: any) {
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER)
    logger.error(`[Order Retrieval] Error getting order:`, error)
    
    res.status(500).json({
      error: "Order retrieval failed",
      message: error.message || "Failed to retrieve order",
      details: process.env.NODE_ENV === "development" ? error.stack : undefined
    })
  }
}
