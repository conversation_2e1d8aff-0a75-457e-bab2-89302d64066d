import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";
import { ContainerRegistrationKeys } from "@medusajs/framework/utils";

// Tenant-specific order filtering configuration
const TENANT_ORDER_CONFIG = {
  'tenant-electronics-001': {
    orderFilter: (orders: any[]) => {
      return orders.filter(order =>
        order.metadata?.tenant_id === 'tenant-electronics-001'
      )
    }
  },
  'tenant-fashion-002': {
    orderFilter: (orders: any[]) => {
      return orders.filter(order =>
        order.metadata?.tenant_id === 'tenant-fashion-002'
      )
    }
  },
  'default': {
    orderFilter: (orders: any[]) => orders // Return all orders for default tenant
  }
}

// Legacy mock data (will be replaced with real data)
const mockOrders = {
  'tenant-electronics-001': [
    {
      id: 'order_electronics_001',
      display_id: 'ELE-001',
      customer_id: 'cus_electronics_001',
      tenant_id: 'tenant-electronics-001',
      email: '<EMAIL>',
      status: 'pending',
      fulfillment_status: 'not_fulfilled',
      payment_status: 'awaiting',
      currency_code: 'INR',
      items: [
        {
          id: 'item_001',
          order_id: 'order_electronics_001',
          product_id: 'prod_electronics_001',
          variant_id: 'variant_001',
          title: 'Smartphone Pro Max',
          description: 'Latest smartphone with advanced features',
          thumbnail: '/images/products/smartphone-pro.jpg',
          quantity: 1,
          unit_price: 79999,
          total: 79999
        }
      ],
      shipping_address: {
        first_name: 'John',
        last_name: 'Doe',
        address_1: '123 Tech Street',
        city: 'Mumbai',
        postal_code: '400001',
        country_code: 'IN',
        phone: '+91-9876543210'
      },
      billing_address: {
        first_name: 'John',
        last_name: 'Doe',
        address_1: '123 Tech Street',
        city: 'Mumbai',
        postal_code: '400001',
        country_code: 'IN',
        phone: '+91-9876543210'
      },
      subtotal: 79999,
      tax_total: 14399,
      shipping_total: 0,
      total: 94398,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    }
  ],
  'tenant-fashion-002': [
    {
      id: 'order_fashion_001',
      display_id: 'FAS-001',
      customer_id: 'cus_fashion_001',
      tenant_id: 'tenant-fashion-002',
      email: '<EMAIL>',
      status: 'completed',
      fulfillment_status: 'fulfilled',
      payment_status: 'captured',
      currency_code: 'INR',
      items: [
        {
          id: 'item_002',
          order_id: 'order_fashion_001',
          product_id: 'prod_fashion_001',
          variant_id: 'variant_002',
          title: 'Designer Dress',
          description: 'Elegant evening dress',
          thumbnail: '/images/products/designer-dress.jpg',
          quantity: 1,
          unit_price: 12999,
          total: 12999
        }
      ],
      shipping_address: {
        first_name: 'Alice',
        last_name: 'Johnson',
        address_1: '456 Fashion Avenue',
        city: 'Delhi',
        postal_code: '110001',
        country_code: 'IN',
        phone: '+91-9876543212'
      },
      billing_address: {
        first_name: 'Alice',
        last_name: 'Johnson',
        address_1: '456 Fashion Avenue',
        city: 'Delhi',
        postal_code: '110001',
        country_code: 'IN',
        phone: '+91-9876543212'
      },
      subtotal: 12999,
      tax_total: 2339,
      shipping_total: 500,
      total: 15838,
      created_at: '2024-01-02T00:00:00Z',
      updated_at: '2024-01-02T00:00:00Z'
    }
  ],
  'default': []
};

/**
 * GET /store/orders
 * List orders for a tenant using real Medusa orders with tenant filtering
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { customer_id, status, limit = 10, offset = 0, id } = req.query;
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);

    logger.info(`[ORDERS API] GET request for tenant: ${tenantId}`);

    // If ID is provided, get single order
    if (id && typeof id === 'string') {
      try {
        const { data: orders } = await query.graph({
          entity: "order",
          fields: [
            "id",
            "display_id",
            "status",
            "currency_code",
            "email",
            "customer_id",
            "total",
            "subtotal",
            "tax_total",
            "shipping_total",
            "created_at",
            "updated_at",
            "metadata",
            "items.*",
            "items.product.*",
            "items.variant.*",
            "shipping_address.*",
            "billing_address.*"
          ],
          filters: {
            id: id
          }
        });

        if (!orders || orders.length === 0) {
          return res.status(404).json({
            error: 'Order not found',
            message: `Order with ID ${id} not found`,
            timestamp: new Date().toISOString()
          });
        }

        const order = orders[0];

        // Check tenant access
        if (order.metadata?.tenant_id && order.metadata.tenant_id !== tenantId) {
          return res.status(404).json({
            error: 'Order not found',
            message: `Order with ID ${id} not found`,
            timestamp: new Date().toISOString()
          });
        }

        return res.status(200).json({
          order: order,
          tenant_id: tenantId,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        logger.error(`[ORDERS API] Error retrieving order ${id}:`, error);
        return res.status(404).json({
          error: 'Order not found',
          message: `Order with ID ${id} not found`,
          timestamp: new Date().toISOString()
        });
      }
    }

    // Build filters for listing orders
    const filters: any = {};

    if (customer_id && typeof customer_id === 'string') {
      filters.customer_id = customer_id;
    }

    if (status && typeof status === 'string') {
      filters.status = status;
    }

    // Get orders with relations (fetch more to allow for tenant filtering)
    const { data: allOrders } = await query.graph({
      entity: "order",
      fields: [
        "id",
        "display_id",
        "status",
        "currency_code",
        "email",
        "customer_id",
        "total",
        "subtotal",
        "tax_total",
        "shipping_total",
        "created_at",
        "updated_at",
        "metadata",
        "items.*",
        "shipping_address.*",
        "billing_address.*"
      ],
      filters,
      pagination: {
        skip: 0,
        take: 100 // Fetch more to allow for tenant filtering
      }
    });

    // Apply tenant-specific filtering
    const tenantConfig = TENANT_ORDER_CONFIG[tenantId] || TENANT_ORDER_CONFIG['default'];
    const tenantFilteredOrders = tenantConfig.orderFilter(allOrders);

    // Apply pagination after tenant filtering
    const startIndex = Number(offset);
    const endIndex = startIndex + Number(limit);
    const orders = tenantFilteredOrders.slice(startIndex, endIndex);

    logger.info(`[ORDERS API] Found ${orders.length}/${tenantFilteredOrders.length} orders for tenant: ${tenantId}`);

    res.status(200).json({
      orders: orders,
      count: orders.length,
      offset: Number(offset),
      limit: Number(limit),
      total: tenantFilteredOrders.length,
      metadata: {
        tenantId,
        filtering: {
          totalBeforeFiltering: allOrders.length,
          totalAfterFiltering: tenantFilteredOrders.length,
          returned: orders.length
        },
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);
    logger.error('[ORDERS API] Error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      details: process.env.NODE_ENV === "development" ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * POST /store/orders
 * Create a new order from cart
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { 
      cart_id, 
      customer_id, 
      email, 
      shipping_address, 
      billing_address,
      payment_method 
    } = req.body;

    console.log(`[ORDERS API] POST request for tenant: ${tenantId}`);

    // Validate required fields
    if (!cart_id || !email || !shipping_address) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'cart_id, email, and shipping_address are required',
        timestamp: new Date().toISOString()
      });
    }

    // Create new order
    const newOrder = {
      id: `order_${tenantId}_${Date.now()}`,
      display_id: `${tenantId.toUpperCase().slice(-3)}-${String(Date.now()).slice(-3)}`,
      customer_id: customer_id || null,
      tenant_id: tenantId,
      email,
      status: 'pending',
      fulfillment_status: 'not_fulfilled',
      payment_status: 'awaiting',
      currency_code: 'INR',
      items: [], // In real implementation, copy from cart
      shipping_address,
      billing_address: billing_address || shipping_address,
      subtotal: 0,
      tax_total: 0,
      shipping_total: 500,
      total: 500,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Add to mock data
    if (!mockOrders[tenantId]) {
      mockOrders[tenantId] = [];
    }
    mockOrders[tenantId].push(newOrder);

    res.status(201).json({
      order: newOrder,
      message: 'Order created successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[ORDERS API] Error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}
