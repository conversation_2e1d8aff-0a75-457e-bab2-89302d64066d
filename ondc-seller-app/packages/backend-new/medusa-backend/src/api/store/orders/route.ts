import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";

// Mock order data for multi-tenant testing
const mockOrders = {
  'tenant-electronics-001': [
    {
      id: 'order_electronics_001',
      display_id: 'ELE-001',
      customer_id: 'cus_electronics_001',
      tenant_id: 'tenant-electronics-001',
      email: '<EMAIL>',
      status: 'pending',
      fulfillment_status: 'not_fulfilled',
      payment_status: 'awaiting',
      currency_code: 'INR',
      items: [
        {
          id: 'item_001',
          order_id: 'order_electronics_001',
          product_id: 'prod_electronics_001',
          variant_id: 'variant_001',
          title: 'Smartphone Pro Max',
          description: 'Latest smartphone with advanced features',
          thumbnail: '/images/products/smartphone-pro.jpg',
          quantity: 1,
          unit_price: 79999,
          total: 79999
        }
      ],
      shipping_address: {
        first_name: 'John',
        last_name: '<PERSON><PERSON>',
        address_1: '123 Tech Street',
        city: 'Mumbai',
        postal_code: '400001',
        country_code: 'IN',
        phone: '+91-9876543210'
      },
      billing_address: {
        first_name: '<PERSON>',
        last_name: '<PERSON><PERSON>',
        address_1: '123 Tech Street',
        city: 'Mumbai',
        postal_code: '400001',
        country_code: 'IN',
        phone: '+91-9876543210'
      },
      subtotal: 79999,
      tax_total: 14399,
      shipping_total: 0,
      total: 94398,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    }
  ],
  'tenant-fashion-002': [
    {
      id: 'order_fashion_001',
      display_id: 'FAS-001',
      customer_id: 'cus_fashion_001',
      tenant_id: 'tenant-fashion-002',
      email: '<EMAIL>',
      status: 'completed',
      fulfillment_status: 'fulfilled',
      payment_status: 'captured',
      currency_code: 'INR',
      items: [
        {
          id: 'item_002',
          order_id: 'order_fashion_001',
          product_id: 'prod_fashion_001',
          variant_id: 'variant_002',
          title: 'Designer Dress',
          description: 'Elegant evening dress',
          thumbnail: '/images/products/designer-dress.jpg',
          quantity: 1,
          unit_price: 12999,
          total: 12999
        }
      ],
      shipping_address: {
        first_name: 'Alice',
        last_name: 'Johnson',
        address_1: '456 Fashion Avenue',
        city: 'Delhi',
        postal_code: '110001',
        country_code: 'IN',
        phone: '+91-9876543212'
      },
      billing_address: {
        first_name: 'Alice',
        last_name: 'Johnson',
        address_1: '456 Fashion Avenue',
        city: 'Delhi',
        postal_code: '110001',
        country_code: 'IN',
        phone: '+91-9876543212'
      },
      subtotal: 12999,
      tax_total: 2339,
      shipping_total: 500,
      total: 15838,
      created_at: '2024-01-02T00:00:00Z',
      updated_at: '2024-01-02T00:00:00Z'
    }
  ],
  'default': []
};

/**
 * GET /store/orders
 * List orders for a tenant
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { customer_id, status, limit = 10, offset = 0 } = req.query;

    console.log(`[ORDERS API] GET request for tenant: ${tenantId}`);

    // Get orders for the tenant
    let orders = mockOrders[tenantId] || [];

    // Filter by customer if provided
    if (customer_id && typeof customer_id === 'string') {
      orders = orders.filter(order => order.customer_id === customer_id);
    }

    // Filter by status if provided
    if (status && typeof status === 'string') {
      orders = orders.filter(order => order.status === status);
    }

    // Apply pagination
    const startIndex = Number(offset);
    const endIndex = startIndex + Number(limit);
    const paginatedOrders = orders.slice(startIndex, endIndex);

    res.status(200).json({
      orders: paginatedOrders,
      count: orders.length,
      offset: Number(offset),
      limit: Number(limit),
      tenant_id: tenantId,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[ORDERS API] Error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * POST /store/orders
 * Create a new order from cart
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { 
      cart_id, 
      customer_id, 
      email, 
      shipping_address, 
      billing_address,
      payment_method 
    } = req.body;

    console.log(`[ORDERS API] POST request for tenant: ${tenantId}`);

    // Validate required fields
    if (!cart_id || !email || !shipping_address) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'cart_id, email, and shipping_address are required',
        timestamp: new Date().toISOString()
      });
    }

    // Create new order
    const newOrder = {
      id: `order_${tenantId}_${Date.now()}`,
      display_id: `${tenantId.toUpperCase().slice(-3)}-${String(Date.now()).slice(-3)}`,
      customer_id: customer_id || null,
      tenant_id: tenantId,
      email,
      status: 'pending',
      fulfillment_status: 'not_fulfilled',
      payment_status: 'awaiting',
      currency_code: 'INR',
      items: [], // In real implementation, copy from cart
      shipping_address,
      billing_address: billing_address || shipping_address,
      subtotal: 0,
      tax_total: 0,
      shipping_total: 500,
      total: 500,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Add to mock data
    if (!mockOrders[tenantId]) {
      mockOrders[tenantId] = [];
    }
    mockOrders[tenantId].push(newOrder);

    res.status(201).json({
      order: newOrder,
      message: 'Order created successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[ORDERS API] Error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}
