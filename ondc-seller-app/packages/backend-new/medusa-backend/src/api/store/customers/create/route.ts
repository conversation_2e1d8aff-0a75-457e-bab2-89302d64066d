import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { ContainerRegistrationKeys } from "@medusajs/framework/utils"

/**
 * Simple Customer Creation
 * This endpoint creates a customer without authentication requirements
 */
export async function POST(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { email, first_name, last_name, phone, password } = req.body
    const customerModuleService = req.scope.resolve("customer")
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER)

    logger.info(`[Customer Creation] Creating customer for tenant ${tenantId}: ${email}`)

    // Validate required fields
    if (!email || !first_name || !last_name) {
      res.status(400).json({
        error: "Validation error",
        message: "Email, first_name, and last_name are required"
      })
      return
    }

    // Check if customer already exists
    try {
      const existingCustomers = await customerModuleService.listCustomers({
        email: email.toLowerCase().trim()
      })
      
      if (existingCustomers && existingCustomers.length > 0) {
        res.status(409).json({
          error: "Customer exists",
          message: "Customer with this email already exists",
          customer: {
            id: existingCustomers[0].id,
            email: existingCustomers[0].email,
            first_name: existingCustomers[0].first_name,
            last_name: existingCustomers[0].last_name
          }
        })
        return
      }
    } catch (error) {
      // If listing fails, continue with creation
      logger.warn(`[Customer Creation] Could not check existing customer: ${error}`)
    }

    // Create customer data
    const customerData = {
      email: email.toLowerCase().trim(),
      first_name: first_name.trim(),
      last_name: last_name.trim(),
      phone: phone || null,
      has_account: !!password,
      metadata: {
        tenant_id: tenantId,
        registration_method: "store_api",
        created_via: "customer_creation",
        registration_date: new Date().toISOString(),
        has_password: !!password
      }
    }

    logger.info(`[Customer Creation] Creating customer with data:`, customerData)

    // Create the customer
    const customer = await customerModuleService.createCustomers(customerData)
    
    logger.info(`[Customer Creation] Customer created successfully:`, customer.id)

    // Return success response
    res.status(201).json({
      success: true,
      customer: {
        id: customer.id,
        email: customer.email,
        first_name: customer.first_name,
        last_name: customer.last_name,
        phone: customer.phone,
        has_account: customer.has_account,
        created_at: customer.created_at,
        updated_at: customer.updated_at,
        metadata: customer.metadata
      },
      message: "Customer created successfully",
      tenant_id: tenantId,
      timestamp: new Date().toISOString()
    })

  } catch (error: any) {
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER)
    logger.error(`[Customer Creation] Error creating customer:`, error)
    
    res.status(500).json({
      error: "Customer creation failed",
      message: error.message || "Failed to create customer",
      details: process.env.NODE_ENV === "development" ? error.stack : undefined,
      timestamp: new Date().toISOString()
    })
  }
}
