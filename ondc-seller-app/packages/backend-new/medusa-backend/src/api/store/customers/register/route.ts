import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { ContainerRegistrationKeys } from "@medusajs/framework/utils"

/**
 * Simple Customer Registration
 * This endpoint creates a customer without complex authentication flows
 */
export async function POST(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  try {
    const { email, first_name, last_name, phone } = req.body
    const customerModuleService = req.scope.resolve("customer")
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER)

    logger.info(`[Customer Registration] Creating customer: ${email}`)

    // Validate required fields
    if (!email) {
      res.status(400).json({
        error: "Validation error",
        message: "Email is required"
      })
      return
    }

    // Check if customer already exists
    try {
      const existingCustomers = await customerModuleService.listCustomers({
        email: email
      })
      
      if (existingCustomers && existingCustomers.length > 0) {
        res.status(409).json({
          error: "Customer exists",
          message: "Customer with this email already exists",
          customer: {
            id: existingCustomers[0].id,
            email: existingCustomers[0].email,
            first_name: existingCustomers[0].first_name,
            last_name: existingCustomers[0].last_name
          }
        })
        return
      }
    } catch (error) {
      // If listing fails, continue with creation
      logger.warn(`[Customer Registration] Could not check existing customer: ${error}`)
    }

    // Create customer data
    const customerData = {
      email: email.toLowerCase().trim(),
      first_name: first_name || "Guest",
      last_name: last_name || "Customer",
      phone: phone || null,
      metadata: {
        registration_method: "store_api",
        created_via: "simple_registration",
        registration_date: new Date().toISOString()
      }
    }

    logger.info(`[Customer Registration] Creating customer with data:`, customerData)

    // Create the customer
    const customer = await customerModuleService.createCustomers(customerData)
    
    logger.info(`[Customer Registration] Customer created successfully:`, customer.id)

    // Return success response
    res.status(201).json({
      success: true,
      customer: {
        id: customer.id,
        email: customer.email,
        first_name: customer.first_name,
        last_name: customer.last_name,
        phone: customer.phone,
        created_at: customer.created_at,
        metadata: customer.metadata
      },
      message: "Customer registered successfully"
    })

  } catch (error: any) {
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER)
    logger.error(`[Customer Registration] Error creating customer:`, error)
    
    res.status(500).json({
      error: "Registration failed",
      message: error.message || "Failed to register customer",
      details: process.env.NODE_ENV === "development" ? error.stack : undefined
    })
  }
}
