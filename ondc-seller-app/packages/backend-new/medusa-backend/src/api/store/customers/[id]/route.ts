import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";

// Import mock data from parent route
const mockCustomers = {
  'tenant-electronics-001': [
    {
      id: 'cus_electronics_001',
      email: '<EMAIL>',
      first_name: '<PERSON>',
      last_name: '<PERSON><PERSON>',
      phone: '+91-**********',
      has_account: true,
      tenant_id: 'tenant-electronics-001',
      addresses: [
        {
          id: 'addr_001',
          first_name: '<PERSON>',
          last_name: '<PERSON><PERSON>',
          address_1: '123 Tech Street',
          city: 'Mumbai',
          postal_code: '400001',
          country_code: 'IN',
          phone: '+91-**********'
        }
      ],
      orders: [],
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    },
    {
      id: 'cus_electronics_002',
      email: '<EMAIL>',
      first_name: '<PERSON>',
      last_name: '<PERSON>',
      phone: '+91-**********',
      has_account: true,
      tenant_id: 'tenant-electronics-001',
      addresses: [],
      orders: [],
      created_at: '2024-01-02T00:00:00Z',
      updated_at: '2024-01-02T00:00:00Z'
    },
    {
      id: 'cust_electronics_003',
      company_name: null,
      first_name: 'Mike',
      last_name: 'Wilson',
      email: '<EMAIL>',
      phone: '+91-**********',
      metadata: null,
      has_account: true,
      tenant_id: 'tenant-electronics-001',
      addresses: [],
      orders: [],
      created_by: null,
      created_at: '2025-07-02T08:06:09.302Z',
      updated_at: '2025-07-02T08:06:09.302Z',
      deleted_at: null
    }
  ],
  'tenant-fashion-002': [
    {
      id: 'cus_fashion_001',
      email: '<EMAIL>',
      first_name: 'Alice',
      last_name: 'Johnson',
      phone: '+91-**********',
      has_account: true,
      tenant_id: 'tenant-fashion-002',
      addresses: [
        {
          id: 'addr_002',
          first_name: 'Alice',
          last_name: 'Johnson',
          address_1: '456 Fashion Avenue',
          city: 'Delhi',
          postal_code: '110001',
          country_code: 'IN',
          phone: '+91-**********'
        }
      ],
      orders: [],
      created_at: '2024-01-03T00:00:00Z',
      updated_at: '2024-01-03T00:00:00Z'
    }
  ],
  'default': [
    {
      id: 'cus_default_001',
      email: '<EMAIL>',
      first_name: 'Demo',
      last_name: 'User',
      phone: '+91-**********',
      has_account: true,
      tenant_id: 'default',
      addresses: [],
      orders: [],
      created_at: '2024-01-04T00:00:00Z',
      updated_at: '2024-01-04T00:00:00Z'
    }
  ]
};

/**
 * GET /store/customers/[id]
 * Get customer details by ID
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const customerId = req.params.id;

    console.log(`[CUSTOMERS API] GET customer ${customerId} for tenant: ${tenantId}`);

    // Get customers for the tenant
    const customers = mockCustomers[tenantId] || [];
    const customer = customers.find(c => c.id === customerId);

    if (!customer) {
      return res.status(404).json({
        error: 'Customer not found',
        message: `Customer with ID ${customerId} not found for tenant ${tenantId}`,
        timestamp: new Date().toISOString()
      });
    }

    res.status(200).json({
      customer,
      tenant_id: tenantId,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[CUSTOMERS API] Error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * PUT /store/customers/[id]
 * Update customer details
 */
export async function PUT(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const customerId = req.params.id;
    const { email, first_name, last_name, phone } = req.body;

    console.log(`[CUSTOMERS API] PUT customer ${customerId} for tenant: ${tenantId}`);

    // Get customers for the tenant
    const customers = mockCustomers[tenantId] || [];
    const customerIndex = customers.findIndex(c => c.id === customerId);

    if (customerIndex === -1) {
      return res.status(404).json({
        error: 'Customer not found',
        message: `Customer with ID ${customerId} not found for tenant ${tenantId}`,
        timestamp: new Date().toISOString()
      });
    }

    // Update customer
    const updatedCustomer = {
      ...customers[customerIndex],
      email: email || customers[customerIndex].email,
      first_name: first_name || customers[customerIndex].first_name,
      last_name: last_name || customers[customerIndex].last_name,
      phone: phone !== undefined ? phone : customers[customerIndex].phone,
      updated_at: new Date().toISOString()
    };

    customers[customerIndex] = updatedCustomer;

    res.status(200).json({
      customer: updatedCustomer,
      message: 'Customer updated successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[CUSTOMERS API] Error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * DELETE /store/customers/[id]
 * Delete customer (soft delete)
 */
export async function DELETE(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const customerId = req.params.id;

    console.log(`[CUSTOMERS API] DELETE customer ${customerId} for tenant: ${tenantId}`);

    // Get customers for the tenant
    const customers = mockCustomers[tenantId] || [];
    const customerIndex = customers.findIndex(c => c.id === customerId);

    if (customerIndex === -1) {
      return res.status(404).json({
        error: 'Customer not found',
        message: `Customer with ID ${customerId} not found for tenant ${tenantId}`,
        timestamp: new Date().toISOString()
      });
    }

    // Remove customer (in real implementation, this would be a soft delete)
    customers.splice(customerIndex, 1);

    res.status(200).json({
      message: 'Customer deleted successfully',
      customer_id: customerId,
      tenant_id: tenantId,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[CUSTOMERS API] Error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}
