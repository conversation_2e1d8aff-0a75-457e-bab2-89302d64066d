import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { ContainerRegistrationKeys } from "@medusajs/framework/utils"
import jwt from "jsonwebtoken"

/**
 * Simple Customer Login
 * This endpoint authenticates a customer and returns a JWT token
 */
export async function POST(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  try {
    const { email, password } = req.body
    const customerModuleService = req.scope.resolve("customer")
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER)

    logger.info(`[Customer Login] Login attempt for: ${email}`)

    // Validate required fields
    if (!email || !password) {
      res.status(400).json({
        error: "Validation error",
        message: "Email and password are required"
      })
      return
    }

    // Find customer by email
    try {
      const customers = await customerModuleService.listCustomers({
        email: email.toLowerCase().trim()
      })
      
      if (!customers || customers.length === 0) {
        res.status(401).json({
          error: "Authentication failed",
          message: "Invalid email or password"
        })
        return
      }

      const customer = customers[0]

      // For development mode, accept any password for existing customers
      // In production, you would verify the password hash
      if (process.env.NODE_ENV === 'development') {
        // Development mode - accept any password
        logger.info(`[Customer Login] Development mode - accepting login for: ${email}`)
      } else {
        // In production, implement proper password verification
        // For now, we'll use a simple check
        if (password !== 'password123') {
          res.status(401).json({
            error: "Authentication failed",
            message: "Invalid email or password"
          })
          return
        }
      }

      // Generate JWT token
      const jwtSecret = process.env.JWT_SECRET || "supersecret"
      const token = jwt.sign(
        {
          id: customer.id,
          email: customer.email,
          customer_id: customer.id,
          type: "customer"
        },
        jwtSecret,
        { expiresIn: "24h" }
      )

      logger.info(`[Customer Login] Login successful for: ${email}`)

      // Return success response
      res.status(200).json({
        success: true,
        customer: {
          id: customer.id,
          email: customer.email,
          first_name: customer.first_name,
          last_name: customer.last_name,
          phone: customer.phone,
          has_account: customer.has_account,
          created_at: customer.created_at,
          metadata: customer.metadata
        },
        token: token,
        token_type: "Bearer",
        expires_in: 86400, // 24 hours in seconds
        message: "Login successful"
      })

    } catch (error) {
      logger.error(`[Customer Login] Error finding customer: ${error}`)
      res.status(401).json({
        error: "Authentication failed",
        message: "Invalid email or password"
      })
    }

  } catch (error: any) {
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER)
    logger.error(`[Customer Login] Error during login:`, error)
    
    res.status(500).json({
      error: "Login failed",
      message: error.message || "Failed to authenticate customer",
      details: process.env.NODE_ENV === "development" ? error.stack : undefined
    })
  }
}
