import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";

// Real database customer data for multi-tenant testing
const mockCustomers = {
  'tenant-electronics-001': [
    {
      id: 'cus_electronics_001',
      email: '<EMAIL>',
      first_name: '<PERSON>',
      last_name: '<PERSON><PERSON>',
      phone: '+91-**********',
      has_account: true,
      tenant_id: 'tenant-electronics-001',
      addresses: [
        {
          id: 'addr_001',
          first_name: '<PERSON>',
          last_name: '<PERSON><PERSON>',
          address_1: '123 Tech Street',
          city: 'Mumbai',
          postal_code: '400001',
          country_code: 'IN',
          phone: '+91-**********'
        }
      ],
      orders: [],
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    },
    {
      id: 'cus_electronics_002',
      email: '<EMAIL>',
      first_name: '<PERSON>',
      last_name: '<PERSON>',
      phone: '+91-**********',
      has_account: true,
      tenant_id: 'tenant-electronics-001',
      addresses: [],
      orders: [],
      created_at: '2024-01-02T00:00:00Z',
      updated_at: '2024-01-02T00:00:00Z'
    },
    {
      id: 'cust_electronics_003',
      company_name: null,
      first_name: 'Mike',
      last_name: 'Wilson',
      email: '<EMAIL>',
      phone: '+91-**********',
      metadata: null,
      has_account: true,
      tenant_id: 'tenant-electronics-001',
      addresses: [],
      orders: [],
      created_by: null,
      created_at: '2025-07-02T08:06:09.302Z',
      updated_at: '2025-07-02T08:06:09.302Z',
      deleted_at: null
    }
  ],
  'tenant-fashion-002': [
    {
      id: 'cus_fashion_001',
      email: '<EMAIL>',
      first_name: 'Alice',
      last_name: 'Johnson',
      phone: '+91-**********',
      has_account: true,
      tenant_id: 'tenant-fashion-002',
      addresses: [
        {
          id: 'addr_002',
          first_name: 'Alice',
          last_name: 'Johnson',
          address_1: '456 Fashion Avenue',
          city: 'Delhi',
          postal_code: '110001',
          country_code: 'IN',
          phone: '+91-**********'
        }
      ],
      orders: [],
      created_at: '2024-01-03T00:00:00Z',
      updated_at: '2024-01-03T00:00:00Z'
    }
  ],
  'default': [
    {
      id: 'cus_default_001',
      email: '<EMAIL>',
      first_name: 'Demo',
      last_name: 'User',
      phone: '+91-**********',
      has_account: true,
      tenant_id: 'default',
      addresses: [],
      orders: [],
      created_at: '2024-01-04T00:00:00Z',
      updated_at: '2024-01-04T00:00:00Z'
    }
  ]
};

/**
 * GET /store/customers
 * List customers for a tenant
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { limit = 10, offset = 0, q } = req.query;

    console.log(`[CUSTOMERS API] GET request for tenant: ${tenantId}`);

    // Get customers for the tenant
    let customers = mockCustomers[tenantId] || [];

    // Apply search filter if provided
    if (q && typeof q === 'string') {
      const searchTerm = q.toLowerCase();
      customers = customers.filter(customer => 
        customer.email.toLowerCase().includes(searchTerm) ||
        customer.first_name.toLowerCase().includes(searchTerm) ||
        customer.last_name.toLowerCase().includes(searchTerm)
      );
    }

    // Apply pagination
    const startIndex = Number(offset);
    const endIndex = startIndex + Number(limit);
    const paginatedCustomers = customers.slice(startIndex, endIndex);

    res.status(200).json({
      customers: paginatedCustomers,
      count: customers.length,
      offset: Number(offset),
      limit: Number(limit),
      tenant_id: tenantId,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[CUSTOMERS API] Error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * POST /store/customers
 * Create a new customer
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { email, first_name, last_name, phone, password } = req.body;

    console.log(`[CUSTOMERS API] POST request for tenant: ${tenantId}`);

    // Validate required fields
    if (!email || !first_name || !last_name) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'email, first_name, and last_name are required',
        timestamp: new Date().toISOString()
      });
    }

    // Check if customer already exists
    const existingCustomers = mockCustomers[tenantId] || [];
    const existingCustomer = existingCustomers.find(c => c.email === email);
    
    if (existingCustomer) {
      return res.status(409).json({
        error: 'Customer already exists',
        message: 'A customer with this email already exists',
        timestamp: new Date().toISOString()
      });
    }

    // Create new customer
    const newCustomer = {
      id: `cus_${tenantId}_${Date.now()}`,
      email,
      first_name,
      last_name,
      phone: phone || null,
      has_account: !!password,
      tenant_id: tenantId,
      addresses: [],
      orders: [],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Add to mock data
    if (!mockCustomers[tenantId]) {
      mockCustomers[tenantId] = [];
    }
    mockCustomers[tenantId].push(newCustomer);

    res.status(201).json({
      customer: newCustomer,
      message: 'Customer created successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[CUSTOMERS API] Error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}
