import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";
import { ContainerRegistrationKeys } from "@medusajs/framework/utils";

// This endpoint now uses real Medusa customer service instead of mock data


/**
 * GET /store/customers
 * List customers using real Medusa customer service
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { limit = 10, offset = 0, q, id } = req.query;
    const customerModuleService = req.scope.resolve("customer");
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);

    logger.info(`[CUSTOMERS API] GET request for tenant: ${tenantId}`);

    // If ID is provided, get single customer
    if (id && typeof id === 'string') {
      try {
        const customer = await customerModuleService.retrieveCustomer(id);

        if (!customer) {
          return res.status(404).json({
            error: 'Customer not found',
            message: `Customer with ID ${id} not found`,
            timestamp: new Date().toISOString()
          });
        }

        return res.status(200).json({
          customer: {
            id: customer.id,
            email: customer.email,
            first_name: customer.first_name,
            last_name: customer.last_name,
            phone: customer.phone,
            has_account: customer.has_account,
            created_at: customer.created_at,
            updated_at: customer.updated_at,
            metadata: customer.metadata
          },
          tenant_id: tenantId,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        logger.error(`[CUSTOMERS API] Error retrieving customer ${id}:`, error);
        return res.status(404).json({
          error: 'Customer not found',
          message: `Customer with ID ${id} not found`,
          timestamp: new Date().toISOString()
        });
      }
    }

    // Build filter for listing customers
    const filters: any = {};

    // Add search filter if provided
    if (q && typeof q === 'string') {
      // For now, we'll search by email since Medusa customer service supports email filtering
      filters.email = { $ilike: `%${q}%` };
    }

    // List customers with pagination
    const customers = await customerModuleService.listCustomers(filters, {
      take: Number(limit),
      skip: Number(offset)
    });

    // Get total count for pagination
    const totalCount = await customerModuleService.listCustomers(filters, { take: 1000 });

    res.status(200).json({
      customers: customers.map(customer => ({
        id: customer.id,
        email: customer.email,
        first_name: customer.first_name,
        last_name: customer.last_name,
        phone: customer.phone,
        has_account: customer.has_account,
        created_at: customer.created_at,
        updated_at: customer.updated_at,
        metadata: customer.metadata
      })),
      count: totalCount.length,
      offset: Number(offset),
      limit: Number(limit),
      tenant_id: tenantId,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);
    logger.error('[CUSTOMERS API] Error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * POST /store/customers
 * Create a new customer using real Medusa customer service
 * Note: This endpoint redirects to /store/customers/register for compatibility
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { email, first_name, last_name, phone, password } = req.body;
    const customerModuleService = req.scope.resolve("customer");
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);

    logger.info(`[CUSTOMERS API] POST request for tenant: ${tenantId}`);

    // Validate required fields
    if (!email || !first_name || !last_name) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'email, first_name, and last_name are required',
        timestamp: new Date().toISOString()
      });
    }

    // Check if customer already exists
    try {
      const existingCustomers = await customerModuleService.listCustomers({
        email: email.toLowerCase().trim()
      });

      if (existingCustomers && existingCustomers.length > 0) {
        return res.status(409).json({
          error: 'Customer already exists',
          message: 'A customer with this email already exists',
          customer: {
            id: existingCustomers[0].id,
            email: existingCustomers[0].email,
            first_name: existingCustomers[0].first_name,
            last_name: existingCustomers[0].last_name
          },
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      logger.warn(`[CUSTOMERS API] Could not check existing customer: ${error}`);
    }

    // Create customer data
    const customerData = {
      email: email.toLowerCase().trim(),
      first_name: first_name.trim(),
      last_name: last_name.trim(),
      phone: phone || null,
      has_account: !!password,
      metadata: {
        tenant_id: tenantId,
        registration_method: "store_api",
        created_via: "customer_creation",
        registration_date: new Date().toISOString()
      }
    };

    logger.info(`[CUSTOMERS API] Creating customer with data:`, customerData);

    // Create the customer
    const customer = await customerModuleService.createCustomers(customerData);

    logger.info(`[CUSTOMERS API] Customer created successfully:`, customer.id);

    res.status(201).json({
      customer: {
        id: customer.id,
        email: customer.email,
        first_name: customer.first_name,
        last_name: customer.last_name,
        phone: customer.phone,
        has_account: customer.has_account,
        created_at: customer.created_at,
        updated_at: customer.updated_at,
        metadata: customer.metadata
      },
      message: 'Customer created successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);
    logger.error('[CUSTOMERS API] Error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      details: process.env.NODE_ENV === "development" ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });
  }
}
