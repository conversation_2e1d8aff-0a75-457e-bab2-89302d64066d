import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { ContainerRegistrationKeys } from "@medusajs/framework/utils"

/**
 * Customer Profile Management API
 * Get and update customer profile data
 */

/**
 * GET /store/customers/me
 * Get current customer profile data
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { email, customer_id } = req.query;
    
    const customerModuleService = req.scope.resolve("customer");
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);

    logger.info(`[CUSTOMER PROFILE] GET request for tenant: ${tenantId}`);

    // For demo purposes, allow fetching by email or customer_id
    // In production, this would use authentication middleware
    let customer = null;

    if (customer_id && typeof customer_id === 'string') {
      try {
        customer = await customerModuleService.retrieveCustomer(customer_id);
      } catch (error) {
        logger.warn(`[CUSTOMER PROFILE] Customer not found by ID: ${customer_id}`);
      }
    } else if (email && typeof email === 'string') {
      try {
        const customers = await customerModuleService.listCustomers({
          email: email.toLowerCase().trim()
        });
        if (customers && customers.length > 0) {
          customer = customers[0];
        }
      } catch (error) {
        logger.warn(`[CUSTOMER PROFILE] Customer not found by email: ${email}`);
      }
    }

    if (!customer) {
      return res.status(404).json({
        error: 'Customer not found',
        message: 'Customer profile not found',
        timestamp: new Date().toISOString()
      });
    }

    // Check tenant access
    if (customer.metadata?.tenant_id && customer.metadata.tenant_id !== tenantId) {
      return res.status(404).json({
        error: 'Customer not found',
        message: 'Customer profile not found',
        timestamp: new Date().toISOString()
      });
    }

    // Return customer profile data
    res.status(200).json({
      customer: {
        id: customer.id,
        email: customer.email,
        first_name: customer.first_name,
        last_name: customer.last_name,
        phone: customer.phone,
        has_account: customer.has_account,
        created_at: customer.created_at,
        updated_at: customer.updated_at,
        metadata: {
          ...customer.metadata,
          // Remove sensitive metadata for customer view
          tenant_id: customer.metadata?.tenant_id
        }
      },
      tenant_id: tenantId,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);
    logger.error('[CUSTOMER PROFILE] Error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error.message || 'Failed to retrieve customer profile',
      details: process.env.NODE_ENV === "development" ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * PUT /store/customers/me
 * Update current customer profile data
 */
export async function PUT(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { 
      customer_id,
      email: queryEmail,
      first_name, 
      last_name, 
      phone,
      email: bodyEmail,
      metadata = {} 
    } = req.body;
    
    const customerModuleService = req.scope.resolve("customer");
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);

    logger.info(`[CUSTOMER PROFILE] PUT request for tenant: ${tenantId}`);

    // Get customer by ID or email
    let customer = null;
    const lookupEmail = queryEmail || bodyEmail;

    if (customer_id) {
      try {
        customer = await customerModuleService.retrieveCustomer(customer_id);
      } catch (error) {
        logger.warn(`[CUSTOMER PROFILE] Customer not found by ID: ${customer_id}`);
      }
    } else if (lookupEmail) {
      try {
        const customers = await customerModuleService.listCustomers({
          email: lookupEmail.toLowerCase().trim()
        });
        if (customers && customers.length > 0) {
          customer = customers[0];
        }
      } catch (error) {
        logger.warn(`[CUSTOMER PROFILE] Customer not found by email: ${lookupEmail}`);
      }
    }

    if (!customer) {
      return res.status(404).json({
        error: 'Customer not found',
        message: 'Customer profile not found',
        timestamp: new Date().toISOString()
      });
    }

    // Check tenant access
    if (customer.metadata?.tenant_id && customer.metadata.tenant_id !== tenantId) {
      return res.status(404).json({
        error: 'Customer not found',
        message: 'Customer profile not found',
        timestamp: new Date().toISOString()
      });
    }

    // If email is being updated, check for conflicts
    if (bodyEmail && bodyEmail.toLowerCase().trim() !== customer.email) {
      const existingWithEmail = await customerModuleService.listCustomers({
        email: bodyEmail.toLowerCase().trim()
      });

      if (existingWithEmail && existingWithEmail.length > 0) {
        return res.status(409).json({
          error: 'Email already exists',
          message: 'A customer with this email already exists',
          timestamp: new Date().toISOString()
        });
      }
    }

    // Prepare update data
    const updateData: any = {};
    
    if (bodyEmail !== undefined) updateData.email = bodyEmail.toLowerCase().trim();
    if (first_name !== undefined) updateData.first_name = first_name.trim();
    if (last_name !== undefined) updateData.last_name = last_name.trim();
    if (phone !== undefined) updateData.phone = phone;
    
    updateData.metadata = {
      ...customer.metadata,
      ...metadata,
      tenant_id: tenantId,
      updated_via: "customer_api",
      updated_by: "customer",
      last_updated: new Date().toISOString()
    };

    logger.info(`[CUSTOMER PROFILE] Updating customer ${customer.id}:`, { 
      email: updateData.email, 
      first_name: updateData.first_name,
      last_name: updateData.last_name 
    });

    // Update customer
    const updatedCustomer = await customerModuleService.updateCustomers(customer.id, updateData);

    logger.info(`[CUSTOMER PROFILE] Customer ${customer.id} updated successfully`);

    res.status(200).json({
      customer: {
        id: updatedCustomer.id,
        email: updatedCustomer.email,
        first_name: updatedCustomer.first_name,
        last_name: updatedCustomer.last_name,
        phone: updatedCustomer.phone,
        has_account: updatedCustomer.has_account,
        created_at: updatedCustomer.created_at,
        updated_at: updatedCustomer.updated_at,
        metadata: {
          ...updatedCustomer.metadata,
          // Remove sensitive metadata for customer view
          tenant_id: updatedCustomer.metadata?.tenant_id
        }
      },
      message: 'Customer profile updated successfully',
      tenant_id: tenantId,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER);
    logger.error(`[CUSTOMER PROFILE] Error updating customer:`, error);
    res.status(500).json({
      error: 'Customer update failed',
      message: error.message || 'Failed to update customer profile',
      details: process.env.NODE_ENV === "development" ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });
  }
}
