import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";

// Mock cart data (shared with parent route)
const mockCarts = {
  'tenant-electronics-001': [
    {
      id: 'cart_electronics_001',
      customer_id: 'cus_electronics_001',
      tenant_id: 'tenant-electronics-001',
      region_id: 'reg_01',
      currency_code: 'INR',
      items: [
        {
          id: 'item_001',
          cart_id: 'cart_electronics_001',
          product_id: 'prod_electronics_001',
          variant_id: 'variant_001',
          title: 'Smartphone Pro Max',
          description: 'Latest smartphone with advanced features',
          thumbnail: '/images/products/smartphone-pro.jpg',
          quantity: 1,
          unit_price: 79999,
          total: 79999,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z'
        }
      ],
      subtotal: 79999,
      tax_total: 14399,
      total: 94398,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    }
  ],
  'tenant-fashion-002': [
    {
      id: 'cart_fashion_001',
      customer_id: 'cus_fashion_001',
      tenant_id: 'tenant-fashion-002',
      region_id: 'reg_01',
      currency_code: 'INR',
      items: [
        {
          id: 'item_002',
          cart_id: 'cart_fashion_001',
          product_id: 'prod_fashion_001',
          variant_id: 'variant_002',
          title: 'Designer Dress',
          description: 'Elegant evening dress',
          thumbnail: '/images/products/designer-dress.jpg',
          quantity: 1,
          unit_price: 12999,
          total: 12999,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z'
        }
      ],
      subtotal: 12999,
      tax_total: 2339,
      total: 15338,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    }
  ],
  'default': []
};

/**
 * GET /store/carts/[id]
 * Get cart details by ID
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const cartId = req.params.id;

    console.log(`[CARTS API] GET cart ${cartId} for tenant: ${tenantId}`);

    // Get carts for the tenant
    const carts = mockCarts[tenantId] || [];
    const cart = carts.find(c => c.id === cartId);

    if (!cart) {
      return res.status(404).json({
        error: 'Cart not found',
        message: `Cart with ID ${cartId} not found for tenant ${tenantId}`,
        timestamp: new Date().toISOString()
      });
    }

    res.status(200).json({
      cart,
      tenant_id: tenantId,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[CARTS API] Error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * PUT /store/carts/[id]
 * Update cart details
 */
export async function PUT(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const cartId = req.params.id;
    const { customer_id, region_id, currency_code } = req.body;

    console.log(`[CARTS API] PUT cart ${cartId} for tenant: ${tenantId}`);

    // Get carts for the tenant
    const carts = mockCarts[tenantId] || [];
    const cartIndex = carts.findIndex(c => c.id === cartId);

    if (cartIndex === -1) {
      return res.status(404).json({
        error: 'Cart not found',
        message: `Cart with ID ${cartId} not found for tenant ${tenantId}`,
        timestamp: new Date().toISOString()
      });
    }

    // Update cart
    const updatedCart = {
      ...carts[cartIndex],
      customer_id: customer_id !== undefined ? customer_id : carts[cartIndex].customer_id,
      region_id: region_id || carts[cartIndex].region_id,
      currency_code: currency_code || carts[cartIndex].currency_code,
      updated_at: new Date().toISOString()
    };

    carts[cartIndex] = updatedCart;

    res.status(200).json({
      cart: updatedCart,
      message: 'Cart updated successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[CARTS API] Error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * DELETE /store/carts/[id]
 * Delete cart
 */
export async function DELETE(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const cartId = req.params.id;

    console.log(`[CARTS API] DELETE cart ${cartId} for tenant: ${tenantId}`);

    // Get carts for the tenant
    const carts = mockCarts[tenantId] || [];
    const cartIndex = carts.findIndex(c => c.id === cartId);

    if (cartIndex === -1) {
      return res.status(404).json({
        error: 'Cart not found',
        message: `Cart with ID ${cartId} not found for tenant ${tenantId}`,
        timestamp: new Date().toISOString()
      });
    }

    // Remove cart
    carts.splice(cartIndex, 1);

    res.status(200).json({
      message: 'Cart deleted successfully',
      cart_id: cartId,
      tenant_id: tenantId,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[CARTS API] Error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}
