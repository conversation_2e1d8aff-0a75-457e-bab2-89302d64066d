import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { ContainerRegistrationKeys } from "@medusajs/framework/utils"

/**
 * Complete Cart with Cash on Delivery
 * This endpoint bypasses complex payment sessions and creates an order with COD payment
 */
export async function POST(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  try {
    const cartId = req.params.id
    const cartModuleService = req.scope.resolve("cart")
    const orderModuleService = req.scope.resolve("order")
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER)

    logger.info(`[COD Order] Starting COD order creation for cart: ${cartId}`)

    // Get cart details
    const cart = await cartModuleService.retrieveCart(cartId, {
      relations: ["items", "shipping_address", "billing_address"]
    })

    if (!cart) {
      res.status(404).json({
        error: "Cart not found",
        message: `Cart with ID ${cartId} not found`
      })
      return
    }

    if (!cart.items || cart.items.length === 0) {
      res.status(400).json({
        error: "Empty cart",
        message: "Cannot create order from empty cart"
      })
      return
    }

    logger.info(`[COD Order] Cart found with ${cart.items.length} items, total: ${cart.total}`)

    // Create order data
    const orderData = {
      cart_id: cartId,
      region_id: cart.region_id,
      currency_code: cart.currency_code,
      email: cart.email || "<EMAIL>",
      
      // Order totals
      total: cart.total || 0,
      subtotal: cart.subtotal || 0,
      tax_total: cart.tax_total || 0,
      shipping_total: cart.shipping_total || 0,
      
      // Payment information for COD
      payment_status: "awaiting",
      payment_method: "cash_on_delivery",
      
      // Fulfillment status
      fulfillment_status: "not_fulfilled",
      
      // Order items
      items: cart.items.map((item: any) => ({
        variant_id: item.variant_id,
        product_id: item.product_id,
        title: item.title || item.product?.title || "Product",
        quantity: item.quantity,
        unit_price: item.unit_price,
        total: item.quantity * item.unit_price,
        metadata: {
          product_title: item.product?.title,
          variant_title: item.variant?.title,
          sku: item.variant?.sku
        }
      })),
      
      // Addresses
      shipping_address: cart.shipping_address || {
        first_name: "Guest",
        last_name: "Customer",
        address_1: "Default Address",
        city: "Default City",
        postal_code: "00000",
        country_code: "us"
      },
      
      billing_address: cart.billing_address || cart.shipping_address || {
        first_name: "Guest",
        last_name: "Customer", 
        address_1: "Default Address",
        city: "Default City",
        postal_code: "00000",
        country_code: "us"
      },
      
      // Metadata
      metadata: {
        payment_method: "cash_on_delivery",
        payment_provider: "manual",
        created_via: "cod_api",
        cart_id: cartId
      }
    }

    logger.info(`[COD Order] Creating order with data:`, JSON.stringify(orderData, null, 2))

    // Create the order
    const order = await orderModuleService.createOrders(orderData)
    
    logger.info(`[COD Order] Order created successfully:`, order.id)

    // Mark cart as completed (optional - you might want to keep it for reference)
    await cartModuleService.updateCarts(cartId, {
      completed_at: new Date(),
      metadata: {
        ...cart.metadata,
        order_id: order.id,
        completed_via: "cod_api"
      }
    })

    // Return success response
    res.status(200).json({
      success: true,
      order: {
        id: order.id,
        cart_id: cartId,
        total: orderData.total,
        currency_code: orderData.currency_code,
        payment_method: "cash_on_delivery",
        payment_status: "awaiting",
        fulfillment_status: "not_fulfilled",
        items: orderData.items,
        shipping_address: orderData.shipping_address,
        created_at: new Date().toISOString(),
        metadata: orderData.metadata
      },
      message: "Order created successfully with Cash on Delivery payment"
    })

  } catch (error: any) {
    const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER)
    logger.error(`[COD Order] Error creating COD order:`, error)
    
    res.status(500).json({
      error: "Order creation failed",
      message: error.message || "Failed to create order with Cash on Delivery",
      details: process.env.NODE_ENV === "development" ? error.stack : undefined
    })
  }
}
