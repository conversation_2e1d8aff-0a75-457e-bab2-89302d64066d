import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";

// Mock cart data (shared with parent routes)
const mockCarts = {
  'tenant-electronics-001': [
    {
      id: 'cart_electronics_001',
      customer_id: 'cus_electronics_001',
      tenant_id: 'tenant-electronics-001',
      region_id: 'reg_01',
      currency_code: 'INR',
      items: [
        {
          id: 'item_001',
          cart_id: 'cart_electronics_001',
          product_id: 'prod_electronics_001',
          variant_id: 'variant_001',
          title: 'Smartphone Pro Max',
          description: 'Latest smartphone with advanced features',
          thumbnail: '/images/products/smartphone-pro.jpg',
          quantity: 1,
          unit_price: 79999,
          total: 79999,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z'
        }
      ],
      subtotal: 79999,
      tax_total: 14399,
      total: 94398,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    }
  ],
  'tenant-fashion-002': [
    {
      id: 'cart_fashion_001',
      customer_id: 'cus_fashion_001',
      tenant_id: 'tenant-fashion-002',
      region_id: 'reg_01',
      currency_code: 'INR',
      items: [
        {
          id: 'item_002',
          cart_id: 'cart_fashion_001',
          product_id: 'prod_fashion_001',
          variant_id: 'variant_002',
          title: 'Designer Dress',
          description: 'Elegant evening dress',
          thumbnail: '/images/products/designer-dress.jpg',
          quantity: 1,
          unit_price: 12999,
          total: 12999,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z'
        }
      ],
      subtotal: 12999,
      tax_total: 2339,
      total: 15338,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    }
  ],
  'default': []
};

// Helper function to recalculate cart totals
function recalculateCartTotals(cart: any) {
  const subtotal = cart.items.reduce((sum: number, item: any) => sum + item.total, 0);
  const tax_total = Math.round(subtotal * 0.18); // 18% GST
  const total = subtotal + tax_total;
  
  cart.subtotal = subtotal;
  cart.tax_total = tax_total;
  cart.total = total;
  cart.updated_at = new Date().toISOString();
}

/**
 * POST /store/carts/[id]/line-items
 * Add item to cart
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const cartId = req.params.id;
    const { product_id, variant_id, quantity = 1, unit_price, title, description, thumbnail } = req.body;

    console.log(`[CART LINE ITEMS API] POST item to cart ${cartId} for tenant: ${tenantId}`);

    // Validate required fields
    if (!product_id || !variant_id || !unit_price) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'product_id, variant_id, and unit_price are required',
        timestamp: new Date().toISOString()
      });
    }

    // Get carts for the tenant
    const carts = mockCarts[tenantId] || [];
    const cartIndex = carts.findIndex(c => c.id === cartId);

    if (cartIndex === -1) {
      return res.status(404).json({
        error: 'Cart not found',
        message: `Cart with ID ${cartId} not found for tenant ${tenantId}`,
        timestamp: new Date().toISOString()
      });
    }

    const cart = carts[cartIndex];

    // Check if item already exists in cart
    const existingItemIndex = cart.items.findIndex((item: any) => 
      item.product_id === product_id && item.variant_id === variant_id
    );

    if (existingItemIndex !== -1) {
      // Update existing item quantity
      cart.items[existingItemIndex].quantity += Number(quantity);
      cart.items[existingItemIndex].total = cart.items[existingItemIndex].quantity * cart.items[existingItemIndex].unit_price;
      cart.items[existingItemIndex].updated_at = new Date().toISOString();
    } else {
      // Add new item
      const newItem = {
        id: `item_${Date.now()}`,
        cart_id: cartId,
        product_id,
        variant_id,
        title: title || `Product ${product_id}`,
        description: description || '',
        thumbnail: thumbnail || '/images/products/default.jpg',
        quantity: Number(quantity),
        unit_price: Number(unit_price),
        total: Number(quantity) * Number(unit_price),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      cart.items.push(newItem);
    }

    // Recalculate cart totals
    recalculateCartTotals(cart);

    res.status(201).json({
      cart,
      message: 'Item added to cart successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[CART LINE ITEMS API] Error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}
