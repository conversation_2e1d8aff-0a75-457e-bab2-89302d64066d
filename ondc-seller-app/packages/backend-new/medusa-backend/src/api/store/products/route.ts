import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { ContainerRegistrationKeys } from "@medusajs/framework/utils"

// Tenant-specific product filtering configuration
const TENANT_PRODUCT_CONFIG = {
  'tenant-electronics-001': {
    categories: ['Electronics', 'Gadgets', 'Tech Accessories'],
    collections: ['featured-products', 'top-deals'],
    tags: ['electronics', 'tech', 'gadgets'],
    productFilter: (products: any[]) => {
      // Filter products that are electronics-related
      return products.filter(product =>
        product.title?.toLowerCase().includes('jacket') ||
        product.title?.toLowerCase().includes('tech') ||
        product.title?.toLowerCase().includes('electronic') ||
        product.categories?.some((cat: any) =>
          cat.name?.toLowerCase().includes('electronics') ||
          cat.name?.toLowerCase().includes('tech')
        )
      )
    }
  },
  'tenant-fashion-002': {
    categories: ['Fashion', 'Clothing', 'Accessories'],
    collections: ['hot-picks', 'featured-products'],
    tags: ['fashion', 'clothing', 'style'],
    productFilter: (products: any[]) => {
      // Filter products that are fashion-related
      return products.filter(product =>
        product.title?.toLowerCase().includes('shirt') ||
        product.title?.toLowerCase().includes('dress') ||
        product.title?.toLowerCase().includes('fashion') ||
        product.categories?.some((cat: any) =>
          cat.name?.toLowerCase().includes('fashion') ||
          cat.name?.toLowerCase().includes('clothing')
        )
      )
    }
  },
  'default': {
    categories: [],
    collections: [],
    tags: [],
    productFilter: (products: any[]) => products // Return all products for default tenant
  }
}

export const GET = async (
  req: MedusaRequest,
  res: MedusaResponse
) => {
  // Get tenant ID from header
  const tenantId = req.headers["x-tenant-id"] as string || "default"

  try {
    // Use the query system to get all products
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY)

    // Get query parameters
    const {
      limit = 10,
      offset = 0,
      q,
      category_id,
      collection_id,
      tags,
      region_id,
    } = req.query

    // Build filters
    const filters: any = {
      status: "published"
    }

    // Add search query if provided
    if (q) {
      filters.title = {
        $ilike: `%${q}%`
      }
    }

    // Add category filter if provided
    if (category_id) {
      filters.categories = {
        id: category_id
      }
    }

    // Add collection filter if provided
    if (collection_id) {
      filters.collection_id = collection_id
    }

    // Add tags filter if provided
    if (tags) {
      const tagArray = Array.isArray(tags) ? tags : [tags]
      filters.tags = {
        value: {
          $in: tagArray
        }
      }
    }

    // Get products with relations (fetch more to allow for tenant filtering)
    const { data: allProducts, metadata } = await query.graph({
      entity: "product",
      fields: [
        "id",
        "title",
        "description",
        "handle",
        "status",
        "thumbnail",
        "created_at",
        "updated_at",
        "metadata",
        "variants.*",
        "images.*",
        "tags.*",
        "categories.*",
        "collection.*"
      ],
      filters,
      pagination: {
        skip: 0,
        take: 100 // Fetch more products to allow for tenant filtering
      }
    })

    // Apply tenant-specific filtering
    const tenantConfig = TENANT_PRODUCT_CONFIG[tenantId] || TENANT_PRODUCT_CONFIG['default']
    const tenantFilteredProducts = tenantConfig.productFilter(allProducts)

    // Apply pagination after tenant filtering
    const startIndex = Number(offset)
    const endIndex = startIndex + Number(limit)
    const products = tenantFilteredProducts.slice(startIndex, endIndex)

    // Calculate total count from tenant-filtered products
    const total = tenantFilteredProducts.length

    console.log(`[Store Products API] Found ${products.length}/${total} products for tenant: ${tenantId}`)

    res.json({
      products,
      count: products.length,
      offset: Number(offset),
      limit: Number(limit),
      total,
      metadata: {
        tenantId,
        tenantConfig: {
          categories: tenantConfig.categories,
          collections: tenantConfig.collections,
          tags: tenantConfig.tags
        },
        filtering: {
          totalBeforeFiltering: allProducts.length,
          totalAfterFiltering: tenantFilteredProducts.length,
          returned: products.length
        },
        timestamp: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('[Store Products API] Error:', error)
    res.status(500).json({
      error: 'Failed to fetch products',
      message: error instanceof Error ? error.message : 'Unknown error',
      tenantId,
      timestamp: new Date().toISOString()
    })
  }
}
