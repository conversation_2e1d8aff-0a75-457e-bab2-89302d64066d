import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { ContainerRegistrationKeys } from "@medusajs/framework/utils"

export const GET = async (
  req: MedusaRequest,
  res: MedusaResponse
) => {
  // Get tenant ID from header
  const tenantId = req.headers["x-tenant-id"] as string || "default"

  try {
    // Use the query system to get all products
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY)

    // Get query parameters
    const {
      limit = 10,
      offset = 0,
      q,
      category_id,
      collection_id,
      tags,
      region_id,
    } = req.query

    // Build filters
    const filters: any = {
      status: "published"
    }

    // Add search query if provided
    if (q) {
      filters.title = {
        $ilike: `%${q}%`
      }
    }

    // Add category filter if provided
    if (category_id) {
      filters.categories = {
        id: category_id
      }
    }

    // Add collection filter if provided
    if (collection_id) {
      filters.collection_id = collection_id
    }

    // Add tags filter if provided
    if (tags) {
      const tagArray = Array.isArray(tags) ? tags : [tags]
      filters.tags = {
        value: {
          $in: tagArray
        }
      }
    }

    // Get products with relations
    const { data: products, metadata } = await query.graph({
      entity: "product",
      fields: [
        "id",
        "title",
        "description",
        "handle",
        "status",
        "thumbnail",
        "created_at",
        "updated_at",
        "metadata",
        "variants.*",
        "images.*",
        "tags.*",
        "categories.*",
        "collection.*"
      ],
      filters,
      pagination: {
        skip: Number(offset),
        take: Number(limit)
      }
    })

    // Get total count for pagination
    const { data: countData } = await query.graph({
      entity: "product",
      fields: ["id"],
      filters
    })

    const total = countData.length

    console.log(`[Store Products API] Found ${products.length} products for tenant: ${tenantId}`)

    res.json({
      products,
      count: products.length,
      offset: Number(offset),
      limit: Number(limit),
      total,
      metadata: {
        tenantId,
        timestamp: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('[Store Products API] Error:', error)
    res.status(500).json({
      error: 'Failed to fetch products',
      message: error instanceof Error ? error.message : 'Unknown error',
      tenantId,
      timestamp: new Date().toISOString()
    })
  }
}
