import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { ContainerRegistrationKeys } from "@medusajs/framework/utils"

export const GET = async (
  req: MedusaRequest,
  res: MedusaResponse
) => {
  const { id } = req.params
  const tenantId = req.headers["x-tenant-id"] as string || "default"

  try {
    // Use the query system to get the product by ID
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY)

    // Get query parameters for expansion
    const { region_id } = req.query

    console.log(`[Store Product Details API] Fetching product: ${id} for tenant: ${tenantId}`)

    // Get product with all relations
    const { data: products } = await query.graph({
      entity: "product",
      fields: [
        "id",
        "title",
        "description",
        "handle",
        "status",
        "thumbnail",
        "created_at",
        "updated_at",
        "metadata",
        "variants.*",
        "variants.prices.*",
        "images.*",
        "tags.*",
        "categories.*",
        "collection.*"
      ],
      filters: {
        id,
        status: "published"
      }
    })

    if (!products || products.length === 0) {
      console.log(`[Store Product Details API] Product not found: ${id}`)
      return res.status(404).json({
        error: 'Product not found',
        message: `Product with ID ${id} not found`,
        product_id: id,
        tenantId,
        timestamp: new Date().toISOString()
      })
    }

    const product = products[0]

    // Add computed fields for better frontend integration
    const enhancedProduct = {
      ...product,
      computed: {
        price_range: calculatePriceRange(product.variants),
        availability: checkAvailability(product.variants),
        variant_count: product.variants?.length || 0,
        image_count: product.images?.length || 0
      }
    }

    console.log(`[Store Product Details API] Successfully fetched product: ${product.title}`)

    res.json({
      product: enhancedProduct,
      metadata: {
        tenantId,
        timestamp: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('[Store Product Details API] Error:', error)
    res.status(500).json({
      error: 'Failed to fetch product details',
      message: error instanceof Error ? error.message : 'Unknown error',
      product_id: id,
      tenantId,
      timestamp: new Date().toISOString()
    })
  }
}

// Helper function to calculate price range from variants
function calculatePriceRange(variants: any[] = []) {
  if (!variants || variants.length === 0) {
    return { min: 0, max: 0, currency: 'USD' }
  }

  const prices = variants
    .flatMap(variant => variant.prices || [])
    .map(price => price.amount)
    .filter(amount => amount != null)

  if (prices.length === 0) {
    return { min: 0, max: 0, currency: 'USD' }
  }

  return {
    min: Math.min(...prices),
    max: Math.max(...prices),
    currency: 'USD' // Default currency, could be dynamic
  }
}

// Helper function to check availability
function checkAvailability(variants: any[] = []) {
  if (!variants || variants.length === 0) {
    return { in_stock: false, total_inventory: 0 }
  }

  const totalInventory = variants.reduce((total, variant) => {
    return total + (variant.inventory_quantity || 0)
  }, 0)

  return {
    in_stock: totalInventory > 0,
    total_inventory: totalInventory,
    variant_availability: variants.map(variant => ({
      variant_id: variant.id,
      in_stock: (variant.inventory_quantity || 0) > 0,
      inventory: variant.inventory_quantity || 0
    }))
  }
}
