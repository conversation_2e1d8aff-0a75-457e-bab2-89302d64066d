import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"

export const GET = async (
  req: MedusaRequest,
  res: MedusaResponse
) => {
  // Get tenant ID from header
  const tenantId = req.headers["x-tenant-id"] as string || "default"
  
  try {
    // Get tenant-specific store information
    const storeInfo = {
      id: tenantId,
      name: tenantId === "tenant-electronics-001" ? "Electronics Store" : 
            tenantId === "tenant-fashion-002" ? "Fashion Store" : "Default Store",
      domain: tenantId === "tenant-electronics-001" ? "electronics.ondc-seller.com" : 
              tenantId === "tenant-fashion-002" ? "fashion.ondc-seller.com" : "localhost",
      currency: "INR",
      timezone: "Asia/Kolkata",
      region: "India",
      features: tenantId === "tenant-electronics-001" ? 
        ["electronics", "gadgets", "smartphones", "laptops", "audio"] :
        tenantId === "tenant-fashion-002" ? 
        ["fashion", "clothing", "accessories", "footwear", "jewelry"] :
        ["general", "marketplace"],
      branding: {
        primaryColor: tenantId === "tenant-electronics-001" ? "#2563eb" : 
                     tenantId === "tenant-fashion-002" ? "#ec4899" : "#6b7280",
        logo: tenantId === "tenant-electronics-001" ? "/logos/electronics-logo.png" : 
              tenantId === "tenant-fashion-002" ? "/logos/fashion-logo.png" : "/logos/default-logo.png"
      },
      contact: {
        email: tenantId === "tenant-electronics-001" ? "<EMAIL>" : 
               tenantId === "tenant-fashion-002" ? "<EMAIL>" : "support@localhost",
        phone: "+91-1234567890",
        address: {
          street: "123 Commerce Street",
          city: "Mumbai",
          state: "Maharashtra",
          country: "India",
          pincode: "400001"
        }
      },
      ondcConfig: tenantId === "tenant-electronics-001" ? {
        participantId: "electronics-participant-001",
        subscriberId: "electronics-subscriber-001",
        bppId: "ondc-bpp-electronics-001",
        domain: "electronics",
        region: "IND"
      } : tenantId === "tenant-fashion-002" ? {
        participantId: "fashion-participant-002",
        subscriberId: "fashion-subscriber-002",
        bppId: "ondc-bpp-fashion-002",
        domain: "fashion",
        region: "IND"
      } : {
        participantId: "default-participant",
        subscriberId: "default-subscriber",
        bppId: "ondc-bpp-default",
        domain: "general",
        region: "IND"
      }
    }

    return res.json({
      store: storeInfo,
      tenant_id: tenantId,
      api_version: "v2",
      multi_tenant: true,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error("Store info error:", error)
    return res.status(500).json({
      error: "Failed to fetch store information",
      tenant_id: tenantId,
      message: error.message
    })
  }
}
