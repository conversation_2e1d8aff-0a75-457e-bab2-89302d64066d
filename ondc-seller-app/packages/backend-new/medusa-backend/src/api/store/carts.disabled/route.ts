import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";

// Mock cart data for multi-tenant testing
const mockCarts = {
  'tenant-electronics-001': [
    {
      id: 'cart_electronics_001',
      customer_id: 'cus_electronics_001',
      tenant_id: 'tenant-electronics-001',
      region_id: 'reg_01',
      currency_code: 'INR',
      items: [
        {
          id: 'item_001',
          cart_id: 'cart_electronics_001',
          product_id: 'prod_electronics_001',
          variant_id: 'variant_001',
          title: 'Smartphone Pro Max',
          description: 'Latest smartphone with advanced features',
          thumbnail: '/images/products/smartphone-pro.jpg',
          quantity: 1,
          unit_price: 79999,
          total: 79999,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z'
        }
      ],
      subtotal: 79999,
      tax_total: 14399,
      total: 94398,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    }
  ],
  'tenant-fashion-002': [
    {
      id: 'cart_fashion_001',
      customer_id: 'cus_fashion_001',
      tenant_id: 'tenant-fashion-002',
      region_id: 'reg_01',
      currency_code: 'INR',
      items: [
        {
          id: 'item_002',
          cart_id: 'cart_fashion_001',
          product_id: 'prod_fashion_001',
          variant_id: 'variant_002',
          title: 'Designer Dress',
          description: 'Elegant evening dress',
          thumbnail: '/images/products/designer-dress.jpg',
          quantity: 1,
          unit_price: 12999,
          total: 12999,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z'
        }
      ],
      subtotal: 12999,
      tax_total: 2339,
      total: 15338,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    }
  ],
  'default': []
};

/**
 * GET /store/carts
 * List carts for a tenant
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { customer_id, limit = 10, offset = 0 } = req.query;

    console.log(`[CARTS API] GET request for tenant: ${tenantId}`);

    // Get carts for the tenant
    let carts = mockCarts[tenantId] || [];

    // Filter by customer if provided
    if (customer_id && typeof customer_id === 'string') {
      carts = carts.filter(cart => cart.customer_id === customer_id);
    }

    // Apply pagination
    const startIndex = Number(offset);
    const endIndex = startIndex + Number(limit);
    const paginatedCarts = carts.slice(startIndex, endIndex);

    res.status(200).json({
      carts: paginatedCarts,
      count: carts.length,
      offset: Number(offset),
      limit: Number(limit),
      tenant_id: tenantId,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[CARTS API] Error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * POST /store/carts
 * Create a new cart
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string || 'default';
    const { customer_id, region_id = 'reg_01', currency_code = 'INR' } = req.body;

    console.log(`[CARTS API] POST request for tenant: ${tenantId}`);

    // Create new cart
    const newCart = {
      id: `cart_${tenantId}_${Date.now()}`,
      customer_id: customer_id || null,
      tenant_id: tenantId,
      region_id,
      currency_code,
      items: [],
      subtotal: 0,
      tax_total: 0,
      total: 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Add to mock data
    if (!mockCarts[tenantId]) {
      mockCarts[tenantId] = [];
    }
    mockCarts[tenantId].push(newCart);

    res.status(201).json({
      cart: newCart,
      message: 'Cart created successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[CARTS API] Error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}
