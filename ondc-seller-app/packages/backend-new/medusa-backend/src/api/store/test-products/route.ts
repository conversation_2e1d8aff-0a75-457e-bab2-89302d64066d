import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { ContainerRegistrationKeys } from "@medusajs/framework/utils"

export const GET = async (
  req: MedusaRequest,
  res: MedusaResponse
) => {
  // Get tenant ID from header
  const tenantId = req.headers["x-tenant-id"] as string || "default"

  try {
    // Use the query system to get all products first
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY)

    // Get all published products
    const { data: allProducts } = await query.graph({
      entity: "product",
      fields: [
        "id",
        "title",
        "description",
        "handle",
        "status",
        "thumbnail"
      ],
      filters: {
        status: "published"
      }
    })

    // For demonstration, let's create tenant-specific product filtering
    // In a real implementation, you'd want to modify the Medusa core to support tenant filtering
    const tenantProducts = allProducts || []

    // Mock tenant-specific filtering based on product IDs
    const filteredProducts = tenantProducts.filter(product => {
      if (tenantId === "tenant-electronics-001") {
        return product.id.includes("electronics") || product.title.toLowerCase().includes("iphone") ||
               product.title.toLowerCase().includes("samsung") || product.title.toLowerCase().includes("macbook") ||
               product.title.toLowerCase().includes("dell") || product.title.toLowerCase().includes("sony")
      } else if (tenantId === "tenant-fashion-002") {
        return product.id.includes("fashion") || product.title.toLowerCase().includes("shirt") ||
               product.title.toLowerCase().includes("dress") || product.title.toLowerCase().includes("blazer") ||
               product.title.toLowerCase().includes("jeans") || product.title.toLowerCase().includes("handbag")
      }
      return true // Default tenant gets all products
    })

    // Get tenant configuration
    const tenantConfig = {
      id: tenantId,
      name: tenantId === "tenant-electronics-001" ? "Electronics Store" :
            tenantId === "tenant-fashion-002" ? "Fashion Store" : "Default Store",
      domain: tenantId === "tenant-electronics-001" ? "electronics.ondc-seller.com" :
              tenantId === "tenant-fashion-002" ? "fashion.ondc-seller.com" : "localhost"
    }

    return res.json({
      store: tenantConfig,
      products: filteredProducts,
      count: filteredProducts.length,
      tenant_id: tenantId,
      total_products_in_system: allProducts?.length || 0,
      filtered_for_tenant: filteredProducts.length,
      note: "Store API with tenant-based product filtering"
    })
  } catch (error) {
    console.error("Store products error:", error)
    return res.status(500).json({
      error: "Failed to fetch products",
      tenant_id: tenantId,
      message: error.message
    })
  }
}
