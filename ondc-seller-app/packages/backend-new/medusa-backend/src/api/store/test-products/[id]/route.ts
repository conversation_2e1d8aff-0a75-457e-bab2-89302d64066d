import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { ContainerRegistrationKeys } from "@medusajs/framework/utils"

export const GET = async (
  req: MedusaRequest,
  res: MedusaResponse
) => {
  // Get tenant ID from header and product ID from params
  const tenantId = req.headers["x-tenant-id"] as string || "default"
  const productId = req.params.id
  
  try {
    // Use the query system to get the specific product
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY)
    
    // Get the specific product
    const { data: products } = await query.graph({
      entity: "product",
      fields: [
        "id",
        "title",
        "description", 
        "handle",
        "status",
        "thumbnail",
        "weight",
        "length",
        "height",
        "width",
        "hs_code",
        "origin_country",
        "material"
      ],
      filters: {
        id: productId,
        status: "published"
      }
    })

    const product = products?.[0]
    
    if (!product) {
      return res.status(404).json({
        error: "Product not found",
        product_id: productId,
        tenant_id: tenantId
      })
    }

    // Check if product belongs to the current tenant (mock implementation)
    const belongsToTenant = (product: any, tenantId: string) => {
      if (tenantId === "tenant-electronics-001") {
        return product.id.includes("electronics") || product.title.toLowerCase().includes("iphone") || 
               product.title.toLowerCase().includes("samsung") || product.title.toLowerCase().includes("macbook") ||
               product.title.toLowerCase().includes("dell") || product.title.toLowerCase().includes("sony")
      } else if (tenantId === "tenant-fashion-002") {
        return product.id.includes("fashion") || product.title.toLowerCase().includes("shirt") || 
               product.title.toLowerCase().includes("dress") || product.title.toLowerCase().includes("blazer") ||
               product.title.toLowerCase().includes("jeans") || product.title.toLowerCase().includes("handbag")
      }
      return true // Default tenant gets all products
    }

    if (!belongsToTenant(product, tenantId)) {
      return res.status(404).json({
        error: "Product not found in this store",
        product_id: productId,
        tenant_id: tenantId,
        note: "Product exists but doesn't belong to this tenant"
      })
    }

    // Get tenant-specific store information
    const storeInfo = {
      id: tenantId,
      name: tenantId === "tenant-electronics-001" ? "Electronics Store" : 
            tenantId === "tenant-fashion-002" ? "Fashion Store" : "Default Store",
      domain: tenantId === "tenant-electronics-001" ? "electronics.ondc-seller.com" : 
              tenantId === "tenant-fashion-002" ? "fashion.ondc-seller.com" : "localhost"
    }

    // Add tenant-specific product enhancements
    const enhancedProduct = {
      ...product,
      store: storeInfo,
      tenant_id: tenantId,
      availability: "in_stock",
      price: {
        amount: tenantId === "tenant-electronics-001" ? 
          (product.id.includes("001") ? 99999 : product.id.includes("002") ? 129999 : 
           product.id.includes("003") ? 199999 : product.id.includes("004") ? 89999 : 29999) :
          (product.id.includes("001") ? 2999 : product.id.includes("002") ? 4999 : 
           product.id.includes("003") ? 8999 : product.id.includes("004") ? 3999 : 12999),
        currency: "INR"
      },
      shipping: {
        free_shipping: true,
        estimated_delivery: "3-5 business days",
        regions: ["India"]
      },
      warranty: tenantId === "tenant-electronics-001" ? "1 year manufacturer warranty" : "30 days return policy"
    }

    return res.json({
      product: enhancedProduct,
      tenant_id: tenantId,
      store: storeInfo,
      api_version: "v2",
      multi_tenant: true
    })
  } catch (error) {
    console.error("Store product details error:", error)
    return res.status(500).json({
      error: "Failed to fetch product details",
      product_id: productId,
      tenant_id: tenantId,
      message: error.message
    })
  }
}
