import { MedusaRequest, MedusaResponse } from "@medusajs/medusa"
import { NextFunction } from "express"
import { TenantRequest, getTenantId } from "./tenant"

/**
 * Tenant Query Filter Middleware
 * Automatically injects tenant filtering into all database queries
 */

interface TenantFilterOptions {
  enabled: boolean
  skipPaths: string[]
  skipMethods: string[]
  enforceStrict: boolean
}

const defaultOptions: TenantFilterOptions = {
  enabled: true,
  skipPaths: ['/health', '/docs', '/auth', '/admin/auth', '/store/auth'],
  skipMethods: ['OPTIONS'],
  enforceStrict: true
}

/**
 * Query Filter Injector
 * Modifies request query to include tenant filtering
 */
export const injectTenantFilter = (
  req: TenantRequest,
  res: MedusaResponse,
  next: NextFunction,
  options: Partial<TenantFilterOptions> = {}
) => {
  const config = { ...defaultOptions, ...options }

  // Skip if disabled
  if (!config.enabled) {
    return next()
  }

  // Skip for certain paths
  const shouldSkip = config.skipPaths.some(path => req.path.startsWith(path)) ||
                    config.skipMethods.includes(req.method)

  if (shouldSkip) {
    return next()
  }

  try {
    const tenantId = getTenantId(req)

    // Inject tenant filter into query parameters
    if (!req.query) {
      req.query = {}
    }

    // Add tenant filter to query
    req.query.tenant_id = tenantId

    // Store original query for reference
    req.originalQuery = { ...req.query }

    // Add tenant context to request
    req.tenantContext = {
      tenantId,
      filterApplied: true,
      timestamp: new Date().toISOString()
    }

    next()
  } catch (error) {
    next(error)
  }
}

/**
 * Service Query Wrapper
 * Wraps service methods to automatically apply tenant filtering
 */
export class TenantAwareServiceWrapper {
  private service: any
  private tenantId: string

  constructor(service: any, tenantId: string) {
    this.service = service
    this.tenantId = tenantId
  }

  /**
   * Wrap list methods with tenant filtering
   */
  async list(query: any = {}) {
    const tenantQuery = this.addTenantFilter(query)
    return await this.service.list(tenantQuery)
  }

  /**
   * Wrap listAndCount methods with tenant filtering
   */
  async listAndCount(query: any = {}) {
    const tenantQuery = this.addTenantFilter(query)
    return await this.service.listAndCount(tenantQuery)
  }

  /**
   * Wrap retrieve methods with tenant validation
   */
  async retrieve(id: string, config: any = {}) {
    const result = await this.service.retrieve(id, config)
    
    // Validate tenant access
    if (result && result.tenant_id && result.tenant_id !== this.tenantId) {
      throw new Error(`Access denied: Resource belongs to different tenant`)
    }
    
    return result
  }

  /**
   * Wrap create methods with tenant injection
   */
  async create(data: any) {
    const tenantData = this.addTenantContext(data)
    return await this.service.create(tenantData)
  }

  /**
   * Wrap update methods with tenant validation
   */
  async update(id: string, data: any) {
    // First validate access
    await this.retrieve(id)
    
    // Remove tenant_id from update data to prevent modification
    const updateData = { ...data }
    delete updateData.tenant_id
    
    return await this.service.update(id, updateData)
  }

  /**
   * Wrap delete methods with tenant validation
   */
  async delete(id: string) {
    // First validate access
    await this.retrieve(id)
    
    return await this.service.delete(id)
  }

  /**
   * Add tenant filter to query
   */
  private addTenantFilter(query: any) {
    return {
      ...query,
      tenant_id: this.tenantId
    }
  }

  /**
   * Add tenant context to data
   */
  private addTenantContext(data: any) {
    return {
      ...data,
      tenant_id: this.tenantId
    }
  }

  /**
   * Proxy other methods to original service
   */
  [key: string]: any
}

/**
 * Service Resolver with Tenant Awareness
 */
export const resolveTenantAwareService = (req: TenantRequest, serviceName: string) => {
  const tenantId = getTenantId(req)

  // Map service names to correct Medusa v2 service names
  const serviceMap: Record<string, string> = {
    'productModuleService': 'productService',
    'customerModuleService': 'customerService',
    'orderModuleService': 'orderService',
    'cartModuleService': 'cartService',
    'inventoryModuleService': 'inventoryService',
    'stockLocationModuleService': 'stockLocationService'
  }

  const actualServiceName = serviceMap[serviceName] || serviceName

  try {
    const originalService = req.scope.resolve(actualServiceName)
    return new TenantAwareServiceWrapper(originalService, tenantId)
  } catch (error) {
    console.warn(`Service ${actualServiceName} not found, trying alternative names`)

    // Try alternative service names
    const alternatives = [
      serviceName.replace('ModuleService', ''),
      serviceName.replace('Module', ''),
      serviceName.toLowerCase()
    ]

    for (const alt of alternatives) {
      try {
        const service = req.scope.resolve(alt)
        return new TenantAwareServiceWrapper(service, tenantId)
      } catch (e) {
        continue
      }
    }

    throw new Error(`Could not resolve service: ${serviceName}`)
  }
}

/**
 * Batch Query Filter for Multiple Services
 */
export const applyTenantFilterToServices = (
  req: TenantRequest,
  serviceNames: string[]
): Record<string, TenantAwareServiceWrapper> => {
  const tenantId = getTenantId(req)
  const services: Record<string, TenantAwareServiceWrapper> = {}

  serviceNames.forEach(serviceName => {
    try {
      const originalService = req.scope.resolve(serviceName)
      services[serviceName] = new TenantAwareServiceWrapper(originalService, tenantId)
    } catch (error) {
      console.warn(`Service ${serviceName} not found, skipping tenant wrapper`)
    }
  })

  return services
}

/**
 * Tenant Filter Validation
 */
export const validateTenantAccess = (
  resource: any,
  tenantId: string,
  strict: boolean = true
): boolean => {
  if (!resource) {
    return false
  }

  // If resource doesn't have tenant_id, allow access in non-strict mode
  if (!resource.tenant_id) {
    return !strict
  }

  // Check if tenant matches
  return resource.tenant_id === tenantId
}

/**
 * Tenant Context Extractor
 */
export const extractTenantContext = (req: TenantRequest) => {
  return {
    tenantId: getTenantId(req),
    tenant: req.tenant,
    filterApplied: req.tenantContext?.filterApplied || false,
    originalQuery: req.originalQuery,
    timestamp: new Date().toISOString()
  }
}

/**
 * Response Enhancer with Tenant Info
 */
export const enhanceResponseWithTenantInfo = (
  req: TenantRequest,
  data: any
) => {
  const tenantContext = extractTenantContext(req)
  
  return {
    ...data,
    _tenant: {
      id: tenantContext.tenantId,
      name: req.tenant?.name,
      filter_applied: tenantContext.filterApplied
    }
  }
}

// Extend TenantRequest interface
declare module "./tenant" {
  interface TenantRequest {
    originalQuery?: any
    tenantContext?: {
      tenantId: string
      filterApplied: boolean
      timestamp: string
    }
  }
}
