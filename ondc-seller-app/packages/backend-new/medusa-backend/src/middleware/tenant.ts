import { MedusaRequest, MedusaResponse } from "@medusajs/medusa"
import { NextFunction } from "express"

/**
 * Tenant Configuration Interface
 */
export interface TenantConfig {
  id: string
  name: string
  domain?: string
  settings: {
    currency: string
    timezone: string
    features: string[]
    ondcConfig?: {
      participantId?: string
      subscriberId?: string
      bppId?: string
    }
  }
  status: 'active' | 'inactive' | 'suspended'
  createdAt: Date
  updatedAt: Date
}

/**
 * Extended Medusa Request with Tenant Information
 */
export interface TenantRequest extends MedusaRequest {
  tenant?: TenantConfig
  tenantId?: string
}

/**
 * Create Error Helper
 */
const createError = (message: string, statusCode: number, code: string, details?: any) => {
  const error = new Error(message) as any
  error.statusCode = statusCode
  error.code = code
  error.details = details
  return error
}

/**
 * Extract Tenant ID from Request
 */
const extractTenantId = (req: TenantRequest): string => {
  // Priority order: header > query > subdomain > default
  let tenantId = req.headers['x-tenant-id'] as string
  
  if (!tenantId) {
    tenantId = req.query.tenant as string
  }
  
  if (!tenantId && req.headers.host) {
    // Extract from subdomain (e.g., tenant1.ondc-seller.com)
    const host = req.headers.host
    const subdomain = host.split('.')[0]
    if (subdomain && subdomain !== 'www' && subdomain !== 'api') {
      tenantId = subdomain
    }
  }
  
  // Default tenant for development
  return tenantId || process.env.DEFAULT_TENANT_ID || 'default'
}

/**
 * Validate Tenant Existence and Status
 */
const validateTenant = async (tenantId: string): Promise<TenantConfig | null> => {
  // For development mode, return mock tenant
  if (process.env.NODE_ENV === 'development') {
    return {
      id: tenantId,
      name: `Development Tenant ${tenantId}`,
      settings: {
        currency: 'INR',
        timezone: 'Asia/Kolkata',
        features: ['all'],
        ondcConfig: {
          participantId: process.env.ONDC_PARTICIPANT_ID,
          subscriberId: process.env.ONDC_SUBSCRIBER_ID,
          bppId: `${process.env.ONDC_BPP_ID || 'ondc-bpp'}-${tenantId}`,
        },
      },
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date(),
    }
  }

  // In production, validate against database or configuration
  const validTenants = process.env.VALID_TENANTS?.split(',') || ['default']
  
  if (!validTenants.includes(tenantId)) {
    return null
  }

  // Mock tenant data - in real implementation, fetch from database
  return {
    id: tenantId,
    name: `Tenant ${tenantId}`,
    domain: `${tenantId}.ondc-seller.com`,
    settings: {
      currency: 'INR',
      timezone: 'Asia/Kolkata',
      features: ['products', 'orders', 'customers', 'analytics'],
      ondcConfig: {
        participantId: process.env[`ONDC_PARTICIPANT_ID_${tenantId.toUpperCase()}`] || process.env.ONDC_PARTICIPANT_ID,
        subscriberId: process.env[`ONDC_SUBSCRIBER_ID_${tenantId.toUpperCase()}`] || process.env.ONDC_SUBSCRIBER_ID,
        bppId: process.env[`ONDC_BPP_ID_${tenantId.toUpperCase()}`] || `${process.env.ONDC_BPP_ID || 'ondc-bpp'}-${tenantId}`,
      },
    },
    status: 'active',
    createdAt: new Date(),
    updatedAt: new Date(),
  }
}

/**
 * Main Tenant Middleware
 */
export const tenantMiddleware = async (
  req: TenantRequest,
  res: MedusaResponse,
  next: NextFunction
): Promise<void> => {
  try {
    // Skip tenant validation for health checks and public endpoints
    const skipPaths = ['/health', '/docs', '/admin/auth', '/store/auth']
    const shouldSkip = skipPaths.some(path => req.path.startsWith(path))
    
    if (shouldSkip) {
      return next()
    }

    // Extract tenant ID
    const tenantId = extractTenantId(req)
    req.tenantId = tenantId

    // Validate tenant
    const tenant = await validateTenant(tenantId)
    if (!tenant) {
      throw createError(
        `Tenant '${tenantId}' not found or inactive`,
        403,
        'INVALID_TENANT',
        { tenantId }
      )
    }

    if (tenant.status !== 'active') {
      throw createError(
        `Tenant '${tenantId}' is ${tenant.status}`,
        403,
        'TENANT_INACTIVE',
        { tenantId, status: tenant.status }
      )
    }

    // Attach tenant to request
    req.tenant = tenant

    // Add tenant context to response headers (for debugging)
    if (process.env.NODE_ENV === 'development') {
      res.setHeader('X-Tenant-ID', tenantId)
      res.setHeader('X-Tenant-Name', tenant.name)
    }

    next()
  } catch (error) {
    next(error)
  }
}

/**
 * Require Specific Tenant Feature
 */
export const requireTenantFeature = (feature: string) => {
  return (req: TenantRequest, res: MedusaResponse, next: NextFunction) => {
    if (!req.tenant) {
      return next(createError(
        'Tenant validation required',
        400,
        'TENANT_VALIDATION_REQUIRED'
      ))
    }

    const hasFeature = req.tenant.settings?.features?.includes(feature) || 
                      req.tenant.settings?.features?.includes('all')

    if (!hasFeature) {
      return next(createError(
        `Feature '${feature}' not available for tenant '${req.tenant.id}'`,
        403,
        'FEATURE_NOT_AVAILABLE',
        {
          feature,
          tenantId: req.tenant.id,
          availableFeatures: req.tenant.settings?.features || [],
        }
      ))
    }

    next()
  }
}

/**
 * Get Tenant ID from Request (Utility Function)
 */
export const getTenantId = (req: TenantRequest): string => {
  return req.tenantId || req.tenant?.id || 'default'
}

/**
 * Get Tenant Configuration (Utility Function)
 */
export const getTenantConfig = (req: TenantRequest): TenantConfig | null => {
  return req.tenant || null
}

/**
 * Validate Admin Access for Tenant
 */
export const validateAdminTenantAccess = (req: TenantRequest, res: MedusaResponse, next: NextFunction) => {
  // For admin endpoints, ensure user has access to the requested tenant
  const tenantId = getTenantId(req)
  
  // In a real implementation, check user permissions against tenant
  // For now, allow all admin access in development
  if (process.env.NODE_ENV === 'development') {
    return next()
  }

  // TODO: Implement proper admin-tenant access validation
  // Check if authenticated admin user has access to this tenant
  
  next()
}

/**
 * Error Handler for Tenant-related Errors
 */
export const tenantErrorHandler = (
  error: any,
  req: TenantRequest,
  res: MedusaResponse,
  next: NextFunction
) => {
  if (error.code && error.code.startsWith('TENANT_')) {
    return res.status(error.statusCode || 400).json({
      type: 'tenant_error',
      code: error.code,
      message: error.message,
      details: error.details || {},
    })
  }

  next(error)
}
