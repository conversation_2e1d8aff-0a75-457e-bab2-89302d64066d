-- Multi-Tenant Sample Data Seeding Script
-- Creates two tenants with complete sample data

-- ============================================================================
-- 1. TENANT CONFIGURATIONS
-- ============================================================================

INSERT INTO "tenant_config" (id, name, settings, created_at, updated_at) VALUES 
('tenant-electronics-001', 'Electronics Store', 
 '{"domain": "electronics.ondc-seller.com", "ondcConfig": {"participantId": "electronics-participant-001", "subscriberId": "electronics-subscriber-001", "bppId": "ondc-bpp-electronics-001", "domain": "electronics", "region": "IND"}, "branding": {"primaryColor": "#2563eb", "logo": "/logos/electronics-logo.png"}}', 
 NOW(), NOW()),
('tenant-fashion-002', 'Fashion Store', 
 '{"domain": "fashion.ondc-seller.com", "ondcConfig": {"participantId": "fashion-participant-002", "subscriberId": "fashion-subscriber-002", "bppId": "ondc-bpp-fashion-002", "domain": "fashion", "region": "IND"}, "branding": {"primaryColor": "#ec4899", "logo": "/logos/fashion-logo.png"}}', 
 NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET 
  name = EXCLUDED.name,
  settings = EXCLUDED.settings,
  updated_at = NOW();

-- ============================================================================
-- 2. PRODUCT CATEGORIES
-- ============================================================================

-- Electronics Categories
INSERT INTO "product_category" (id, name, handle, description, is_active, tenant_id, created_at, updated_at) VALUES 
('cat_electronics_smartphones', 'Smartphones', 'smartphones', 'Latest smartphones and mobile devices', true, 'tenant-electronics-001', NOW(), NOW()),
('cat_electronics_laptops', 'Laptops', 'laptops', 'Laptops and computers', true, 'tenant-electronics-001', NOW(), NOW()),
('cat_electronics_accessories', 'Electronics Accessories', 'electronics-accessories', 'Phone cases, chargers, and accessories', true, 'tenant-electronics-001', NOW(), NOW()),
('cat_electronics_audio', 'Audio & Headphones', 'audio-headphones', 'Headphones, speakers, and audio equipment', true, 'tenant-electronics-001', NOW(), NOW());

-- Fashion Categories
INSERT INTO "product_category" (id, name, handle, description, is_active, tenant_id, created_at, updated_at) VALUES 
('cat_fashion_mens', 'Men\'s Fashion', 'mens-fashion', 'Men\'s clothing and accessories', true, 'tenant-fashion-002', NOW(), NOW()),
('cat_fashion_womens', 'Women\'s Fashion', 'womens-fashion', 'Women\'s clothing and accessories', true, 'tenant-fashion-002', NOW(), NOW()),
('cat_fashion_shoes', 'Shoes', 'shoes', 'Footwear for men and women', true, 'tenant-fashion-002', NOW(), NOW()),
('cat_fashion_accessories', 'Fashion Accessories', 'fashion-accessories', 'Bags, jewelry, and fashion accessories', true, 'tenant-fashion-002', NOW(), NOW());

-- ============================================================================
-- 3. CUSTOMERS
-- ============================================================================

-- Electronics Store Customers
INSERT INTO "customer" (id, email, first_name, last_name, phone, has_account, tenant_id, created_at, updated_at) VALUES 
('cust_electronics_001', '<EMAIL>', 'John', 'Doe', '+91-**********', true, 'tenant-electronics-001', NOW(), NOW()),
('cust_electronics_002', '<EMAIL>', 'Sarah', 'Johnson', '+91-**********', true, 'tenant-electronics-001', NOW(), NOW()),
('cust_electronics_003', '<EMAIL>', 'Mike', 'Wilson', '+91-**********', true, 'tenant-electronics-001', NOW(), NOW());

-- Fashion Store Customers
INSERT INTO "customer" (id, email, first_name, last_name, phone, has_account, tenant_id, created_at, updated_at) VALUES 
('cust_fashion_001', '<EMAIL>', 'Emma', 'Davis', '+91-**********', true, 'tenant-fashion-002', NOW(), NOW()),
('cust_fashion_002', '<EMAIL>', 'Alex', 'Brown', '+91-**********', true, 'tenant-fashion-002', NOW(), NOW()),
('cust_fashion_003', '<EMAIL>', 'Lisa', 'Miller', '+91-**********', true, 'tenant-fashion-002', NOW(), NOW());

-- ============================================================================
-- 4. CUSTOMER ADDRESSES
-- ============================================================================

-- Electronics Store Customer Addresses
INSERT INTO "customer_address" (id, customer_id, first_name, last_name, address_1, address_2, city, province, postal_code, country_code, phone, tenant_id, created_at, updated_at) VALUES 
('addr_electronics_001', 'cust_electronics_001', 'John', 'Doe', '123 Tech Street', 'Apartment 4B', 'Bangalore', 'Karnataka', '560001', 'IN', '+91-**********', 'tenant-electronics-001', NOW(), NOW()),
('addr_electronics_002', 'cust_electronics_002', 'Sarah', 'Johnson', '456 Innovation Road', 'Floor 2', 'Mumbai', 'Maharashtra', '400001', 'IN', '+91-**********', 'tenant-electronics-001', NOW(), NOW()),
('addr_electronics_003', 'cust_electronics_003', 'Mike', 'Wilson', '789 Digital Avenue', 'Suite 301', 'Delhi', 'Delhi', '110001', 'IN', '+91-**********', 'tenant-electronics-001', NOW(), NOW());

-- Fashion Store Customer Addresses
INSERT INTO "customer_address" (id, customer_id, first_name, last_name, address_1, address_2, city, province, postal_code, country_code, phone, tenant_id, created_at, updated_at) VALUES 
('addr_fashion_001', 'cust_fashion_001', 'Emma', 'Davis', '321 Fashion Boulevard', 'Building A', 'Chennai', 'Tamil Nadu', '600001', 'IN', '+91-**********', 'tenant-fashion-002', NOW(), NOW()),
('addr_fashion_002', 'cust_fashion_002', 'Alex', 'Brown', '654 Style Street', 'Shop 15', 'Kolkata', 'West Bengal', '700001', 'IN', '+91-**********', 'tenant-fashion-002', NOW(), NOW()),
('addr_fashion_003', 'cust_fashion_003', 'Lisa', 'Miller', '987 Trend Lane', 'Flat 2C', 'Pune', 'Maharashtra', '411001', 'IN', '+91-**********', 'tenant-fashion-002', NOW(), NOW());

-- ============================================================================
-- 5. PRODUCTS
-- ============================================================================

-- Electronics Products
INSERT INTO "product" (id, title, handle, description, status, thumbnail, weight, length, height, width, hs_code, origin_country, material, tenant_id, created_at, updated_at) VALUES 
('prod_electronics_001', 'iPhone 15 Pro', 'iphone-15-pro', 'Latest iPhone with advanced camera system and A17 Pro chip', 'published', '/images/iphone-15-pro.jpg', 174, 14.67, 0.83, 7.09, '8517.12.00', 'US', 'Titanium', 'tenant-electronics-001', NOW(), NOW()),
('prod_electronics_002', 'Samsung Galaxy S24 Ultra', 'samsung-galaxy-s24-ultra', 'Premium Android smartphone with S Pen and 200MP camera', 'published', '/images/galaxy-s24-ultra.jpg', 232, 16.26, 0.86, 7.90, '8517.12.00', 'KR', 'Aluminum', 'tenant-electronics-001', NOW(), NOW()),
('prod_electronics_003', 'MacBook Pro 14"', 'macbook-pro-14', 'Powerful laptop with M3 chip for professionals', 'published', '/images/macbook-pro-14.jpg', 1600, 31.26, 1.55, 22.12, '8471.30.00', 'US', 'Aluminum', 'tenant-electronics-001', NOW(), NOW()),
('prod_electronics_004', 'Dell XPS 13', 'dell-xps-13', 'Ultra-portable laptop with InfinityEdge display', 'published', '/images/dell-xps-13.jpg', 1200, 29.57, 1.47, 19.90, '8471.30.00', 'US', 'Carbon Fiber', 'tenant-electronics-001', NOW(), NOW()),
('prod_electronics_005', 'Sony WH-1000XM5', 'sony-wh-1000xm5', 'Industry-leading noise canceling headphones', 'published', '/images/sony-wh-1000xm5.jpg', 250, 26.40, 21.60, 7.30, '8518.30.00', 'JP', 'Plastic', 'tenant-electronics-001', NOW(), NOW()),
('prod_electronics_006', 'AirPods Pro 2nd Gen', 'airpods-pro-2nd-gen', 'Wireless earbuds with active noise cancellation', 'published', '/images/airpods-pro-2.jpg', 50, 6.11, 4.50, 2.17, '8518.30.00', 'US', 'Plastic', 'tenant-electronics-001', NOW(), NOW()),
('prod_electronics_007', 'iPad Air 5th Gen', 'ipad-air-5th-gen', 'Powerful tablet with M1 chip and 10.9-inch display', 'published', '/images/ipad-air-5.jpg', 461, 24.76, 0.61, 17.85, '8471.30.00', 'US', 'Aluminum', 'tenant-electronics-001', NOW(), NOW()),
('prod_electronics_008', 'Samsung 65" QLED TV', 'samsung-65-qled-tv', '4K QLED Smart TV with Quantum Dot technology', 'published', '/images/samsung-qled-65.jpg', 25000, 144.78, 8.28, 82.90, '8528.72.00', 'KR', 'Plastic', 'tenant-electronics-001', NOW(), NOW());

-- Fashion Products
INSERT INTO "product" (id, title, handle, description, status, thumbnail, weight, length, height, width, hs_code, origin_country, material, tenant_id, created_at, updated_at) VALUES 
('prod_fashion_001', 'Men\'s Casual Shirt', 'mens-casual-shirt', 'Comfortable cotton casual shirt for everyday wear', 'published', '/images/mens-casual-shirt.jpg', 200, 70, 50, 2, '6205.20.00', 'IN', 'Cotton', 'tenant-fashion-002', NOW(), NOW()),
('prod_fashion_002', 'Women\'s Summer Dress', 'womens-summer-dress', 'Elegant floral summer dress perfect for any occasion', 'published', '/images/womens-summer-dress.jpg', 300, 120, 60, 3, '6204.44.00', 'IN', 'Polyester', 'tenant-fashion-002', NOW(), NOW()),
('prod_fashion_003', 'Men\'s Formal Blazer', 'mens-formal-blazer', 'Professional blazer for business and formal events', 'published', '/images/mens-formal-blazer.jpg', 800, 75, 55, 5, '6203.31.00', 'IN', 'Wool Blend', 'tenant-fashion-002', NOW(), NOW()),
('prod_fashion_004', 'Women\'s Denim Jeans', 'womens-denim-jeans', 'Classic blue denim jeans with perfect fit', 'published', '/images/womens-denim-jeans.jpg', 500, 100, 40, 3, '6204.52.00', 'IN', 'Denim', 'tenant-fashion-002', NOW(), NOW()),
('prod_fashion_005', 'Men\'s Running Shoes', 'mens-running-shoes', 'Lightweight running shoes with advanced cushioning', 'published', '/images/mens-running-shoes.jpg', 400, 30, 12, 11, '6403.91.00', 'IN', 'Synthetic', 'tenant-fashion-002', NOW(), NOW()),
('prod_fashion_006', 'Women\'s High Heels', 'womens-high-heels', 'Elegant high heel shoes for special occasions', 'published', '/images/womens-high-heels.jpg', 350, 25, 10, 8, '6403.91.00', 'IN', 'Leather', 'tenant-fashion-002', NOW(), NOW()),
('prod_fashion_007', 'Designer Handbag', 'designer-handbag', 'Premium leather handbag with modern design', 'published', '/images/designer-handbag.jpg', 600, 35, 25, 15, '4202.21.00', 'IN', 'Leather', 'tenant-fashion-002', NOW(), NOW()),
('prod_fashion_008', 'Fashion Sunglasses', 'fashion-sunglasses', 'Trendy sunglasses with UV protection', 'published', '/images/fashion-sunglasses.jpg', 50, 15, 5, 14, '9004.10.00', 'IN', 'Plastic', 'tenant-fashion-002', NOW(), NOW());

-- ============================================================================
-- 6. PRODUCT VARIANTS
-- ============================================================================

-- Electronics Product Variants
INSERT INTO "product_variant" (id, title, sku, barcode, ean, upc, inventory_quantity, allow_backorder, manage_inventory, weight, length, height, width, product_id, tenant_id, created_at, updated_at) VALUES 
('var_electronics_001_128gb', 'iPhone 15 Pro 128GB Natural Titanium', 'IPH15P-128-NT', '1234567890123', '1234567890123', '123456789012', 50, false, true, 174, 14.67, 0.83, 7.09, 'prod_electronics_001', 'tenant-electronics-001', NOW(), NOW()),
('var_electronics_001_256gb', 'iPhone 15 Pro 256GB Natural Titanium', 'IPH15P-256-NT', '1234567890124', '1234567890124', '123456789013', 30, false, true, 174, 14.67, 0.83, 7.09, 'prod_electronics_001', 'tenant-electronics-001', NOW(), NOW()),
('var_electronics_002_256gb', 'Galaxy S24 Ultra 256GB Titanium Gray', 'GS24U-256-TG', '1234567890125', '1234567890125', '123456789014', 40, false, true, 232, 16.26, 0.86, 7.90, 'prod_electronics_002', 'tenant-electronics-001', NOW(), NOW()),
('var_electronics_003_512gb', 'MacBook Pro 14" M3 512GB Space Gray', 'MBP14-M3-512-SG', '1234567890126', '1234567890126', '123456789015', 20, false, true, 1600, 31.26, 1.55, 22.12, 'prod_electronics_003', 'tenant-electronics-001', NOW(), NOW());

-- Fashion Product Variants
INSERT INTO "product_variant" (id, title, sku, barcode, ean, upc, inventory_quantity, allow_backorder, manage_inventory, weight, length, height, width, product_id, tenant_id, created_at, updated_at) VALUES 
('var_fashion_001_m_blue', 'Men\'s Casual Shirt Medium Blue', 'MCS-M-BLUE', '2234567890123', '2234567890123', '223456789012', 100, false, true, 200, 70, 50, 2, 'prod_fashion_001', 'tenant-fashion-002', NOW(), NOW()),
('var_fashion_001_l_white', 'Men\'s Casual Shirt Large White', 'MCS-L-WHITE', '2234567890124', '2234567890124', '223456789013', 80, false, true, 200, 70, 50, 2, 'prod_fashion_001', 'tenant-fashion-002', NOW(), NOW()),
('var_fashion_002_s_floral', 'Women\'s Summer Dress Small Floral', 'WSD-S-FLORAL', '2234567890125', '2234567890125', '223456789014', 60, false, true, 300, 120, 60, 3, 'prod_fashion_002', 'tenant-fashion-002', NOW(), NOW()),
('var_fashion_005_9_black', 'Men\'s Running Shoes Size 9 Black', 'MRS-9-BLACK', '2234567890126', '2234567890126', '223456789015', 50, false, true, 400, 30, 12, 11, 'prod_fashion_005', 'tenant-fashion-002', NOW(), NOW());

-- ============================================================================
-- 7. INVENTORY ITEMS
-- ============================================================================

-- Electronics Inventory
INSERT INTO "inventory_item" (id, sku, origin_country, hs_code, requires_shipping, tenant_id, created_at, updated_at) VALUES 
('inv_electronics_001', 'IPH15P-128-NT', 'US', '8517.12.00', true, 'tenant-electronics-001', NOW(), NOW()),
('inv_electronics_002', 'IPH15P-256-NT', 'US', '8517.12.00', true, 'tenant-electronics-001', NOW(), NOW()),
('inv_electronics_003', 'GS24U-256-TG', 'KR', '8517.12.00', true, 'tenant-electronics-001', NOW(), NOW()),
('inv_electronics_004', 'MBP14-M3-512-SG', 'US', '8471.30.00', true, 'tenant-electronics-001', NOW(), NOW());

-- Fashion Inventory
INSERT INTO "inventory_item" (id, sku, origin_country, hs_code, requires_shipping, tenant_id, created_at, updated_at) VALUES 
('inv_fashion_001', 'MCS-M-BLUE', 'IN', '6205.20.00', true, 'tenant-fashion-002', NOW(), NOW()),
('inv_fashion_002', 'MCS-L-WHITE', 'IN', '6205.20.00', true, 'tenant-fashion-002', NOW(), NOW()),
('inv_fashion_003', 'WSD-S-FLORAL', 'IN', '6204.44.00', true, 'tenant-fashion-002', NOW(), NOW()),
('inv_fashion_004', 'MRS-9-BLACK', 'IN', '6403.91.00', true, 'tenant-fashion-002', NOW(), NOW());

-- ============================================================================
-- 8. STOCK LOCATIONS
-- ============================================================================

-- Electronics Stock Locations
INSERT INTO "stock_location" (id, name, address_1, address_2, city, country_code, province, postal_code, phone, email, tenant_id, created_at, updated_at) VALUES 
('stock_electronics_001', 'Electronics Warehouse Bangalore', 'Plot 123, Electronic City', 'Phase 1', 'Bangalore', 'IN', 'Karnataka', '560100', '+91-8012345678', '<EMAIL>', 'tenant-electronics-001', NOW(), NOW()),
('stock_electronics_002', 'Electronics Store Mumbai', 'Shop 456, Tech Mall', 'Andheri East', 'Mumbai', 'IN', 'Maharashtra', '400069', '+91-8012345679', '<EMAIL>', 'tenant-electronics-001', NOW(), NOW());

-- Fashion Stock Locations
INSERT INTO "stock_location" (id, name, address_1, address_2, city, country_code, province, postal_code, phone, email, tenant_id, created_at, updated_at) VALUES 
('stock_fashion_001', 'Fashion Warehouse Delhi', 'Building 789, Fashion District', 'Sector 18', 'Gurgaon', 'IN', 'Haryana', '122015', '+91-8012345680', '<EMAIL>', 'tenant-fashion-002', NOW(), NOW()),
('stock_fashion_002', 'Fashion Boutique Chennai', 'Store 321, Express Avenue', 'T. Nagar', 'Chennai', 'IN', 'Tamil Nadu', '600017', '+91-8012345681', '<EMAIL>', 'tenant-fashion-002', NOW(), NOW());

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================

-- Verify tenant configurations
SELECT 'Tenant Configurations' as table_name, COUNT(*) as count FROM "tenant_config";

-- Verify categories by tenant
SELECT 'Categories by Tenant' as info, tenant_id, COUNT(*) as count FROM "product_category" GROUP BY tenant_id;

-- Verify customers by tenant
SELECT 'Customers by Tenant' as info, tenant_id, COUNT(*) as count FROM "customer" GROUP BY tenant_id;

-- Verify products by tenant
SELECT 'Products by Tenant' as info, tenant_id, COUNT(*) as count FROM "product" GROUP BY tenant_id;

-- Verify inventory by tenant
SELECT 'Inventory by Tenant' as info, tenant_id, COUNT(*) as count FROM "inventory_item" GROUP BY tenant_id;

-- Verify stock locations by tenant
SELECT 'Stock Locations by Tenant' as info, tenant_id, COUNT(*) as count FROM "stock_location" GROUP BY tenant_id;
