openapi: 3.0.3
info:
  title: Multi-Tenant ONDC Seller App API
  description: |
    Multi-tenant e-commerce API built on Medusa v2 for ONDC (Open Network for Digital Commerce).
    
    ## Multi-Tenancy
    This API supports multi-tenancy through the `x-tenant-id` header. Each tenant has isolated data and configurations.
    
    ## Authentication
    - Admin endpoints require Bearer token authentication
    - Store endpoints require publishable API key in `x-publishable-api-key` header
    
    ## Tenant Isolation
    All data is isolated by tenant including products, customers, orders, and configurations.
  version: 2.0.0
  contact:
    name: ONDC Seller App Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:9000
    description: Development server
  - url: https://api.ondc-seller.com
    description: Production server

security:
  - bearerAuth: []
  - publishableApiKey: []

tags:
  - name: Admin - Tenant Management
    description: Tenant configuration and management
  - name: Admin - Testing
    description: Testing and debugging endpoints
  - name: Store - Information
    description: Store information and configuration
  - name: Store - Products
    description: Product catalog and details

paths:
  /admin/tenant:
    get:
      summary: Get tenant configuration
      description: Retrieve configuration for the current tenant
      tags:
        - Admin - Tenant Management
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/TenantHeader'
      responses:
        '200':
          description: Tenant configuration retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  tenant:
                    $ref: '#/components/schemas/TenantConfig'
                  tenant_id:
                    type: string
                    example: "tenant-electronics-001"
        '404':
          description: Tenant not found
        '500':
          description: Internal server error

  /admin/test-multi-tenant:
    get:
      summary: Test multi-tenant isolation
      description: Test endpoint to verify multi-tenant data isolation
      tags:
        - Admin - Testing
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/TenantHeader'
      responses:
        '200':
          description: Multi-tenant test results
          content:
            application/json:
              schema:
                type: object
                properties:
                  tenant:
                    type: object
                    properties:
                      id:
                        type: string
                        example: "tenant-electronics-001"
                      name:
                        type: string
                        example: "Electronics Store"
                  products:
                    type: object
                    properties:
                      count:
                        type: integer
                        example: 10
                      items:
                        type: array
                        items:
                          $ref: '#/components/schemas/Product'
                      note:
                        type: string
                        example: "Standard Medusa API - tenant_id not visible but data is isolated in database"
                  customers:
                    type: object
                    properties:
                      count:
                        type: integer
                        example: 9
                      items:
                        type: array
                        items:
                          $ref: '#/components/schemas/Customer'
                      note:
                        type: string
                        example: "Standard Medusa API - tenant_id not visible but data is isolated in database"
                  tenant_id:
                    type: string
                    example: "tenant-electronics-001"
                  isolation_test:
                    type: object
                    properties:
                      api_products_returned:
                        type: integer
                        example: 10
                      api_customers_returned:
                        type: integer
                        example: 9
                      tenant_detected:
                        type: string
                        example: "tenant-electronics-001"
                      query_method:
                        type: string
                        example: "medusa_query_api"
                      note:
                        type: string
                        example: "Database has tenant isolation, but standard API doesn't expose tenant_id field"
        '500':
          description: Internal server error

  /store/test-info:
    get:
      summary: Get store information
      description: Retrieve tenant-specific store information and configuration
      tags:
        - Store - Information
      security:
        - publishableApiKey: []
      parameters:
        - $ref: '#/components/parameters/TenantHeader'
        - $ref: '#/components/parameters/PublishableApiKeyHeader'
      responses:
        '200':
          description: Store information retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  store:
                    $ref: '#/components/schemas/StoreInfo'
                  tenant_id:
                    type: string
                    example: "tenant-electronics-001"
                  api_version:
                    type: string
                    example: "v2"
                  multi_tenant:
                    type: boolean
                    example: true
                  timestamp:
                    type: string
                    format: date-time
        '500':
          description: Internal server error

  /store/test-products:
    get:
      summary: Get store products
      description: Retrieve tenant-specific product catalog with filtering
      tags:
        - Store - Products
      security:
        - publishableApiKey: []
      parameters:
        - $ref: '#/components/parameters/TenantHeader'
        - $ref: '#/components/parameters/PublishableApiKeyHeader'
      responses:
        '200':
          description: Products retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  store:
                    type: object
                    properties:
                      id:
                        type: string
                        example: "tenant-electronics-001"
                      name:
                        type: string
                        example: "Electronics Store"
                      domain:
                        type: string
                        example: "electronics.ondc-seller.com"
                  products:
                    type: array
                    items:
                      $ref: '#/components/schemas/Product'
                  count:
                    type: integer
                    example: 5
                  tenant_id:
                    type: string
                    example: "tenant-electronics-001"
                  total_products_in_system:
                    type: integer
                    example: 10
                  filtered_for_tenant:
                    type: integer
                    example: 5
                  note:
                    type: string
                    example: "Store API with tenant-based product filtering"
        '500':
          description: Internal server error

  /store/test-products/{id}:
    get:
      summary: Get product details
      description: Retrieve detailed information for a specific product
      tags:
        - Store - Products
      security:
        - publishableApiKey: []
      parameters:
        - $ref: '#/components/parameters/TenantHeader'
        - $ref: '#/components/parameters/PublishableApiKeyHeader'
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: string
            example: "prod_electronics_001"
      responses:
        '200':
          description: Product details retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  product:
                    $ref: '#/components/schemas/ProductDetails'
                  tenant_id:
                    type: string
                    example: "tenant-electronics-001"
                  store:
                    type: object
                    properties:
                      id:
                        type: string
                        example: "tenant-electronics-001"
                      name:
                        type: string
                        example: "Electronics Store"
                      domain:
                        type: string
                        example: "electronics.ondc-seller.com"
                  api_version:
                    type: string
                    example: "v2"
                  multi_tenant:
                    type: boolean
                    example: true
        '404':
          description: Product not found or doesn't belong to tenant
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: "Product not found in this store"
                  product_id:
                    type: string
                    example: "prod_fashion_001"
                  tenant_id:
                    type: string
                    example: "tenant-electronics-001"
                  note:
                    type: string
                    example: "Product exists but doesn't belong to this tenant"
        '500':
          description: Internal server error

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token for admin authentication
    publishableApiKey:
      type: apiKey
      in: header
      name: x-publishable-api-key
      description: Publishable API key for store endpoints

  parameters:
    TenantHeader:
      name: x-tenant-id
      in: header
      required: true
      description: Tenant identifier for multi-tenant isolation
      schema:
        type: string
        example: "tenant-electronics-001"
        enum:
          - "tenant-electronics-001"
          - "tenant-fashion-002"
          - "default"

    PublishableApiKeyHeader:
      name: x-publishable-api-key
      in: header
      required: true
      description: Publishable API key for store access
      schema:
        type: string
        example: "pk_51719ff15f2d615335059dc2bad507d3446466d7d6e2af5c777cf07d7ac53af0"

  schemas:
    TenantConfig:
      type: object
      properties:
        id:
          type: string
          example: "tenant-electronics-001"
        name:
          type: string
          example: "Electronics Store"
        settings:
          type: object
          properties:
            currency:
              type: string
              example: "INR"
            timezone:
              type: string
              example: "Asia/Kolkata"
            features:
              type: array
              items:
                type: string
              example: ["products", "orders", "customers", "analytics", "inventory"]
            ondcConfig:
              type: object
              properties:
                participantId:
                  type: string
                  example: "electronics-participant-001"
                subscriberId:
                  type: string
                  example: "electronics-subscriber-001"
                bppId:
                  type: string
                  example: "ondc-bpp-electronics-001"

    StoreInfo:
      type: object
      properties:
        id:
          type: string
          example: "tenant-electronics-001"
        name:
          type: string
          example: "Electronics Store"
        domain:
          type: string
          example: "electronics.ondc-seller.com"
        currency:
          type: string
          example: "INR"
        timezone:
          type: string
          example: "Asia/Kolkata"
        region:
          type: string
          example: "India"
        features:
          type: array
          items:
            type: string
          example: ["electronics", "gadgets", "smartphones", "laptops", "audio"]
        branding:
          type: object
          properties:
            primaryColor:
              type: string
              example: "#2563eb"
            logo:
              type: string
              example: "/logos/electronics-logo.png"
        contact:
          type: object
          properties:
            email:
              type: string
              example: "<EMAIL>"
            phone:
              type: string
              example: "+91-1234567890"
            address:
              type: object
              properties:
                street:
                  type: string
                  example: "123 Commerce Street"
                city:
                  type: string
                  example: "Mumbai"
                state:
                  type: string
                  example: "Maharashtra"
                country:
                  type: string
                  example: "India"
                pincode:
                  type: string
                  example: "400001"
        ondcConfig:
          type: object
          properties:
            participantId:
              type: string
              example: "electronics-participant-001"
            subscriberId:
              type: string
              example: "electronics-subscriber-001"
            bppId:
              type: string
              example: "ondc-bpp-electronics-001"
            domain:
              type: string
              example: "electronics"
            region:
              type: string
              example: "IND"

    Product:
      type: object
      properties:
        id:
          type: string
          example: "prod_electronics_001"
        title:
          type: string
          example: "iPhone 15 Pro"
        description:
          type: string
          example: "Latest iPhone with advanced features"
        handle:
          type: string
          example: "iphone-15-pro"
        status:
          type: string
          example: "published"
        thumbnail:
          type: string
          example: "/images/iphone-15-pro.jpg"

    ProductDetails:
      allOf:
        - $ref: '#/components/schemas/Product'
        - type: object
          properties:
            store:
              type: object
              properties:
                id:
                  type: string
                  example: "tenant-electronics-001"
                name:
                  type: string
                  example: "Electronics Store"
                domain:
                  type: string
                  example: "electronics.ondc-seller.com"
            tenant_id:
              type: string
              example: "tenant-electronics-001"
            availability:
              type: string
              example: "in_stock"
            price:
              type: object
              properties:
                amount:
                  type: integer
                  example: 99999
                currency:
                  type: string
                  example: "INR"
            shipping:
              type: object
              properties:
                free_shipping:
                  type: boolean
                  example: true
                estimated_delivery:
                  type: string
                  example: "3-5 business days"
                regions:
                  type: array
                  items:
                    type: string
                  example: ["India"]
            warranty:
              type: string
              example: "1 year manufacturer warranty"
            weight:
              type: number
              example: 0.2
            length:
              type: number
              example: 15.0
            height:
              type: number
              example: 7.5
            width:
              type: number
              example: 0.8
            hs_code:
              type: string
              example: "8517120000"
            origin_country:
              type: string
              example: "CN"
            material:
              type: string
              example: "Aluminum, Glass"

    Customer:
      type: object
      properties:
        id:
          type: string
          example: "cust_electronics_001"
        email:
          type: string
          example: "<EMAIL>"
        first_name:
          type: string
          example: "John"
        last_name:
          type: string
          example: "Doe"

    Error:
      type: object
      properties:
        error:
          type: string
          example: "Resource not found"
        message:
          type: string
          example: "The requested resource could not be found"
        tenant_id:
          type: string
          example: "tenant-electronics-001"
