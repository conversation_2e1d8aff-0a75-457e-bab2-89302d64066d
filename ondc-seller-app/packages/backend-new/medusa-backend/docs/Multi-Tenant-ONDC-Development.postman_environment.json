{"id": "multi-tenant-ondc-dev-env", "name": "Multi-Tenant ONDC Development", "values": [{"key": "base_url", "value": "http://localhost:9000", "type": "default", "enabled": true}, {"key": "admin_email", "value": "<EMAIL>", "type": "default", "enabled": true}, {"key": "admin_password", "value": "supersecret", "type": "secret", "enabled": true}, {"key": "admin_token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY3Rvcl9pZCI6InVzZXJfMDFKWjRWVkdFSlg2S1RRM1JaTUI3OFk1TTEiLCJhY3Rvcl90eXBlIjoidXNlciIsImF1dGhfaWRlbnRpdHlfaWQiOiJhdXRoaWRfMDFKWjRWVkdKSlRLVzNSWUdNTUUwRVg0NDMiLCJhcHBfbWV0YWRhdGEiOnsidXNlcl9pZCI6InVzZXJfMDFKWjRWVkdFSlg2S1RRM1JaTUI3OFk1TTEifSwiaWF0IjoxNzUxNDQ5NDA2LCJleHAiOjE3NTE1MzU4MDZ9.8UR1_CpBrJFgkdNeU2iU-_8x2EvcMidMTP8bt8Ok9pA", "type": "secret", "enabled": true}, {"key": "publishable_api_key", "value": "pk_51719ff15f2d615335059dc2bad507d3446466d7d6e2af5c777cf07d7ac53af0", "type": "secret", "enabled": true}, {"key": "electronics_tenant_id", "value": "tenant-electronics-001", "type": "default", "enabled": true}, {"key": "fashion_tenant_id", "value": "tenant-fashion-002", "type": "default", "enabled": true}, {"key": "default_tenant_id", "value": "default", "type": "default", "enabled": true}, {"key": "electronics_product_id", "value": "prod_electronics_001", "type": "default", "enabled": true}, {"key": "fashion_product_id", "value": "prod_fashion_001", "type": "default", "enabled": true}, {"key": "electronics_customer_id", "value": "cust_electronics_001", "type": "default", "enabled": true}, {"key": "fashion_customer_id", "value": "cust_fashion_001", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-01-02T12:00:00.000Z", "_postman_exported_using": "Postman/10.0.0"}