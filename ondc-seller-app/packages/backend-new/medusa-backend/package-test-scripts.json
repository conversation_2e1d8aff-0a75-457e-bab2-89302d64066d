{"name": "medusa-backend-api-tests", "version": "1.0.0", "description": "Comprehensive API testing suite for Medusa v2 ONDC Seller App backend", "scripts": {"test:api": "node comprehensive-api-test.js", "test:api:quick": "./test-api-endpoints.sh", "test:security": "node comprehensive-api-test.js --security-only", "test:tenant-isolation": "node -e \"require('./comprehensive-api-test.js').testMultiTenantIsolation()\"", "test:watch": "nodemon comprehensive-api-test.js", "test:ci": "node comprehensive-api-test.js --ci", "report:generate": "node -e \"console.log('Test results available in api-test-results.json')\"", "report:view": "cat api-test-results.json | jq .", "health:check": "curl -s http://localhost:9000/health || echo 'Server not running'", "setup:test-env": "echo 'Setting up test environment...' && npm run health:check"}, "dependencies": {"axios": "^1.6.0"}, "devDependencies": {"nodemon": "^3.0.0"}, "keywords": ["medusa", "api-testing", "multi-tenant", "e-commerce", "ondc", "security-testing"], "author": "ONDC Seller App Team", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/your-org/ondc-seller-app.git"}, "bugs": {"url": "https://github.com/your-org/ondc-seller-app/issues"}, "homepage": "https://github.com/your-org/ondc-seller-app#readme"}