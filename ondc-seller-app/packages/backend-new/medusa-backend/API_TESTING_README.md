# 🧪 API Testing Suite for Medusa v2 ONDC Seller App

This directory contains comprehensive API testing tools for verifying the functionality, security, and multi-tenant isolation of the Medusa v2 backend.

## 📁 Files Overview

### Test Scripts
- **`comprehensive-api-test.js`** - Main automated test suite (Node.js)
- **`test-api-endpoints.sh`** - Quick shell script for basic endpoint testing
- **`api-endpoints-verification.spec.ts`** - Jest integration test (for future use)
- **`direct-api-test.spec.ts`** - Direct HTTP API tests

### Reports & Documentation
- **`API_VERIFICATION_REPORT.md`** - Detailed verification report
- **`FINAL_API_VERIFICATION_SUMMARY.md`** - Executive summary with critical findings
- **`api-test-results.json`** - Latest automated test results
- **`MULTI_TENANCY_TEST_RESULTS.md`** - Multi-tenant specific test results

### Configuration
- **`package-test-scripts.json`** - NPM scripts for test automation
- **`.env`** - Environment configuration

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ installed
- Medusa backend server running on localhost:9000
- PostgreSQL database connected and seeded

### Running Tests

#### 1. Quick Health Check
```bash
# Check if server is running
curl -s http://localhost:9000/health
```

#### 2. Basic Endpoint Testing (Shell Script)
```bash
# Make executable and run
chmod +x test-api-endpoints.sh
./test-api-endpoints.sh
```

#### 3. Comprehensive Testing (Node.js)
```bash
# Install dependencies
npm install axios

# Run full test suite
node comprehensive-api-test.js

# View results
cat api-test-results.json | jq .
```

#### 4. Continuous Testing
```bash
# Install nodemon for watching
npm install -g nodemon

# Run tests on file changes
nodemon comprehensive-api-test.js
```

## 📊 Test Categories

### 🔐 Authentication Tests
- Admin login/logout
- JWT token validation
- Invalid credentials handling
- User profile retrieval

### 🏢 Multi-Tenant Configuration Tests
- Tenant switching via headers
- Tenant-specific configurations
- ONDC settings per tenant
- **🚨 CRITICAL: Tenant isolation verification**

### 📦 Products API Tests
- Product CRUD operations
- Product listing (admin & store)
- **🚨 CRITICAL: Cross-tenant product access prevention**
- Product search and filtering

### 🛒 Cart API Tests
- Cart creation and retrieval
- Item management
- Shipping address handling
- Multi-tenant cart isolation

### 👥 Customer API Tests
- Customer registration
- Customer authentication
- Profile management
- Admin customer operations

### 📋 Orders API Tests
- Order creation and management
- Order status updates
- Order history retrieval
- Multi-tenant order isolation

### 🏷️ Categories API Tests
- Category listing
- Category hierarchy
- Store-facing category endpoints

### ⚠️ Error Handling Tests
- Unauthorized access rejection
- Non-existent resource handling
- Invalid input validation
- Proper HTTP status codes

## 🔍 Security Testing

### Multi-Tenant Isolation Testing
The most critical aspect of testing focuses on verifying that data is properly isolated between tenants:

```bash
# Test cross-tenant product access (SHOULD FAIL)
curl -H "Authorization: Bearer $TOKEN" \
     -H "x-tenant-id: tenant-fashion-002" \
     http://localhost:9000/admin/products/ELECTRONICS_PRODUCT_ID
# Expected: 404 Not Found
# Actual: 200 OK (SECURITY BREACH!)
```

### Current Security Status
❌ **CRITICAL SECURITY ISSUE FOUND**
- Multi-tenant data isolation is completely broken
- Products from one tenant are accessible from other tenants
- This represents a critical security vulnerability

## 📈 Test Results Interpretation

### Exit Codes
- **0** - All tests passed
- **1** - Some tests failed (non-critical)
- **2** - Critical security issues found
- **3** - Test suite setup failed

### Test Status Indicators
- ✅ **PASS** - Test passed successfully
- ❌ **FAIL** - Test failed (non-critical)
- 🚨 **CRITICAL FAIL** - Critical security issue found

### Success Rate Thresholds
- **90-100%** - Production ready
- **80-89%** - Needs minor fixes
- **70-79%** - Needs significant fixes
- **<70%** - Not ready for production

**Current Status: 77.78% (Not ready for production due to critical security issue)**

## 🛠️ Automated Test Reports

### JSON Report Structure
```json
{
  "timestamp": "2025-07-03T07:34:49.527Z",
  "summary": {
    "total": 18,
    "passed": 14,
    "failed": 4,
    "critical_failures": 1,
    "success_rate": "77.78%"
  },
  "status": "CRITICAL_ISSUES",
  "tests": [...]
}
```

### Report Files Generated
- **`api-test-results.json`** - Machine-readable test results
- **`API_VERIFICATION_REPORT.md`** - Human-readable detailed report
- **`FINAL_API_VERIFICATION_SUMMARY.md`** - Executive summary

## 🔧 Customizing Tests

### Adding New Tests
1. Edit `comprehensive-api-test.js`
2. Add new test methods to the `APITester` class
3. Call the new test method in the `run()` function

### Modifying Test Configuration
- Update `BASE_URL` for different environments
- Change `PUBLISHABLE_KEY` for different API keys
- Modify tenant list in test data

### Environment-Specific Testing
```bash
# Test against staging
BASE_URL=https://staging-api.example.com node comprehensive-api-test.js

# Test with different API key
PUBLISHABLE_KEY=pk_staging_123 node comprehensive-api-test.js
```

## 🚨 Critical Issues Found

### 1. Multi-Tenant Data Isolation Failure
**Status:** ❌ **BLOCKING PRODUCTION**
- Products accessible across all tenants
- Complete security breach
- Must be fixed before any production deployment

### 2. Cart Retrieval Issues
**Status:** ⚠️ **Needs Investigation**
- Cart creation works but retrieval fails
- Tenant-specific cart ID issues

### 3. Customer Registration Issues
**Status:** ⚠️ **Needs Configuration**
- Customer creation returns 401 errors
- Publishable API key configuration issue

## 📋 Next Steps

### Immediate Actions Required
1. **🚨 URGENT:** Fix multi-tenant isolation
2. **🔍 AUDIT:** Complete security review
3. **🧪 TEST:** Re-run all tests after fixes
4. **📋 VERIFY:** Security compliance check

### Recommended Testing Schedule
- **Daily:** Quick health checks
- **Weekly:** Full test suite execution
- **Before Deployment:** Complete security audit
- **After Changes:** Targeted test execution

## 📞 Support

For issues with the testing suite:
1. Check server is running: `curl http://localhost:9000/health`
2. Verify database connectivity
3. Check environment variables
4. Review test logs in `api-test-results.json`

**Remember: Do not deploy to production until all critical security issues are resolved!**
