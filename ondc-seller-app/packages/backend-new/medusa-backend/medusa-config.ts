import { loadEnv, defineConfig } from '@medusajs/framework/utils'

loadEnv(process.env.NODE_ENV || 'development', process.cwd())

export default defineConfig({
  projectConfig: {
    databaseUrl: process.env.DATABASE_URL,
    http: {
      storeCors: process.env.STORE_CORS!,
      adminCors: process.env.ADMIN_CORS!,
      authCors: process.env.AUTH_CORS!,
      jwtSecret: process.env.JWT_SECRET || "supersecret",
      cookieSecret: process.env.COOKIE_SECRET || "supersecret",
      cors: {
        origin: [
          'http://localhost:3000',
          'http://localhost:3001',
          'http://localhost:3002',
          'http://localhost:5173',
          'http://localhost:9000',
          'https://docs.medusajs.com'
        ],
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
        allowedHeaders: [
          'Content-Type',
          'Authorization',
          'x-medusa-access-token',
          'x-publishable-api-key',
          'x-tenant-id',
          'Accept',
          'Origin',
          'X-Requested-With',
          'Cache-Control'
        ],
        credentials: true,
        exposedHeaders: ['X-Tenant-ID', 'X-Tenant-Name']
      }
    }
  },

  // ✅ REGISTER CORE MODULES HERE
  modules: {
    cart: {
      resolve: "@medusajs/cart",
      options: {}
    },
    order: {
      resolve: "@medusajs/order",
      options: {}
    },
    customer: {
      resolve: "@medusajs/customer",
      options: {}
    },
    payment: {
      resolve: "@medusajs/payment",
      options: {}
    }
  }
})
