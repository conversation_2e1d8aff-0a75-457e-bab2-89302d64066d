import { loadEnv, defineConfig } from '@medusajs/framework/utils'

loadEnv(process.env.NODE_ENV || 'development', process.cwd())

export default defineConfig({
  projectConfig: {
    databaseUrl: process.env.DATABASE_URL,
    http: {
      storeCors: process.env.STORE_CORS!,
      adminCors: process.env.ADMIN_CORS!,
      authCors: process.env.AUTH_CORS!,
      jwtSecret: process.env.JWT_SECRET || "supersecret",
      cookieSecret: process.env.COOKIE_SECRET || "supersecret",
      cors: {
        origin: [
          'http://localhost:3000',
          'http://localhost:3001',
          'http://localhost:3002',
          'http://localhost:5173',
          'http://localhost:9000',
          'https://docs.medusajs.com'
        ],
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
        allowedHeaders: [
          'Content-Type',
          'Authorization',
          'x-medusa-access-token',
          'x-publishable-api-key',
          'x-tenant-id',
          'Accept',
          'Origin',
          'X-Requested-With',
          'Cache-Control'
        ],
        credentials: true,
        exposedHeaders: ['X-Tenant-ID', 'X-Tenant-Name']
      }
    }
  },

  // ✅ REGISTER CORE MODULES HERE
  modules: {
    // Core e-commerce modules
    cart: {
      resolve: "@medusajs/cart",
      options: {
        // Enable cart persistence and recovery
        ttl: 7 * 24 * 60 * 60 * 1000, // 7 days in milliseconds
      }
    },
    order: {
      resolve: "@medusajs/order",
      options: {
        // Order processing configuration
        draft_order_ttl: 24 * 60 * 60 * 1000, // 24 hours for draft orders
      }
    },
    customer: {
      resolve: "@medusajs/customer",
      options: {
        // Customer management configuration
        password_hash_rounds: 12,
      }
    },
    payment: {
      resolve: "@medusajs/payment",
      options: {
        // Payment processing configuration
        providers: [
          {
            resolve: "@medusajs/payment-manual",
            id: "manual",
            options: {
              // Manual payment for testing and COD
              name: "Manual Payment",
              description: "Manual payment processing for testing and Cash on Delivery"
            }
          }
        ]
      }
    },
    // Product and inventory management
    product: {
      resolve: "@medusajs/product",
      options: {}
    },
    pricing: {
      resolve: "@medusajs/pricing",
      options: {}
    },
    inventory: {
      resolve: "@medusajs/inventory",
      options: {}
    },
    stock_location: {
      resolve: "@medusajs/stock-location",
      options: {}
    },
    // Fulfillment and shipping
    fulfillment: {
      resolve: "@medusajs/fulfillment",
      options: {
        providers: [
          {
            resolve: "@medusajs/fulfillment-manual",
            id: "manual",
            options: {
              name: "Manual Fulfillment",
              description: "Manual fulfillment for custom shipping"
            }
          }
        ]
      }
    },
    // Sales channels and regions
    sales_channel: {
      resolve: "@medusajs/sales-channel",
      options: {}
    },
    region: {
      resolve: "@medusajs/region",
      options: {}
    },
    // Tax calculation
    tax: {
      resolve: "@medusajs/tax",
      options: {
        providers: [
          {
            resolve: "@medusajs/tax-calculation-system",
            id: "system",
            options: {
              // System tax calculation for Indian GST
              tax_lines_args: {
                calculation_context: "automatic"
              }
            }
          }
        ]
      }
    },
    // Currency support
    currency: {
      resolve: "@medusajs/currency",
      options: {}
    },
    // File storage for product images
    file: {
      resolve: "@medusajs/file",
      options: {
        providers: [
          {
            resolve: "@medusajs/file-local",
            id: "local",
            options: {
              upload_dir: "uploads",
              backend_url: process.env.BACKEND_URL || "http://localhost:9000"
            }
          }
        ]
      }
    },
    // Notification system
    notification: {
      resolve: "@medusajs/notification",
      options: {
        providers: [
          {
            resolve: "@medusajs/notification-local",
            id: "local",
            options: {}
          }
        ]
      }
    },
    // User management
    user: {
      resolve: "@medusajs/user",
      options: {}
    },
    // API key management
    api_key: {
      resolve: "@medusajs/api-key",
      options: {}
    },
    // Store management
    store: {
      resolve: "@medusajs/store",
      options: {}
    }
  }
})
