#!/usr/bin/env node

/**
 * Comprehensive API Endpoints Test Suite
 * Tests all core e-commerce endpoints with real database operations
 * Focuses on multi-tenant isolation and security verification
 */

const axios = require('axios');
const fs = require('fs');

const BASE_URL = 'http://localhost:9000';
const PUBLISHABLE_KEY = 'pk_51719ff15f2d615335059dc2bad507d3446466d7d6e2af5c777cf07d7ac53af0';

class APITester {
  constructor() {
    this.results = {
      total: 0,
      passed: 0,
      failed: 0,
      critical: 0,
      tests: []
    };
    this.adminToken = null;
    this.testData = {};
  }

  async log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const colors = {
      info: '\x1b[36m',
      success: '\x1b[32m',
      error: '\x1b[31m',
      warning: '\x1b[33m',
      critical: '\x1b[41m\x1b[37m',
      reset: '\x1b[0m'
    };
    
    console.log(`${colors[type]}[${timestamp}] ${message}${colors.reset}`);
  }

  async test(name, testFn, critical = false) {
    this.results.total++;
    try {
      await this.log(`Testing: ${name}`, 'info');
      await testFn();
      this.results.passed++;
      this.results.tests.push({ name, status: 'PASS', critical });
      await this.log(`✅ PASS: ${name}`, 'success');
    } catch (error) {
      this.results.failed++;
      if (critical) {
        this.results.critical++;
        this.results.tests.push({ name, status: 'CRITICAL FAIL', error: error.message, critical });
        await this.log(`🚨 CRITICAL FAIL: ${name} - ${error.message}`, 'critical');
      } else {
        this.results.tests.push({ name, status: 'FAIL', error: error.message, critical });
        await this.log(`❌ FAIL: ${name} - ${error.message}`, 'error');
      }
    }
  }

  async setup() {
    await this.log('Setting up test environment...', 'info');
    
    // Get admin token
    try {
      const response = await axios.post(`${BASE_URL}/auth/user/emailpass`, {
        email: '<EMAIL>',
        password: 'supersecret'
      });
      this.adminToken = response.data.token;
      await this.log('Admin authentication successful', 'success');
    } catch (error) {
      throw new Error(`Failed to authenticate admin: ${error.message}`);
    }
  }

  async testAuthentication() {
    await this.test('Admin Authentication', async () => {
      const response = await axios.post(`${BASE_URL}/auth/user/emailpass`, {
        email: '<EMAIL>',
        password: 'supersecret'
      });
      
      if (!response.data.token) {
        throw new Error('No token received');
      }
    });

    await this.test('Invalid Credentials Rejection', async () => {
      try {
        await axios.post(`${BASE_URL}/auth/user/emailpass`, {
          email: '<EMAIL>',
          password: 'wrongpassword'
        });
        throw new Error('Should have rejected invalid credentials');
      } catch (error) {
        if (error.response && error.response.status === 401) {
          return; // Expected behavior
        }
        throw error;
      }
    });

    await this.test('Get Admin User Info', async () => {
      const response = await axios.get(`${BASE_URL}/admin/users/me`, {
        headers: { Authorization: `Bearer ${this.adminToken}` }
      });
      
      if (!response.data.user || !response.data.user.email) {
        throw new Error('Invalid user data returned');
      }
    });
  }

  async testMultiTenantConfiguration() {
    const tenants = ['default', 'tenant-electronics-001', 'tenant-fashion-002', 'tenant-books-003'];
    
    for (const tenant of tenants) {
      await this.test(`Tenant Configuration: ${tenant}`, async () => {
        const headers = { Authorization: `Bearer ${this.adminToken}` };
        if (tenant !== 'default') {
          headers['x-tenant-id'] = tenant;
        }
        
        const response = await axios.get(`${BASE_URL}/admin/tenant`, { headers });
        
        if (!response.data.success || !response.data.tenant) {
          throw new Error('Invalid tenant configuration response');
        }
      });
    }
  }

  async testProductsAPI() {
    await this.test('List Products (Admin)', async () => {
      const response = await axios.get(`${BASE_URL}/admin/products`, {
        headers: { Authorization: `Bearer ${this.adminToken}` }
      });
      
      if (!Array.isArray(response.data.products)) {
        throw new Error('Products should be an array');
      }
    });

    await this.test('List Products (Store)', async () => {
      const response = await axios.get(`${BASE_URL}/store/products`, {
        headers: { 'x-publishable-api-key': PUBLISHABLE_KEY }
      });
      
      if (!Array.isArray(response.data.products)) {
        throw new Error('Products should be an array');
      }
    });

    // Test multi-tenant product isolation (CRITICAL TEST)
    await this.test('Multi-Tenant Product Isolation', async () => {
      // Get products from electronics tenant
      const electronicsResponse = await axios.get(`${BASE_URL}/admin/products`, {
        headers: {
          Authorization: `Bearer ${this.adminToken}`,
          'x-tenant-id': 'tenant-electronics-001'
        }
      });
      
      if (electronicsResponse.data.products.length === 0) {
        throw new Error('No products found in electronics tenant for isolation test');
      }
      
      const productId = electronicsResponse.data.products[0].id;
      this.testData.electronicsProductId = productId;
      
      // Try to access from fashion tenant - should fail
      try {
        const fashionResponse = await axios.get(`${BASE_URL}/admin/products/${productId}`, {
          headers: {
            Authorization: `Bearer ${this.adminToken}`,
            'x-tenant-id': 'tenant-fashion-002'
          }
        });
        
        if (fashionResponse.status === 200) {
          throw new Error('CRITICAL SECURITY BREACH: Product accessible across tenants');
        }
      } catch (error) {
        if (error.response && error.response.status === 404) {
          return; // Expected behavior - product should not be accessible
        }
        throw error;
      }
    }, true); // Mark as critical test
  }

  async testProductsAPI() {
    await this.test('List Products (Admin)', async () => {
      const response = await axios.get(`${BASE_URL}/admin/products`, {
        headers: { Authorization: `Bearer ${this.adminToken}` }
      });

      if (!Array.isArray(response.data.products)) {
        throw new Error('Products should be an array');
      }
    });

    await this.test('List Products (Store)', async () => {
      const response = await axios.get(`${BASE_URL}/store/products`, {
        headers: { 'x-publishable-api-key': PUBLISHABLE_KEY }
      });

      if (!Array.isArray(response.data.products)) {
        throw new Error('Products should be an array');
      }

      // Store first product for single product test
      if (response.data.products.length > 0) {
        this.testData.storeProductId = response.data.products[0].id;
        this.testData.storeVariantId = response.data.products[0].variants?.[0]?.id;
      }
    });

    await this.test('Get Single Product (Store)', async () => {
      if (!this.testData.storeProductId) {
        throw new Error('No product ID available from previous test');
      }

      const response = await axios.get(`${BASE_URL}/store/products/${this.testData.storeProductId}`, {
        headers: { 'x-publishable-api-key': PUBLISHABLE_KEY }
      });

      if (!response.data.product || response.data.product.id !== this.testData.storeProductId) {
        throw new Error('Single product retrieval failed');
      }
    });

    // Test multi-tenant product isolation (CRITICAL TEST)
    await this.test('Multi-Tenant Product Isolation', async () => {
      // Get products from electronics tenant
      const electronicsResponse = await axios.get(`${BASE_URL}/admin/products`, {
        headers: {
          Authorization: `Bearer ${this.adminToken}`,
          'x-tenant-id': 'tenant-electronics-001'
        }
      });

      if (electronicsResponse.data.products.length === 0) {
        throw new Error('No products found in electronics tenant for isolation test');
      }

      const productId = electronicsResponse.data.products[0].id;
      this.testData.electronicsProductId = productId;

      // Try to access from fashion tenant - should fail
      try {
        const fashionResponse = await axios.get(`${BASE_URL}/admin/products/${productId}`, {
          headers: {
            Authorization: `Bearer ${this.adminToken}`,
            'x-tenant-id': 'tenant-fashion-002'
          }
        });

        if (fashionResponse.status === 200) {
          throw new Error('CRITICAL SECURITY BREACH: Product accessible across tenants');
        }
      } catch (error) {
        if (error.response && error.response.status === 404) {
          return; // Expected behavior - product should not be accessible
        }
        throw error;
      }
    }, true); // Mark as critical test
  }

  async testCartAPI() {
    await this.test('Create Cart', async () => {
      const response = await axios.post(`${BASE_URL}/store/carts`, {}, {
        headers: {
          'x-publishable-api-key': PUBLISHABLE_KEY,
          'Content-Type': 'application/json'
        }
      });

      if (!response.data.cart || !response.data.cart.id) {
        throw new Error('Cart creation failed');
      }

      this.testData.cartId = response.data.cart.id;
    });

    await this.test('Retrieve Cart', async () => {
      if (!this.testData.cartId) {
        throw new Error('No cart ID available from previous test');
      }

      const response = await axios.get(`${BASE_URL}/store/carts/${this.testData.cartId}`, {
        headers: { 'x-publishable-api-key': PUBLISHABLE_KEY }
      });

      if (!response.data.cart) {
        throw new Error('Cart retrieval failed');
      }
    });

    await this.test('Add Product to Cart', async () => {
      if (!this.testData.cartId) {
        throw new Error('No cart ID available');
      }
      if (!this.testData.storeVariantId) {
        throw new Error('No variant ID available for adding to cart');
      }

      const itemData = {
        variant_id: this.testData.storeVariantId,
        quantity: 2
      };

      const response = await axios.post(`${BASE_URL}/store/carts/${this.testData.cartId}/line-items`, itemData, {
        headers: {
          'x-publishable-api-key': PUBLISHABLE_KEY,
          'Content-Type': 'application/json'
        }
      });

      if (!response.data.cart || !response.data.cart.items || response.data.cart.items.length === 0) {
        throw new Error('Adding product to cart failed');
      }

      this.testData.lineItemId = response.data.cart.items[0].id;
    });

    await this.test('Update Cart Item Quantity', async () => {
      if (!this.testData.cartId || !this.testData.lineItemId) {
        throw new Error('No cart ID or line item ID available');
      }

      const updateData = { quantity: 3 };

      const response = await axios.post(`${BASE_URL}/store/carts/${this.testData.cartId}/line-items/${this.testData.lineItemId}`, updateData, {
        headers: {
          'x-publishable-api-key': PUBLISHABLE_KEY,
          'Content-Type': 'application/json'
        }
      });

      if (!response.data.cart) {
        throw new Error('Updating cart item quantity failed');
      }
    });

    await this.test('Add Shipping Address to Cart', async () => {
      if (!this.testData.cartId) {
        throw new Error('No cart ID available');
      }

      const addressData = {
        first_name: 'John',
        last_name: 'Doe',
        address_1: '123 Test Street',
        city: 'Mumbai',
        postal_code: '400001',
        country_code: 'IN',
        phone: '+919876543210'
      };

      const response = await axios.post(`${BASE_URL}/store/carts/${this.testData.cartId}/shipping-address`, addressData, {
        headers: {
          'x-publishable-api-key': PUBLISHABLE_KEY,
          'Content-Type': 'application/json'
        }
      });

      if (!response.data.cart || !response.data.cart.shipping_address) {
        throw new Error('Adding shipping address to cart failed');
      }
    });
  }

  async testCustomersAPI() {
    await this.test('List Customers (Admin)', async () => {
      const response = await axios.get(`${BASE_URL}/admin/customers`, {
        headers: { Authorization: `Bearer ${this.adminToken}` }
      });
      
      if (!Array.isArray(response.data.customers)) {
        throw new Error('Customers should be an array');
      }
    });

    await this.test('Create Customer', async () => {
      const customerData = {
        email: `test-${Date.now()}@example.com`,
        password: 'testpass123',
        first_name: 'Test',
        last_name: 'Customer'
      };
      
      const response = await axios.post(`${BASE_URL}/store/customers`, customerData, {
        headers: {
          'x-publishable-api-key': PUBLISHABLE_KEY,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.data.customer || !response.data.customer.id) {
        throw new Error('Customer creation failed');
      }
      
      this.testData.customerId = response.data.customer.id;
      this.testData.customerEmail = customerData.email;
      this.testData.customerPassword = customerData.password;
    });
  }

  async testCheckoutAPI() {
    await this.test('Get Shipping Options', async () => {
      if (!this.testData.cartId) {
        throw new Error('No cart ID available for shipping options');
      }

      try {
        const response = await axios.get(`${BASE_URL}/store/shipping-options/${this.testData.cartId}`, {
          headers: { 'x-publishable-api-key': PUBLISHABLE_KEY }
        });

        // Shipping options might not be configured, so we accept both success and not found
        if (response.status !== 200 && response.status !== 404) {
          throw new Error('Unexpected response for shipping options');
        }
      } catch (error) {
        if (error.response && (error.response.status === 404 || error.response.status === 400)) {
          // Expected if shipping options not configured
          return;
        }
        throw error;
      }
    });

    await this.test('Create Payment Session', async () => {
      if (!this.testData.cartId) {
        throw new Error('No cart ID available for payment session');
      }

      try {
        const response = await axios.post(`${BASE_URL}/store/carts/${this.testData.cartId}/payment-sessions`, {}, {
          headers: {
            'x-publishable-api-key': PUBLISHABLE_KEY,
            'Content-Type': 'application/json'
          }
        });

        // Payment sessions might not be configured, accept various responses
        if (response.status === 200 && response.data.cart) {
          this.testData.paymentSessionCreated = true;
        }
      } catch (error) {
        if (error.response && (error.response.status === 404 || error.response.status === 400 || error.response.status === 500)) {
          // Expected if payment providers not configured
          return;
        }
        throw error;
      }
    });

    await this.test('Complete Cart (Create Order)', async () => {
      if (!this.testData.cartId) {
        throw new Error('No cart ID available for order completion');
      }

      try {
        const response = await axios.post(`${BASE_URL}/store/carts/${this.testData.cartId}/complete`, {}, {
          headers: {
            'x-publishable-api-key': PUBLISHABLE_KEY,
            'Content-Type': 'application/json'
          }
        });

        if (response.status === 200 && response.data.order) {
          this.testData.orderId = response.data.order.id;
        }
      } catch (error) {
        if (error.response && (error.response.status === 400 || error.response.status === 422)) {
          // Expected if payment/shipping not properly configured
          await this.log('Cart completion failed - likely due to payment/shipping configuration', 'warning');
          return;
        }
        throw error;
      }
    });
  }

  async testOrdersAPI() {
    await this.test('List Orders (Admin)', async () => {
      const response = await axios.get(`${BASE_URL}/admin/orders`, {
        headers: { Authorization: `Bearer ${this.adminToken}` }
      });

      if (!Array.isArray(response.data.orders)) {
        throw new Error('Orders should be an array');
      }
    });

    await this.test('Get Order by ID (if available)', async () => {
      if (!this.testData.orderId) {
        // Try to get any existing order for testing
        const ordersResponse = await axios.get(`${BASE_URL}/admin/orders`, {
          headers: { Authorization: `Bearer ${this.adminToken}` }
        });

        if (ordersResponse.data.orders.length === 0) {
          await this.log('No orders available for individual order testing', 'warning');
          return;
        }

        this.testData.orderId = ordersResponse.data.orders[0].id;
      }

      const response = await axios.get(`${BASE_URL}/admin/orders/${this.testData.orderId}`, {
        headers: { Authorization: `Bearer ${this.adminToken}` }
      });

      if (!response.data.order || response.data.order.id !== this.testData.orderId) {
        throw new Error('Order retrieval by ID failed');
      }
    });

    await this.test('List Customer Orders (Store)', async () => {
      if (!this.testData.customerToken) {
        await this.log('No customer token available for customer orders test', 'warning');
        return;
      }

      try {
        const response = await axios.get(`${BASE_URL}/store/customers/me/orders`, {
          headers: {
            'x-publishable-api-key': PUBLISHABLE_KEY,
            Authorization: `Bearer ${this.testData.customerToken}`
          }
        });

        if (!Array.isArray(response.data.orders)) {
          throw new Error('Customer orders should be an array');
        }
      } catch (error) {
        if (error.response && error.response.status === 401) {
          await this.log('Customer authentication required for orders test', 'warning');
          return;
        }
        throw error;
      }
    });
  }

  async testCategoriesAPI() {
    await this.test('List Categories (Store)', async () => {
      const response = await axios.get(`${BASE_URL}/store/product-categories`, {
        headers: { 'x-publishable-api-key': PUBLISHABLE_KEY }
      });
      
      if (!Array.isArray(response.data.product_categories)) {
        throw new Error('Categories should be an array');
      }
    });
  }

  async testErrorHandling() {
    await this.test('Unauthorized Access Rejection', async () => {
      try {
        await axios.get(`${BASE_URL}/admin/products`);
        throw new Error('Should have rejected unauthorized access');
      } catch (error) {
        if (error.response && error.response.status === 401) {
          return; // Expected behavior
        }
        throw error;
      }
    });

    await this.test('Non-existent Resource Handling', async () => {
      try {
        await axios.get(`${BASE_URL}/admin/products/non-existent-id`, {
          headers: { Authorization: `Bearer ${this.adminToken}` }
        });
        throw new Error('Should have returned 404 for non-existent resource');
      } catch (error) {
        if (error.response && (error.response.status === 404 || error.response.status === 500)) {
          return; // Expected behavior
        }
        throw error;
      }
    });
  }

  async generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        total: this.results.total,
        passed: this.results.passed,
        failed: this.results.failed,
        critical_failures: this.results.critical,
        success_rate: ((this.results.passed / this.results.total) * 100).toFixed(2) + '%'
      },
      status: this.results.critical > 0 ? 'CRITICAL_ISSUES' : 
              this.results.failed > 0 ? 'ISSUES_FOUND' : 'ALL_PASSED',
      tests: this.results.tests
    };

    // Save to file
    fs.writeFileSync('api-test-results.json', JSON.stringify(report, null, 2));
    
    // Print summary
    await this.log('\n=== TEST SUMMARY ===', 'info');
    await this.log(`Total Tests: ${this.results.total}`, 'info');
    await this.log(`Passed: ${this.results.passed}`, 'success');
    await this.log(`Failed: ${this.results.failed}`, 'error');
    
    if (this.results.critical > 0) {
      await this.log(`Critical Failures: ${this.results.critical}`, 'critical');
      await this.log('🚨 CRITICAL ISSUES FOUND - DO NOT DEPLOY TO PRODUCTION', 'critical');
    }
    
    await this.log(`Success Rate: ${report.summary.success_rate}`, 'info');
    await this.log('Report saved to: api-test-results.json', 'info');
    
    return report;
  }

  async run() {
    try {
      await this.log('🚀 Starting Comprehensive API Test Suite', 'info');
      
      await this.setup();
      await this.testAuthentication();
      await this.testMultiTenantConfiguration();
      await this.testProductsAPI();
      await this.testCartAPI();
      await this.testCheckoutAPI();
      await this.testCustomersAPI();
      await this.testOrdersAPI();
      await this.testCategoriesAPI();
      await this.testErrorHandling();
      
      const report = await this.generateReport();
      
      // Exit with appropriate code
      process.exit(this.results.critical > 0 ? 2 : this.results.failed > 0 ? 1 : 0);
      
    } catch (error) {
      await this.log(`Test suite failed: ${error.message}`, 'critical');
      process.exit(3);
    }
  }
}

// Run the test suite
if (require.main === module) {
  const tester = new APITester();
  tester.run();
}

module.exports = APITester;
