{"info": {"name": "02 - Tenant Management", "description": "Multi-tenant configuration and management endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Check if auth token exists", "if (!pm.environment.get('auth_token')) {", "    console.log('Warning: No auth token found. Please run authentication first.');", "}"]}}], "variable": [], "item": [{"name": "Get Tenant Configuration", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has tenant data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('tenant');", "    pm.expect(jsonData.tenant).to.have.property('id');", "    pm.expect(jsonData.tenant.id).to.eql(pm.environment.get('tenant_id'));", "});", "", "pm.test(\"Tenant has ONDC configuration\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.tenant).to.have.property('settings');", "    pm.expect(jsonData.tenant.settings).to.have.property('ondcConfig');", "});"]}}], "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/tenant", "host": ["{{base_url}}"], "path": ["admin", "tenant"]}, "description": "Get current tenant configuration including ONDC settings"}}, {"name": "Update Tenant Configuration", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Configuration updated successfully\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('tenant');", "    pm.expect(jsonData.tenant).to.have.property('settings');", "});"]}}], "request": {"method": "POST", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Content-Type", "value": "{{content_type}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"settings\": {\n    \"ondcConfig\": {\n      \"participantId\": \"{{ondc_participant_id}}\",\n      \"subscriberId\": \"{{ondc_subscriber_id}}\",\n      \"bppId\": \"{{ondc_bpp_id}}\",\n      \"domain\": \"{{tenant_domain}}\",\n      \"region\": \"{{region}}\"\n    },\n    \"branding\": {\n      \"primaryColor\": \"{{primary_color}}\",\n      \"logo\": \"{{logo_url}}\"\n    },\n    \"features\": {\n      \"products\": true,\n      \"orders\": true,\n      \"customers\": true,\n      \"analytics\": true,\n      \"inventory\": true\n    }\n  }\n}"}, "url": {"raw": "{{base_url}}/admin/tenant", "host": ["{{base_url}}"], "path": ["admin", "tenant"]}, "description": "Update tenant configuration and ONDC settings"}}, {"name": "Test Electronics Tenant", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Temporarily set electronics tenant ID", "pm.request.headers.add({", "    key: 'x-tenant-id',", "    value: 'tenant-electronics-001'", "});"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Electronics tenant detected\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.tenant.id).to.eql('tenant-electronics-001');", "    pm.expect(jsonData.tenant.name).to.eql('Electronics Store');", "});"]}}], "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "tenant-electronics-001", "type": "text"}], "url": {"raw": "{{base_url}}/admin/tenant", "host": ["{{base_url}}"], "path": ["admin", "tenant"]}, "description": "Test Electronics Store tenant configuration"}}, {"name": "Test Fashion Tenant", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Temporarily set fashion tenant ID", "pm.request.headers.add({", "    key: 'x-tenant-id',", "    value: 'tenant-fashion-002'", "});"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Fashion tenant detected\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.tenant.id).to.eql('tenant-fashion-002');", "    pm.expect(jsonData.tenant.name).to.eql('Fashion Store');", "});"]}}], "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "tenant-fashion-002", "type": "text"}], "url": {"raw": "{{base_url}}/admin/tenant", "host": ["{{base_url}}"], "path": ["admin", "tenant"]}, "description": "Test Fashion Store tenant configuration"}}, {"name": "Test Default <PERSON>", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Default tenant detected\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.tenant.id).to.eql('default');", "});"]}}], "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "default", "type": "text"}], "url": {"raw": "{{base_url}}/admin/tenant", "host": ["{{base_url}}"], "path": ["admin", "tenant"]}, "description": "Test Default tenant configuration"}}]}