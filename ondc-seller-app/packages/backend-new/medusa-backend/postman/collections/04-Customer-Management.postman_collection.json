{"info": {"name": "04 - Customer Management", "description": "Customer CRUD operations with multi-tenant support", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Check if auth token exists", "if (!pm.environment.get('auth_token')) {", "    console.log('Warning: No auth token found. Please run authentication first.');", "}"]}}], "variable": [], "item": [{"name": "List All Customers", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has customers array\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('customers');", "    pm.expect(jsonData.customers).to.be.an('array');", "});", "", "pm.test(\"Customers have tenant_id\", function () {", "    var jsonData = pm.response.json();", "    if (jsonData.customers.length > 0) {", "        pm.expect(jsonData.customers[0]).to.have.property('tenant_id');", "    }", "});"]}}], "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/customers", "host": ["{{base_url}}"], "path": ["admin", "customers"]}, "description": "Get all customers (shows tenant isolation in database)"}}, {"name": "Get Customer by ID", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has customer data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('customer');", "    pm.expect(jsonData.customer).to.have.property('id');", "    pm.expect(jsonData.customer.id).to.eql(pm.environment.get('customer_id'));", "});", "", "pm.test(\"Customer has correct tenant\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.customer).to.have.property('tenant_id');", "    pm.expect(jsonData.customer.tenant_id).to.eql(pm.environment.get('tenant_id'));", "});"]}}], "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/customers/{{customer_id}}", "host": ["{{base_url}}"], "path": ["admin", "customers", "{{customer_id}}"]}, "description": "Get specific customer by ID with tenant validation"}}, {"name": "Create New Customer", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200 or 201\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201]);", "});", "", "pm.test(\"Customer created successfully\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('customer');", "    pm.expect(jsonData.customer).to.have.property('id');", "    ", "    // Save customer ID for future requests", "    pm.environment.set('new_customer_id', jsonData.customer.id);", "});", "", "pm.test(\"Customer has correct tenant\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.customer).to.have.property('tenant_id');", "    pm.expect(jsonData.customer.tenant_id).to.eql(pm.environment.get('tenant_id'));", "});"]}}], "request": {"method": "POST", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Content-Type", "value": "{{content_type}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"test.customer.{{$randomInt}}@{{tenant_domain}}\",\n  \"first_name\": \"Test\",\n  \"last_name\": \"Customer\",\n  \"phone\": \"+91-98765{{$randomInt}}\",\n  \"has_account\": true\n}"}, "url": {"raw": "{{base_url}}/admin/customers", "host": ["{{base_url}}"], "path": ["admin", "customers"]}, "description": "Create a new customer with tenant context"}}, {"name": "Update Customer", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Customer updated successfully\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('customer');", "    pm.expect(jsonData.customer).to.have.property('phone');", "});"]}}], "request": {"method": "POST", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Content-Type", "value": "{{content_type}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"+91-98765{{$randomInt}}\",\n  \"metadata\": {\n    \"updated_via\": \"postman\",\n    \"updated_at\": \"{{$isoTimestamp}}\"\n  }\n}"}, "url": {"raw": "{{base_url}}/admin/customers/{{customer_id}}", "host": ["{{base_url}}"], "path": ["admin", "customers", "{{customer_id}}"]}, "description": "Update existing customer with tenant validation"}}, {"name": "Search Customers by Email", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Search results returned\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('customers');", "    pm.expect(jsonData.customers).to.be.an('array');", "});"]}}], "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/customers?q={{customer_email}}", "host": ["{{base_url}}"], "path": ["admin", "customers"], "query": [{"key": "q", "value": "{{customer_email}}"}]}, "description": "Search customers by email with tenant filtering"}}, {"name": "Get Customers with Pagination", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Pagination data present\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('count');", "    pm.expect(jsonData).to.have.property('offset');", "    pm.expect(jsonData).to.have.property('limit');", "});"]}}], "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/customers?limit=10&offset=0", "host": ["{{base_url}}"], "path": ["admin", "customers"], "query": [{"key": "limit", "value": "10"}, {"key": "offset", "value": "0"}]}, "description": "Get customers with pagination parameters"}}, {"name": "Filter Customers by Tenant", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"All customers belong to current tenant\", function () {", "    var jsonData = pm.response.json();", "    var currentTenant = pm.environment.get('tenant_id');", "    ", "    jsonData.customers.forEach(function(customer) {", "        if (customer.tenant_id && customer.tenant_id !== 'default') {", "            pm.expect(customer.tenant_id).to.eql(currentTenant);", "        }", "    });", "});"]}}], "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/customers", "host": ["{{base_url}}"], "path": ["admin", "customers"]}, "description": "Verify tenant filtering for customers"}}]}