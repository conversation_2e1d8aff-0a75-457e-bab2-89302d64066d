# Medusa v2 API Reference for ONDC Seller App

## Base URLs
- **Admin API:** `http://localhost:9000/admin`
- **Store API:** `http://localhost:9000/store`
- **Auth API:** `http://localhost:9000/auth`
- **Admin Panel:** `http://localhost:9000/app`

## Authentication

### Admin Login
```bash
POST /auth/user/emailpass
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "supersecret"
}

# Response
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### Using JWT Token
Include the token in all admin API requests:
```bash
Authorization: Bearer YOUR_JWT_TOKEN
```

## Admin API Endpoints

### Users Management

#### Get Current User
```bash
GET /admin/users/me
Authorization: Bearer YOUR_TOKEN

# Response
{
  "user": {
    "id": "user_01JZ4VVGEJX6KTQ3RZMB78Y5M1",
    "email": "<EMAIL>",
    "first_name": null,
    "last_name": null,
    "created_at": "2025-07-02T06:05:45.810Z"
  }
}
```

#### List All Users
```bash
GET /admin/users
Authorization: Bearer YOUR_TOKEN

# Query Parameters
?limit=20&offset=0&q=search_term

# Response
{
  "users": [...],
  "count": 1,
  "offset": 0,
  "limit": 50
}
```

#### Create User
```bash
POST /admin/users
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "email": "<EMAIL>",
  "first_name": "New",
  "last_name": "User"
}
```

#### Update User
```bash
POST /admin/users/{user_id}
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "first_name": "Updated Name"
}
```

#### Delete User
```bash
DELETE /admin/users/{user_id}
Authorization: Bearer YOUR_TOKEN
```

### Customer Management

#### List Customers
```bash
GET /admin/customers
Authorization: Bearer YOUR_TOKEN

# Query Parameters
?limit=20&offset=0&q=search_term&email=<EMAIL>

# Response
{
  "customers": [...],
  "count": 0,
  "offset": 0,
  "limit": 50
}
```

#### Create Customer
```bash
POST /admin/customers
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "phone": "+**********",
  "metadata": {
    "ondc_seller_id": "seller_123"
  }
}

# Response
{
  "customer": {
    "id": "cus_01JZ4W260PPK0PW0R9EK9D3D5F",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "has_account": false,
    "created_at": "2025-07-02T06:09:24.503Z"
  }
}
```

#### Get Customer by ID
```bash
GET /admin/customers/{customer_id}
Authorization: Bearer YOUR_TOKEN

# Response includes customer details, orders, and addresses
```

#### Update Customer
```bash
POST /admin/customers/{customer_id}
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "first_name": "Updated Name",
  "metadata": {
    "updated_field": "value"
  }
}
```

#### Delete Customer
```bash
DELETE /admin/customers/{customer_id}
Authorization: Bearer YOUR_TOKEN
```

### Product Management

#### List Products
```bash
GET /admin/products
Authorization: Bearer YOUR_TOKEN

# Query Parameters
?limit=20&offset=0&q=search_term&status=published&category_id=cat_123

# Response
{
  "products": [...],
  "count": 0,
  "offset": 0,
  "limit": 50
}
```

#### Create Product
```bash
POST /admin/products
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "title": "ONDC Sample Product",
  "handle": "ondc-sample-product",
  "description": "A sample product for ONDC marketplace",
  "status": "published",
  "thumbnail": "https://example.com/image.jpg",
  "metadata": {
    "ondc_category": "electronics",
    "seller_id": "seller_123"
  },
  "options": [
    {
      "title": "Size",
      "values": ["S", "M", "L", "XL"]
    },
    {
      "title": "Color",
      "values": ["Red", "Blue", "Green"]
    }
  ],
  "variants": [
    {
      "title": "Small Red",
      "prices": [
        {
          "amount": 2999,
          "currency_code": "INR"
        }
      ],
      "options": {
        "Size": "S",
        "Color": "Red"
      },
      "manage_inventory": true,
      "inventory_quantity": 100
    }
  ],
  "categories": [
    {
      "id": "pcat_electronics"
    }
  ]
}
```

#### Get Product by ID
```bash
GET /admin/products/{product_id}
Authorization: Bearer YOUR_TOKEN

# Include relations
?fields=*variants,*options,*categories,*images
```

#### Update Product
```bash
POST /admin/products/{product_id}
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "title": "Updated Product Title",
  "description": "Updated description",
  "metadata": {
    "updated_at": "2025-07-02"
  }
}
```

#### Delete Product
```bash
DELETE /admin/products/{product_id}
Authorization: Bearer YOUR_TOKEN
```

### Order Management

#### List Orders
```bash
GET /admin/orders
Authorization: Bearer YOUR_TOKEN

# Query Parameters
?limit=15&offset=0&status=pending&customer_id=cus_123&payment_status=captured

# Response
{
  "orders": [...],
  "count": 0,
  "offset": 0,
  "limit": 15
}
```

#### Create Order (Draft)
```bash
POST /admin/orders
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "customer_id": "cus_0**********",
  "region_id": "reg_0**********",
  "items": [
    {
      "variant_id": "variant_0**********",
      "quantity": 2
    }
  ],
  "shipping_address": {
    "first_name": "John",
    "last_name": "Doe",
    "address_1": "123 Main St",
    "city": "Mumbai",
    "postal_code": "400001",
    "country_code": "IN"
  }
}
```

#### Get Order by ID
```bash
GET /admin/orders/{order_id}
Authorization: Bearer YOUR_TOKEN

# Include relations
?fields=*customer,*items,*shipping_address,*payments
```

#### Update Order
```bash
POST /admin/orders/{order_id}
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "status": "fulfilled",
  "metadata": {
    "tracking_number": "TRK123456789"
  }
}
```

#### Cancel Order
```bash
POST /admin/orders/{order_id}/cancel
Authorization: Bearer YOUR_TOKEN
```

### Inventory Management

#### List Stock Locations
```bash
GET /admin/stock-locations
Authorization: Bearer YOUR_TOKEN

# Response
{
  "stock_locations": [...],
  "count": 0,
  "offset": 0,
  "limit": 50
}
```

#### Create Stock Location
```bash
POST /admin/stock-locations
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "name": "ONDC Main Warehouse",
  "address": {
    "address_1": "123 Warehouse St",
    "city": "Mumbai",
    "postal_code": "400001",
    "country_code": "IN"
  },
  "metadata": {
    "ondc_location_id": "loc_mumbai_001"
  }
}
```

#### Update Inventory Levels
```bash
POST /admin/inventory-items/{inventory_item_id}/location-levels/{location_id}
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "stocked_quantity": 150,
  "incoming_quantity": 50
}
```

### Region Management

#### List Regions
```bash
GET /admin/regions
Authorization: Bearer YOUR_TOKEN

# Response
{
  "regions": [...],
  "count": 0,
  "offset": 0,
  "limit": 50
}
```

#### Create Region
```bash
POST /admin/regions
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "name": "India",
  "currency_code": "INR",
  "countries": ["IN"],
  "metadata": {
    "ondc_region": true,
    "tax_rate": 18
  }
}
```

### Sales Channel Management

#### List Sales Channels
```bash
GET /admin/sales-channels
Authorization: Bearer YOUR_TOKEN
```

#### Create Sales Channel
```bash
POST /admin/sales-channels
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "name": "ONDC Marketplace",
  "description": "Sales channel for ONDC marketplace integration",
  "is_disabled": false
}
```

## Store API Endpoints

### Public Endpoints (No Authentication Required)

#### Get Store Information
```bash
GET /store
x-publishable-api-key: YOUR_PUBLISHABLE_KEY

# Response
{
  "store": {
    "id": "store_0**********",
    "name": "ONDC Store",
    "default_currency_code": "INR",
    "default_sales_channel_id": "sc_0**********"
  }
}
```

#### List Products (Store)
```bash
GET /store/products
x-publishable-api-key: YOUR_PUBLISHABLE_KEY

# Query Parameters
?limit=20&offset=0&category_id=cat_123&region_id=reg_123

# Response
{
  "products": [...],
  "count": 0,
  "offset": 0,
  "limit": 20
}
```

#### Get Product by Handle
```bash
GET /store/products/{handle}
x-publishable-api-key: YOUR_PUBLISHABLE_KEY

# Response includes full product details with variants and options
```

#### List Product Categories
```bash
GET /store/product-categories
x-publishable-api-key: YOUR_PUBLISHABLE_KEY

# Query Parameters
?parent_category_id=cat_123&include_descendants_tree=true

# Response
{
  "product_categories": [...],
  "count": 0,
  "offset": 0,
  "limit": 100
}
```

### Cart Management

#### Create Cart
```bash
POST /store/carts
x-publishable-api-key: YOUR_PUBLISHABLE_KEY
Content-Type: application/json

{
  "region_id": "reg_0**********",
  "sales_channel_id": "sc_0**********"
}

# Response
{
  "cart": {
    "id": "cart_0**********",
    "region_id": "reg_0**********",
    "customer_id": null,
    "items": [],
    "total": 0
  }
}
```

#### Get Cart
```bash
GET /store/carts/{cart_id}
x-publishable-api-key: YOUR_PUBLISHABLE_KEY

# Response includes cart details, items, totals, and shipping options
```

#### Add Item to Cart
```bash
POST /store/carts/{cart_id}/line-items
x-publishable-api-key: YOUR_PUBLISHABLE_KEY
Content-Type: application/json

{
  "variant_id": "variant_0**********",
  "quantity": 2,
  "metadata": {
    "ondc_item_id": "item_123"
  }
}
```

#### Update Cart Item
```bash
POST /store/carts/{cart_id}/line-items/{line_item_id}
x-publishable-api-key: YOUR_PUBLISHABLE_KEY
Content-Type: application/json

{
  "quantity": 3
}
```

#### Remove Cart Item
```bash
DELETE /store/carts/{cart_id}/line-items/{line_item_id}
x-publishable-api-key: YOUR_PUBLISHABLE_KEY
```

#### Add Shipping Address
```bash
POST /store/carts/{cart_id}/shipping-address
x-publishable-api-key: YOUR_PUBLISHABLE_KEY
Content-Type: application/json

{
  "first_name": "John",
  "last_name": "Doe",
  "address_1": "123 Main St",
  "city": "Mumbai",
  "postal_code": "400001",
  "country_code": "IN",
  "phone": "+919876543210"
}
```

### Checkout Process

#### List Shipping Options
```bash
GET /store/carts/{cart_id}/shipping-options
x-publishable-api-key: YOUR_PUBLISHABLE_KEY

# Response
{
  "shipping_options": [
    {
      "id": "so_0**********",
      "name": "Standard Delivery",
      "price": 99,
      "data": {
        "estimated_delivery": "3-5 days"
      }
    }
  ]
}
```

#### Add Shipping Method
```bash
POST /store/carts/{cart_id}/shipping-methods
x-publishable-api-key: YOUR_PUBLISHABLE_KEY
Content-Type: application/json

{
  "option_id": "so_0**********"
}
```

#### Initialize Payment Session
```bash
POST /store/carts/{cart_id}/payment-sessions
x-publishable-api-key: YOUR_PUBLISHABLE_KEY
Content-Type: application/json

{
  "provider_id": "razorpay"
}
```

#### Complete Cart (Place Order)
```bash
POST /store/carts/{cart_id}/complete
x-publishable-api-key: YOUR_PUBLISHABLE_KEY

# Response
{
  "order": {
    "id": "order_0**********",
    "status": "pending",
    "payment_status": "awaiting",
    "fulfillment_status": "not_fulfilled"
  }
}
```

### Customer Authentication (Store)

#### Customer Registration
```bash
POST /store/customers
x-publishable-api-key: YOUR_PUBLISHABLE_KEY
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword",
  "first_name": "John",
  "last_name": "Doe",
  "phone": "+919876543210"
}
```

#### Customer Login
```bash
POST /auth/customer/emailpass
x-publishable-api-key: YOUR_PUBLISHABLE_KEY
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword"
}

# Response
{
  "token": "customer_jwt_token_here"
}
```

#### Get Customer Profile
```bash
GET /store/customers/me
x-publishable-api-key: YOUR_PUBLISHABLE_KEY
Authorization: Bearer CUSTOMER_JWT_TOKEN

# Response includes customer details, addresses, and order history
```

## Error Responses

### Common Error Formats
```json
{
  "type": "invalid_data",
  "message": "Validation error message"
}

{
  "type": "not_found",
  "message": "Resource not found"
}

{
  "type": "unauthorized",
  "message": "Authentication required"
}

{
  "type": "not_allowed",
  "message": "Insufficient permissions"
}
```

### HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Unprocessable Entity
- `500` - Internal Server Error

## Rate Limiting
- No rate limiting configured in development
- Implement rate limiting for production deployment

## Pagination
Most list endpoints support pagination:
```bash
?limit=20&offset=40
```

## Filtering and Search
Many endpoints support filtering:
```bash
?q=search_term&status=published&created_at[gte]=2025-01-01
```

## CORS Configuration
- **Admin CORS:** `http://localhost:5173,http://localhost:9000,https://docs.medusajs.com`
- **Store CORS:** `http://localhost:8000,https://docs.medusajs.com`
- **Auth CORS:** `http://localhost:5173,http://localhost:9000,http://localhost:8000,https://docs.medusajs.com`

## Testing with cURL

### Complete Example: Create and List Customer
```bash
# 1. Login
TOKEN=$(curl -s -X POST http://localhost:9000/auth/user/emailpass \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "supersecret"}' | \
  grep -o '"token":"[^"]*"' | cut -d'"' -f4)

# 2. Create customer
curl -X POST -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "first_name": "Test", "last_name": "User"}' \
  http://localhost:9000/admin/customers

# 3. List customers
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:9000/admin/customers
```

### Complete Example: E-commerce Flow
```bash
# 1. Create cart
CART=$(curl -s -X POST http://localhost:9000/store/carts \
  -H "x-publishable-api-key: pk_test_123" \
  -H "Content-Type: application/json" \
  -d '{"region_id": "reg_0**********"}' | \
  grep -o '"id":"[^"]*"' | cut -d'"' -f4)

# 2. Add item to cart
curl -X POST http://localhost:9000/store/carts/$CART/line-items \
  -H "x-publishable-api-key: pk_test_123" \
  -H "Content-Type: application/json" \
  -d '{"variant_id": "variant_0**********", "quantity": 1}'

# 3. Add shipping address
curl -X POST http://localhost:9000/store/carts/$CART/shipping-address \
  -H "x-publishable-api-key: pk_test_123" \
  -H "Content-Type: application/json" \
  -d '{"first_name": "John", "last_name": "Doe", "address_1": "123 Main St", "city": "Mumbai", "postal_code": "400001", "country_code": "IN"}'

# 4. Complete order
curl -X POST http://localhost:9000/store/carts/$CART/complete \
  -H "x-publishable-api-key: pk_test_123"
```

## Next Steps for ONDC Integration
1. Configure publishable API keys for store endpoints
2. Set up ONDC-specific product attributes and categories
3. Implement ONDC order workflows and status mapping
4. Add ONDC-compliant pricing structures
5. Configure region-specific settings for Indian market
6. Integrate with ONDC payment gateways
7. Implement seller onboarding and multi-vendor features
