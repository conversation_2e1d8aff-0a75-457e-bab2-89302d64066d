#!/bin/bash

# Medusa v2 Backend Setup Script for ONDC Seller App
# This script sets up the complete Medusa v2 backend environment

set -e

echo "🚀 Setting up Medusa v2 Backend for ONDC Seller App..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "medusa-backend/package.json" ]; then
    print_error "Please run this script from the packages/backend-new directory"
    exit 1
fi

# Navigate to medusa-backend directory
cd medusa-backend

print_status "Checking Node.js version..."
NODE_VERSION=$(node --version)
print_success "Node.js version: $NODE_VERSION"

print_status "Checking npm version..."
NPM_VERSION=$(npm --version)
print_success "npm version: $NPM_VERSION"

# Check if PostgreSQL is running
print_status "Checking PostgreSQL connection..."
if docker exec strapi-postgres psql -U strapi -d postgres -c "SELECT 1;" > /dev/null 2>&1; then
    print_success "PostgreSQL is running and accessible"
else
    print_error "PostgreSQL is not accessible. Please ensure Docker container 'strapi-postgres' is running"
    exit 1
fi

# Check if database exists
print_status "Checking if medusa_backend database exists..."
if docker exec strapi-postgres psql -U strapi -d postgres -c "\l" | grep -q "medusa_backend"; then
    print_success "Database 'medusa_backend' exists"
else
    print_warning "Database 'medusa_backend' does not exist. Creating it..."
    docker exec strapi-postgres psql -U strapi -d postgres -c "CREATE DATABASE medusa_backend;"
    print_success "Database 'medusa_backend' created"
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    print_status "Installing dependencies..."
    npm install
    print_success "Dependencies installed"
else
    print_success "Dependencies already installed"
fi

# Run migrations
print_status "Running database migrations..."
npx medusa db:migrate
print_success "Database migrations completed"

# Check if admin user exists
print_status "Checking if admin user exists..."
ADMIN_EXISTS=$(docker exec strapi-postgres psql -U strapi -d medusa_backend -t -c "SELECT COUNT(*) FROM user WHERE email = '<EMAIL>';" 2>/dev/null | tr -d ' ' || echo "0")

if [ "$ADMIN_EXISTS" = "0" ]; then
    print_status "Creating admin user..."
    npx medusa user --email <EMAIL> --password supersecret
    print_success "Admin user created"
else
    print_success "Admin user already exists"
fi

# Create a simple health check script
cat > health-check.sh << 'EOF'
#!/bin/bash
echo "🏥 Medusa Backend Health Check"
echo "=============================="

# Check if server is running
if curl -s http://localhost:9000/health > /dev/null 2>&1; then
    echo "✅ Server is running on port 9000"
else
    echo "❌ Server is not responding on port 9000"
    exit 1
fi

# Test authentication
echo "🔐 Testing authentication..."
TOKEN=$(curl -s -X POST http://localhost:9000/auth/user/emailpass \
    -H "Content-Type: application/json" \
    -d '{"email": "<EMAIL>", "password": "supersecret"}' | \
    grep -o '"token":"[^"]*"' | cut -d'"' -f4)

if [ -n "$TOKEN" ]; then
    echo "✅ Authentication successful"
    
    # Test admin endpoints
    echo "🧪 Testing admin endpoints..."
    
    # Test users endpoint
    if curl -s -H "Authorization: Bearer $TOKEN" http://localhost:9000/admin/users | grep -q "<EMAIL>"; then
        echo "✅ Admin users endpoint working"
    else
        echo "❌ Admin users endpoint failed"
    fi
    
    # Test customers endpoint
    if curl -s -H "Authorization: Bearer $TOKEN" http://localhost:9000/admin/customers | grep -q "customers"; then
        echo "✅ Customers endpoint working"
    else
        echo "❌ Customers endpoint failed"
    fi
    
    # Test products endpoint
    if curl -s -H "Authorization: Bearer $TOKEN" http://localhost:9000/admin/products | grep -q "products"; then
        echo "✅ Products endpoint working"
    else
        echo "❌ Products endpoint failed"
    fi
    
    # Test orders endpoint
    if curl -s -H "Authorization: Bearer $TOKEN" http://localhost:9000/admin/orders | grep -q "orders"; then
        echo "✅ Orders endpoint working"
    else
        echo "❌ Orders endpoint failed"
    fi
    
else
    echo "❌ Authentication failed"
    exit 1
fi

echo ""
echo "🎉 All health checks passed!"
echo "📊 Admin Panel: http://localhost:9000/app"
echo "🔑 Admin Credentials: <EMAIL> / supersecret"
EOF

chmod +x health-check.sh

print_success "Setup completed successfully!"
print_status "Health check script created: ./health-check.sh"

echo ""
echo "🎉 Medusa v2 Backend Setup Complete!"
echo "======================================"
echo ""
echo "📊 Admin Panel: http://localhost:9000/app"
echo "🔑 Admin Credentials:"
echo "   Email: <EMAIL>"
echo "   Password: supersecret"
echo ""
echo "🚀 To start the server:"
echo "   npm run dev"
echo ""
echo "🏥 To run health check:"
echo "   ./health-check.sh"
echo ""
echo "📚 Documentation: ./README.md"
echo ""
