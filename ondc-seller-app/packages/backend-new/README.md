# Medusa v2 Backend for ONDC Seller App

## Overview

This is a fresh Medusa v2 e-commerce backend implementation for the ONDC Seller App, providing comprehensive e-commerce functionality with modern architecture and full API support.

## 🚀 Quick Start

### Prerequisites
- Node.js v20.19.3 or higher
- PostgreSQL database
- npm or yarn package manager

### Installation & Setup

1. **Navigate to the backend directory:**
   ```bash
   cd packages/backend-new/medusa-backend
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Environment Configuration:**
   The `.env` file is already configured with:
   ```env
   DATABASE_URL=postgresql://strapi:strapi_password@localhost:5432/medusa_backend
   STORE_CORS=http://localhost:8000,https://docs.medusajs.com
   ADMIN_CORS=http://localhost:5173,http://localhost:9000,https://docs.medusajs.com
   AUTH_CORS=http://localhost:5173,http://localhost:9000,http://localhost:8000,https://docs.medusajs.com
   JWT_SECRET=supersecret
   COOKIE_SECRET=supersecret
   ```

4. **Run database migrations:**
   ```bash
   npx medusa db:migrate
   ```

5. **Start the development server:**
   ```bash
   npm run dev
   ```

## 🔐 Admin Access

### Admin Credentials
- **Email:** <EMAIL>
- **Password:** supersecret

### Admin Panel
- **URL:** http://localhost:9000/app
- **API Base:** http://localhost:9000/admin

## 📊 Database

### Database Details
- **Name:** medusa_backend
- **Host:** localhost:5432
- **User:** strapi
- **Password:** strapi_password

### Database Schema
The database includes all standard Medusa v2 tables:
- Products & Variants
- Customers & Orders
- Inventory & Stock Locations
- Pricing & Promotions
- Payments & Fulfillments
- Users & Authentication
- Regions & Sales Channels

## 🛠 API Endpoints

### Authentication
```bash
# Login to get JWT token
curl -X POST http://localhost:9000/auth/user/emailpass \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "supersecret"}'
```

### Core E-commerce Endpoints

#### Products
```bash
# List products
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:9000/admin/products

# Create product (requires proper structure)
curl -X POST -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"title": "Product Name", "handle": "product-handle"}' \
  http://localhost:9000/admin/products
```

#### Customers
```bash
# List customers
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:9000/admin/customers

# Create customer
curl -X POST -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "first_name": "John", "last_name": "Doe"}' \
  http://localhost:9000/admin/customers
```

#### Orders
```bash
# List orders
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:9000/admin/orders
```

#### Users
```bash
# List admin users
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:9000/admin/users

# Get current user
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:9000/admin/users/me
```

#### Regions
```bash
# List regions
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:9000/admin/regions
```

## 🏗 Architecture

### Technology Stack
- **Framework:** Medusa v2.8.6
- **Database:** PostgreSQL with MikroORM
- **Authentication:** JWT-based auth
- **Admin UI:** Built-in Medusa Admin SDK
- **API:** RESTful APIs with OpenAPI support

### Key Features
- ✅ Complete e-commerce backend
- ✅ Admin panel with authentication
- ✅ Product management
- ✅ Customer management
- ✅ Order processing
- ✅ Inventory management
- ✅ Multi-region support
- ✅ Payment processing ready
- ✅ Fulfillment workflows
- ✅ Promotion engine

### Project Structure
```
medusa-backend/
├── src/                    # Source code
│   ├── api/               # API routes
│   ├── modules/           # Custom modules
│   ├── workflows/         # Business workflows
│   └── scripts/           # Utility scripts
├── integration-tests/     # Integration tests
├── .env                   # Environment configuration
├── medusa-config.ts       # Medusa configuration
├── package.json           # Dependencies
└── tsconfig.json          # TypeScript configuration
```

## 🔧 Development

### Available Scripts
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run seed         # Seed database with sample data
npm test:unit        # Run unit tests
npm test:integration # Run integration tests
```

### Adding Custom Functionality
1. Create modules in `src/modules/`
2. Add API routes in `src/api/`
3. Implement workflows in `src/workflows/`
4. Update configuration in `medusa-config.ts`

## 🚦 Server Status

### Current Status
- ✅ Server running on port 9000
- ✅ Database connected and migrated
- ✅ Admin user created
- ✅ Authentication working
- ✅ Core endpoints tested
- ✅ Admin panel accessible

### Health Check
```bash
# Check server status
curl http://localhost:9000/health

# Check admin authentication
curl -X POST http://localhost:9000/auth/user/emailpass \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "supersecret"}'
```

## 📝 Next Steps

1. **Customize for ONDC:** Add ONDC-specific modules and workflows
2. **Product Catalog:** Set up product categories and variants
3. **Payment Integration:** Configure payment providers
4. **Shipping:** Set up fulfillment providers
5. **Testing:** Implement comprehensive test suite
6. **Deployment:** Configure for production environment

## 🔗 Integration Points

### Frontend Integration
- Admin panel: http://localhost:9000/app
- Store API: http://localhost:9000/store (requires publishable API key)
- Admin API: http://localhost:9000/admin (requires authentication)

### External Services
- Database: PostgreSQL on localhost:5432
- Redis: In-memory (for development)
- File Storage: Local filesystem (configurable)

## 📚 Documentation

- [Medusa v2 Documentation](https://docs.medusajs.com/)
- [API Reference](http://localhost:9000/docs) (when server is running)
- [Admin Guide](https://docs.medusajs.com/admin)

## 🐛 Troubleshooting

### Common Issues
1. **Database Connection:** Ensure PostgreSQL is running and credentials are correct
2. **Port Conflicts:** Default port 9000, change in configuration if needed
3. **Authentication:** Use correct JWT token in Authorization header
4. **CORS:** Admin panel accessible from configured origins only

### Logs
Check server logs for detailed error information when running `npm run dev`.
