openapi: 3.0.3
info:
  title: ONDC Seller App API
  description: |
    Comprehensive API specification for the ONDC Seller App built on Medusa v2.
    
    ## Authentication
    
    ### Store APIs
    - **Header:** `x-publishable-api-key`
    - **Value:** `pk_3d67561dece2d466dc798c18c1f80523f84f3b2f01316e0bf915e51f3a59b98b`
    
    ### Admin APIs  
    - **Header:** `Authorization: Bearer {token}`
    - **Login:** POST `/auth/user/emailpass`
    
    ### Multi-tenant Support
    - **Header:** `x-tenant-id`
    - **Values:** `tenant-electronics-001`, `tenant-fashion-002`
    
  version: 2.0.0
  contact:
    name: ONDC Seller App Team
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:9000
    description: Development server
  - url: https://api.ondc-seller-app.com
    description: Production server

security:
  - PublishableApiKey: []
  - AdminBearerAuth: []

paths:
  /health:
    get:
      tags:
        - System
      summary: Health check
      description: Check system health status
      responses:
        '200':
          description: System is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "ok"
                  timestamp:
                    type: string
                    format: date-time

  /auth/user/emailpass:
    post:
      tags:
        - Authentication
      summary: Admin login
      description: Authenticate admin user with email and password
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                  format: email
                  example: "<EMAIL>"
                password:
                  type: string
                  example: "supersecret"
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: string
                    description: JWT token for admin authentication
        '401':
          description: Invalid credentials

  /store/products:
    get:
      tags:
        - Store - Products
      summary: Get all products
      description: Retrieve paginated list of products with variants, images, and categories
      security:
        - PublishableApiKey: []
      parameters:
        - name: x-tenant-id
          in: header
          required: true
          schema:
            type: string
            example: "tenant-electronics-001"
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
            maximum: 100
        - name: offset
          in: query
          schema:
            type: integer
            default: 0
      responses:
        '200':
          description: Products retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  products:
                    type: array
                    items:
                      $ref: '#/components/schemas/Product'
                  count:
                    type: integer
                  offset:
                    type: integer
                  limit:
                    type: integer
                  metadata:
                    type: object
                    properties:
                      tenantId:
                        type: string
                      timestamp:
                        type: string
                        format: date-time

  /store/products/{id}:
    get:
      tags:
        - Store - Products
      summary: Get single product
      description: Retrieve detailed product information including variants and pricing
      security:
        - PublishableApiKey: []
      parameters:
        - name: x-tenant-id
          in: header
          required: true
          schema:
            type: string
        - name: id
          in: path
          required: true
          schema:
            type: string
            example: "prod_01JZ7GFAM80F6NXHJB7EJ698GA"
      responses:
        '200':
          description: Product retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  product:
                    $ref: '#/components/schemas/Product'
                  metadata:
                    type: object

  /store/product-categories:
    get:
      tags:
        - Store - Categories
      summary: Get all product categories
      description: Retrieve hierarchical list of product categories
      security:
        - PublishableApiKey: []
      parameters:
        - name: x-tenant-id
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Categories retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  product_categories:
                    type: array
                    items:
                      $ref: '#/components/schemas/Category'

  /store/collections:
    get:
      tags:
        - Store - Collections
      summary: Get all collections
      description: Retrieve product collections (featured, top deals, hot picks)
      security:
        - PublishableApiKey: []
      parameters:
        - name: x-tenant-id
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Collections retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  collections:
                    type: array
                    items:
                      $ref: '#/components/schemas/Collection'

  /store/carts:
    post:
      tags:
        - Store - Cart
      summary: Create new cart
      description: Create a new shopping cart for the specified region
      security:
        - PublishableApiKey: []
      parameters:
        - name: x-tenant-id
          in: header
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - region_id
              properties:
                region_id:
                  type: string
                  example: "reg_01JZ7RPY072WGWKTJ6Q2YE46V7"
                customer_id:
                  type: string
                  description: Optional customer ID for authenticated users
                email:
                  type: string
                  format: email
                  description: Optional customer email
      responses:
        '200':
          description: Cart created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  cart:
                    $ref: '#/components/schemas/Cart'

  /store/carts/{id}/line-items:
    post:
      tags:
        - Store - Cart
      summary: Add item to cart
      description: Add a product variant to the shopping cart
      security:
        - PublishableApiKey: []
      parameters:
        - name: x-tenant-id
          in: header
          required: true
          schema:
            type: string
        - name: id
          in: path
          required: true
          schema:
            type: string
            description: Cart ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - variant_id
                - quantity
              properties:
                variant_id:
                  type: string
                  example: "variant_01JZ7GFASGR7D4G8BEYG6CE57X"
                quantity:
                  type: integer
                  minimum: 1
                  example: 2
      responses:
        '200':
          description: Item added to cart successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  cart:
                    $ref: '#/components/schemas/Cart'

  /admin/products:
    get:
      tags:
        - Admin - Products
      summary: Get all products (admin)
      description: Admin endpoint to retrieve all products with management data
      security:
        - AdminBearerAuth: []
      parameters:
        - name: x-tenant-id
          in: header
          required: true
          schema:
            type: string
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: Products retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  products:
                    type: array
                    items:
                      $ref: '#/components/schemas/AdminProduct'

  /admin/orders:
    get:
      tags:
        - Admin - Orders
      summary: Get all orders (admin)
      description: Admin endpoint to retrieve all orders with payment and fulfillment status
      security:
        - AdminBearerAuth: []
      parameters:
        - name: x-tenant-id
          in: header
          required: true
          schema:
            type: string
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: Orders retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  orders:
                    type: array
                    items:
                      $ref: '#/components/schemas/Order'

components:
  securitySchemes:
    PublishableApiKey:
      type: apiKey
      in: header
      name: x-publishable-api-key
      description: Publishable API key for store endpoints
    AdminBearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token for admin endpoints

  schemas:
    Product:
      type: object
      properties:
        id:
          type: string
          example: "prod_01JZ7GFAM80F6NXHJB7EJ698GA"
        title:
          type: string
          example: "Winter Jacket"
        description:
          type: string
        handle:
          type: string
        status:
          type: string
          enum: [draft, published]
        variants:
          type: array
          items:
            $ref: '#/components/schemas/ProductVariant'
        images:
          type: array
          items:
            $ref: '#/components/schemas/ProductImage'
        tags:
          type: array
          items:
            $ref: '#/components/schemas/ProductTag'
        categories:
          type: array
          items:
            $ref: '#/components/schemas/Category'

    ProductVariant:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        sku:
          type: string
        prices:
          type: array
          items:
            $ref: '#/components/schemas/Price'

    Price:
      type: object
      properties:
        id:
          type: string
        currency_code:
          type: string
        amount:
          type: integer
          description: Price in cents

    Category:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        handle:
          type: string
        parent_category_id:
          type: string
          nullable: true

    Collection:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        handle:
          type: string

    Cart:
      type: object
      properties:
        id:
          type: string
        region_id:
          type: string
        customer_id:
          type: string
          nullable: true
        email:
          type: string
          nullable: true
        items:
          type: array
          items:
            $ref: '#/components/schemas/CartItem'

    CartItem:
      type: object
      properties:
        id:
          type: string
        variant_id:
          type: string
        quantity:
          type: integer
        unit_price:
          type: integer

    Order:
      type: object
      properties:
        id:
          type: string
        display_id:
          type: integer
        status:
          type: string
        payment_status:
          type: string
        fulfillment_status:
          type: string
        items:
          type: array
          items:
            $ref: '#/components/schemas/OrderItem'

    OrderItem:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        variant_id:
          type: string
        quantity:
          type: integer
        unit_price:
          type: integer

    AdminProduct:
      allOf:
        - $ref: '#/components/schemas/Product'
        - type: object
          properties:
            created_at:
              type: string
              format: date-time
            updated_at:
              type: string
              format: date-time

    ProductImage:
      type: object
      properties:
        id:
          type: string
        url:
          type: string
        rank:
          type: integer

    ProductTag:
      type: object
      properties:
        id:
          type: string
        value:
          type: string

tags:
  - name: System
    description: System health and status endpoints
  - name: Authentication
    description: User authentication endpoints
  - name: Store - Products
    description: Customer-facing product endpoints
  - name: Store - Categories
    description: Customer-facing category endpoints
  - name: Store - Collections
    description: Customer-facing collection endpoints
  - name: Store - Cart
    description: Shopping cart management
  - name: Admin - Products
    description: Admin product management
  - name: Admin - Orders
    description: Admin order management
